#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
根据指定格式为每种告警生成样例告警信息
"""

import os
import glob
import json
import re
import random

def extract_alarm_info_from_filename(filename):
    """从文件名中提取告警信息"""
    # 去掉.md扩展名
    name = filename.replace('.md', '')
    
    alarm_info = {
        'filename': filename,
        'section_number': '',
        'alarm_code': '',
        'alarm_name': '',
        'alarm_type': 'unknown'
    }
    
    # 提取章节号
    section_match = re.match(r'^(5\.\d+(?:\.\d+)*)', name)
    if section_match:
        alarm_info['section_number'] = section_match.group(1)
    
    # 判断告警类型并提取代码
    if 'ALM-' in name:
        # ALM告警
        alm_match = re.search(r'(ALM-[\w\d_.-]+)', name)
        if alm_match:
            alarm_info['alarm_code'] = alm_match.group(1)
            alarm_info['alarm_type'] = 'ALM'
            # 提取告警名称
            name_match = re.search(r'ALM-[\w\d_.-]+\s+(.+)', name)
            if name_match:
                alarm_info['alarm_name'] = name_match.group(1)
    
    elif '0x' in name:
        # 十六进制告警
        hex_match = re.search(r'(0x[0-9A-Fa-f]+)', name)
        if hex_match:
            alarm_info['alarm_code'] = hex_match.group(1)
            alarm_info['alarm_type'] = 'HEX'
            # 提取告警名称
            name_match = re.search(r'0x[0-9A-Fa-f]+\s+(.+)', name)
            if name_match:
                alarm_info['alarm_name'] = name_match.group(1)
    
    elif re.search(r'\d{6,}', name):
        # 数字告警代码
        num_match = re.search(r'(\d{6,})', name)
        if num_match:
            alarm_info['alarm_code'] = num_match.group(1)
            alarm_info['alarm_type'] = 'NUM'
            # 提取告警名称
            name_match = re.search(r'\d{6,}\s+(.+)', name)
            if name_match:
                alarm_info['alarm_name'] = name_match.group(1)
    
    else:
        # 其他类型（操作指导等）
        alarm_info['alarm_type'] = 'OTHER'
        # 提取章节号后面的内容作为名称
        name_match = re.search(r'^5\.\d+(?:\.\d+)*\s+(.+)', name)
        if name_match:
            alarm_info['alarm_name'] = name_match.group(1)
    
    return alarm_info

def generate_sample_alarm(alarm_info):
    """为告警信息生成样例告警（按指定格式）"""
    
    # 根据告警类型生成不同的样例
    if alarm_info['alarm_type'] == 'ALM':
        return generate_alm_sample(alarm_info)
    elif alarm_info['alarm_type'] == 'HEX':
        return generate_hex_sample(alarm_info)
    elif alarm_info['alarm_type'] == 'NUM':
        return generate_num_sample(alarm_info)
    else:
        return generate_other_sample(alarm_info)

def generate_alm_sample(alarm_info):
    """生成ALM告警样例"""
    alarm_levels = ["严重", "重要", "次要", "提示"]
    level = random.choice(alarm_levels)
    
    # 根据告警名称推断服务类型
    service_type = determine_service_type(alarm_info['alarm_name'])
    
    # 提取纯数字ID（去掉ALM-前缀）
    alarm_id = alarm_info['alarm_code'].replace('ALM-', '') if alarm_info['alarm_code'] else ''
    
    return {
        "主题": f"[{level}告警]\"{service_type}发生{alarm_info['alarm_name']}\"",
        "告警ID": alarm_id,
        "告警级别": level,
        "告警源": service_type,
        "来源系统": f"ServiceOM{generate_ip()}",
        "定位信息": f"区域=SH_CSVW，云服务={service_type}，节点类型=mgt",
        "附加信息": f"云服务={service_type}，服务=mgt，本端地址=({generate_ip()})，对端地址=(other_{service_type.lower()}={generate_ip()})",
        "可能原因": "未知",
        "metadata": {
            "filename": alarm_info['filename'],
            "section_number": alarm_info['section_number'],
            "alarm_type": alarm_info['alarm_type'],
            "original_alarm_code": alarm_info['alarm_code']
        }
    }

def generate_hex_sample(alarm_info):
    """生成十六进制告警样例"""
    alarm_levels = ["严重", "重要", "次要"]
    level = random.choice(alarm_levels)
    
    # 根据告警名称推断服务类型
    service_type = determine_service_type_for_hex(alarm_info['alarm_name'])
    
    return {
        "主题": f"[{level}告警]\"{service_type}发生{alarm_info['alarm_name']}\"",
        "告警ID": alarm_info['alarm_code'],
        "告警级别": level,
        "告警源": service_type,
        "来源系统": f"eBackup{generate_ip()}",
        "定位信息": f"区域=SH_CSVW，云服务={service_type}，节点类型=backup",
        "附加信息": f"云服务={service_type}，服务=backup，节点地址=({generate_ip()})，错误代码={alarm_info['alarm_code']}",
        "可能原因": "未知",
        "metadata": {
            "filename": alarm_info['filename'],
            "section_number": alarm_info['section_number'],
            "alarm_type": alarm_info['alarm_type'],
            "original_alarm_code": alarm_info['alarm_code']
        }
    }

def generate_num_sample(alarm_info):
    """生成数字告警代码样例"""
    alarm_levels = ["重要", "次要"]
    level = random.choice(alarm_levels)
    
    service_type = determine_service_type(alarm_info['alarm_name'])
    if service_type == "Unknown":
        service_type = "System"
    
    return {
        "主题": f"[{level}告警]\"{service_type}发生{alarm_info['alarm_name']}\"",
        "告警ID": alarm_info['alarm_code'],
        "告警级别": level,
        "告警源": service_type,
        "来源系统": f"SystemOM{generate_ip()}",
        "定位信息": f"区域=SH_CSVW，云服务={service_type}，节点类型=compute",
        "附加信息": f"云服务={service_type}，服务=compute，节点地址=({generate_ip()})，错误代码={alarm_info['alarm_code']}",
        "可能原因": "未知",
        "metadata": {
            "filename": alarm_info['filename'],
            "section_number": alarm_info['section_number'],
            "alarm_type": alarm_info['alarm_type'],
            "original_alarm_code": alarm_info['alarm_code']
        }
    }

def generate_other_sample(alarm_info):
    """生成其他类型样例"""
    service_type = determine_service_type(alarm_info['alarm_name'])
    if service_type == "Unknown":
        service_type = "ManageOne"
    
    return {
        "主题": f"[提示信息]\"{alarm_info['alarm_name']}\"",
        "告警ID": "",
        "告警级别": "提示",
        "告警源": service_type,
        "来源系统": f"ManageOne{generate_ip()}",
        "定位信息": f"区域=SH_CSVW，云服务={service_type}，节点类型=management",
        "附加信息": f"云服务={service_type}，服务=management，操作类型=配置",
        "可能原因": "未知",
        "metadata": {
            "filename": alarm_info['filename'],
            "section_number": alarm_info['section_number'],
            "alarm_type": alarm_info['alarm_type'],
            "original_alarm_code": alarm_info['alarm_code']
        }
    }

def determine_service_type(alarm_name):
    """根据告警名称确定服务类型"""
    if not alarm_name:
        return "Unknown"
    
    name_lower = alarm_name.lower()
    
    # ELB相关
    if any(keyword in name_lower for keyword in ["elb", "负载均衡", "load", "balance"]):
        return "ELB"
    
    # 数据库相关
    elif any(keyword in name_lower for keyword in ["数据库", "database", "db", "mysql", "postgres", "gaussdb"]):
        return "Database"
    
    # ETCD相关
    elif "etcd" in name_lower:
        return "ETCD"
    
    # 网络相关
    elif any(keyword in name_lower for keyword in ["网络", "连接", "network", "connection", "主机", "host"]):
        return "Network"
    
    # 进程相关
    elif any(keyword in name_lower for keyword in ["进程", "process", "服务", "service"]):
        return "Process"
    
    # 存储相关
    elif any(keyword in name_lower for keyword in ["存储", "storage", "磁盘", "disk", "volume"]):
        return "Storage"
    
    # 计算相关
    elif any(keyword in name_lower for keyword in ["计算", "compute", "vm", "虚拟机", "ecs"]):
        return "Compute"
    
    # 监控相关
    elif any(keyword in name_lower for keyword in ["监控", "monitor", "告警", "alarm"]):
        return "Monitor"
    
    else:
        return "System"

def determine_service_type_for_hex(alarm_name):
    """为十六进制告警确定服务类型"""
    if not alarm_name:
        return "eBackup"
    
    name_lower = alarm_name.lower()
    
    # License相关
    if any(keyword in name_lower for keyword in ["license", "许可", "授权"]):
        return "License"
    
    # 证书相关
    elif any(keyword in name_lower for keyword in ["证书", "certificate", "cert"]):
        return "Certificate"
    
    # 数据库相关
    elif any(keyword in name_lower for keyword in ["数据库", "database", "db"]):
        return "Database"
    
    # 备份相关
    elif any(keyword in name_lower for keyword in ["备份", "backup", "restore"]):
        return "Backup"
    
    # 微服务相关
    elif any(keyword in name_lower for keyword in ["微服务", "microservice", "service"]):
        return "Microservice"
    
    else:
        return "eBackup"

def generate_ip():
    """生成随机IP地址"""
    return f"10.200.{random.randint(1, 255)}.{random.randint(1, 255)}"

def main():
    """主函数"""
    directory = "optimized_batch_results"
    if not os.path.exists(directory):
        print(f"❌ 目录不存在: {directory}")
        return
    
    md_files = glob.glob(os.path.join(directory, "*.md"))
    md_files.sort()
    
    if not md_files:
        print(f"⚠️  在目录 {directory} 中未找到MD文件")
        return
    
    print(f"🔍 找到 {len(md_files)} 个MD文件")
    
    # 生成样例告警
    alarm_samples = []
    
    for file_path in md_files:
        filename = os.path.basename(file_path)
        
        # 提取告警信息
        alarm_info = extract_alarm_info_from_filename(filename)
        
        # 生成样例告警
        sample_alarm = generate_sample_alarm(alarm_info)
        
        alarm_samples.append(sample_alarm)
        
        if len(alarm_samples) % 50 == 0:
            print(f"  已生成 {len(alarm_samples)} 个样例...")
    
    # 保存到JSON文件
    output_file = "alarm_samples_formatted.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(alarm_samples, f, ensure_ascii=False, indent=2)
    
    print(f"\n🎉 生成完成！")
    print(f"📁 输出文件: {output_file}")
    print(f"📊 总样例数: {len(alarm_samples)}")
    
    # 统计各类型数量
    type_stats = {}
    for sample in alarm_samples:
        alarm_type = sample['metadata']['alarm_type']
        type_stats[alarm_type] = type_stats.get(alarm_type, 0) + 1
    
    print(f"\n📈 类型统计:")
    for alarm_type, count in type_stats.items():
        print(f"  {alarm_type}: {count} 个")
    
    # 显示前5个样例
    print(f"\n📋 样例预览（前5个）:")
    for i, sample in enumerate(alarm_samples[:5], 1):
        print(f"\n{i}. {sample['metadata']['filename']}")
        print(f"   主题: {sample['主题']}")
        print(f"   告警ID: {sample['告警ID']}")
        print(f"   告警级别: {sample['告警级别']}")
        print(f"   告警源: {sample['告警源']}")
        print(f"   来源系统: {sample['来源系统']}")

if __name__ == "__main__":
    main()
