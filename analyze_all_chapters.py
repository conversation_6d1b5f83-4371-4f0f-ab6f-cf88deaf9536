#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析文档中的所有章节类型
"""

from docx import Document
import re
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def analyze_all_chapters():
    """分析文档中的所有章节类型"""
    doc = Document("华为云Stack告警处理参考.docx")
    
    logger.info("🔍 开始分析文档中的所有章节...")
    
    # 不同类型的章节模式
    patterns = {
        'ALM告警_5位': r'^5\.\d+\.\d+\.\d+\.\d+\s+ALM-\d+',
        'ALM告警_6位': r'^5\.\d+\.\d+\.\d+\.\d+\.\d+\s+ALM-',
        '十六进制告警': r'^5\.\d+\.\d+\.\d+\.\d+\s+0x[0-9A-Fa-f]+',
        '其他告警代码': r'^5\.\d+\.\d+\.\d+\.\d+\s+[A-Z0-9_-]+\s+',
        '操作指导_5位': r'^5\.\d+\.\d+\.\d+\.\d+\s+[^ALM][^0x]',
        '操作指导_6位': r'^5\.\d+\.\d+\.\d+\.\d+\.\d+\s+[^ALM][^0x]',
        '其他章节': r'^5\.\d+\.\d+\.\d+\.\d+',
    }
    
    chapter_stats = {}
    all_chapters = []
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        # 检查是否是5.x开头的章节
        if re.match(r'^5\.', text):
            chapter_type = 'unknown'
            
            # 按优先级检查模式
            for pattern_name, pattern in patterns.items():
                if re.match(pattern, text):
                    chapter_type = pattern_name
                    break
            
            # 记录章节
            chapter_info = {
                'index': i,
                'text': text,
                'type': chapter_type,
                'style': paragraph.style.name
            }
            
            all_chapters.append(chapter_info)
            
            # 统计
            if chapter_type not in chapter_stats:
                chapter_stats[chapter_type] = []
            chapter_stats[chapter_type].append(chapter_info)
        
        # 如果遇到6.x章节，停止搜索
        if text.startswith('6.'):
            logger.info(f"遇到6.x章节，停止搜索: {text[:50]}...")
            break
    
    # 输出统计结果
    logger.info(f"\n📊 章节类型统计:")
    logger.info(f"总章节数: {len(all_chapters)}")
    
    for chapter_type, chapters in chapter_stats.items():
        logger.info(f"\n{chapter_type}: {len(chapters)} 个")
        
        # 显示前5个示例
        for i, chapter in enumerate(chapters[:5]):
            logger.info(f"  {i+1}. {chapter['text'][:80]}...")
        
        if len(chapters) > 5:
            logger.info(f"  ... 还有 {len(chapters) - 5} 个")
    
    # 详细分析特殊情况
    logger.info(f"\n🔍 详细分析特殊章节:")
    
    # 分析十六进制告警
    hex_alarms = chapter_stats.get('十六进制告警', [])
    if hex_alarms:
        logger.info(f"\n十六进制告警章节 ({len(hex_alarms)} 个):")
        for chapter in hex_alarms[:10]:
            logger.info(f"  {chapter['text']}")
    
    # 分析6位数章节号
    six_digit_alm = chapter_stats.get('ALM告警_6位', [])
    if six_digit_alm:
        logger.info(f"\n6位数ALM告警章节 ({len(six_digit_alm)} 个):")
        for chapter in six_digit_alm[:10]:
            logger.info(f"  {chapter['text']}")
    
    # 分析操作指导章节
    operation_5 = chapter_stats.get('操作指导_5位', [])
    operation_6 = chapter_stats.get('操作指导_6位', [])
    
    if operation_5 or operation_6:
        total_operations = len(operation_5) + len(operation_6)
        logger.info(f"\n操作指导章节 ({total_operations} 个):")
        
        # 显示5位数操作指导
        for chapter in operation_5[:5]:
            logger.info(f"  5位: {chapter['text']}")
        
        # 显示6位数操作指导
        for chapter in operation_6[:5]:
            logger.info(f"  6位: {chapter['text']}")
    
    return all_chapters, chapter_stats


def find_specific_examples():
    """查找您提到的具体示例"""
    doc = Document("华为云Stack告警处理参考.docx")
    
    logger.info(f"\n🎯 查找具体示例:")
    
    examples = [
        "0x10E01C000B",
        "servicemonitor_agent_heartbeat",
        "修改配置文件中对接其他组件或服务的帐户密码"
    ]
    
    found_examples = []
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        for example in examples:
            if example in text:
                found_examples.append({
                    'index': i,
                    'text': text,
                    'example': example,
                    'style': paragraph.style.name
                })
                logger.info(f"找到示例 '{example}':")
                logger.info(f"  第{i}行: {text}")
                logger.info(f"  样式: {paragraph.style.name}")
    
    return found_examples


if __name__ == "__main__":
    # 分析所有章节
    all_chapters, chapter_stats = analyze_all_chapters()
    
    # 查找具体示例
    examples = find_specific_examples()
    
    logger.info(f"\n📋 总结:")
    logger.info(f"找到 {len(all_chapters)} 个5.x章节")
    logger.info(f"找到 {len(examples)} 个具体示例")
