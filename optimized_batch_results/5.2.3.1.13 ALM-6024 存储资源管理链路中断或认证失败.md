# 5.2.3.1.13 ALM-6024 存储资源管理链路中断或认证失败

##### 告警解释
OpenStack周期（默认为5分钟）检测主机的存储资源管理链路状态，当检测到主机和存储设备之间有管理链路故障时，系统产生此告警。
OpenStack每两个周期（默认即为10分钟）进行一次存储管理用户名和密码验证，当检测到后端存储用户名密码不正确时系统产生此告警。
本告警仅支持华为磁阵。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6024 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：存储链路异常的主机ID<br>对象：产生告警的具体对象 |
| 附加信息 | 主机ID：存储链路异常的主机ID<br>主机名：存储链路异常的主机名 |
##### 对系统的影响
当存储资源的管理链路出现故障时，会引起涉及该存储资源的部分业务操作中断。当存储资源的管理用户名和密码配置错误时，该存储资源将无法使用。当磁阵存储密码过期时，会在迁移虚拟机时导致虚拟机故障且会造成业务中断。
##### 可能原因
- 网络异常。
- SAN故障。
- 存储管理用户名或密码配置错误。
- 磁阵会话数达到上限。
- 对接帐户被锁。
##### 当OpenStack对接后端存储的多个存储池，且对接用户相同，若对接一个存储池时密码错误的次数达到上限（默认3次），导致该帐户被锁，则使用此帐户对接的所有存储池都会上报此告警。此时，仅需定位出现对接密码错误的存储池，并输入正确密码重新对接，则相关告警会自动清除，具体方法请参见本告警处理步骤。
##### 处理步骤
##### 1. 根据告警原因，选择对应的处理步骤。
##### - 如果定位信息中告警对象是设备管理IP，表示存储资源管理链路中断，排查一下可能原因：
- 存储资源管理IP中断，请参考2处理。
- SAN故障，请参考12处理。
##### - 如果定位信息中告警对象是后端存储名称，表示后端存储连接认证失败，排查一下可能原因：
存储管理用户名或密码配置错误或密码过期，请参考11处理。
- 如果定位信息未上报上述信息，可能是磁阵会话数达到上限，请参考14处理。
- 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
- 在“概要”界面，根据告警附加信息中的“主机ID”或“主机名”，在“管理IP地址”一栏获得主机的管理IP地址。
- 使用PuTTY，通过主机的管理IP地址，登录告警所在主机。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
- 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
- 执行以下命令，防止系统超时退出。
TMOUT=0
- 执行以下命令，导入环境变量。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
- 输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
默认密码为：*****。
- 在告警“对象”字段中获取故障的存储资源管理IP，执行如下命令，检查故障存储资源的管理IP能否ping通。
- 当故障存储资源的管理IP地址为IPv4地址时，命令如下：
ping 故障存储资源的管理IP
- 当故障存储资源的管理IP地址为IPv6地址时，命令如下：
ping6 故障存储资源的管理IP
- 如果对接华为V3及以上系列存储，则需通过curl命令检查REST URL是否正常。
- 当故障存储资源的管理IP地址为IPv4地址时，命令如下：
curl -i -k https://故障存储资源的管理IP地址:8088/deviceManager/rest
- 当故障存储资源的管理IP地址为IPv6地址时，命令如下：
curl -g -i -k https://[故障存储资源的管理IP地址]:8088/deviceManager/rest
- 通过curl命令检查REST URL是否正常时，如果无返回信息，或者返回信息为http消息头，且错误码为503/404之类，则代表到存储资源的链路不通。否则，返回html格式的信息，代表到存储资源的链路正常。
- 是，执行11。
- 否，执行10。
- 联系运维人员恢复网络。网络恢复之后，等待4分钟～5分钟，告警是否恢复。
- 是，处理完毕。
- 否，执行11。
- 如果登录磁阵密码做过变更，请参照帐户一览表“更新FusionSphere OpenStack对接存储设备的密码”进行处理。
- 告警恢复，结束操作。
- 告警未恢复，执行12。
- 参考SAN维护手册，检查SAN状态是否异常，可通过https:/support.huawei.com/enterprise/查找获取对应型号存储设备的维护手册。
- 是，执行13。
- 否，执行15。
- 联系运维人员恢复SAN。SAN恢复之后，告警是否恢复。
- 是，处理完毕。
- 否，执行15。
- 根据9中获取故障的存储资源管理IP，使用PuTTY工具以admin帐户登录磁阵。
- 如果登录失败，并提示系统已达最大连接数拒绝连接，则可确认磁阵会话数已达到上限，执行15。
- 如果登录成功，执行如下操作。
- 执行如下命令，查询当前系统最大连接数（默认为32）。
show user_session number
- 执行如下命令，查询已经建立连接的用户。
change user_mode current_mode user_mode=developer
minisystem
om_show_omm_info.sh -q
- 确认已经建立连接的用户数是否接近当前系统最大连接数，
- 是，请与客户确认修改磁阵会话的最大连接数，可执行以下命令修改磁阵的最大连接数为256。
change user_session number max_number=256
- 否，执行15。
- 请联系技术支持工程师协助解决。
##### 参考信息
无。