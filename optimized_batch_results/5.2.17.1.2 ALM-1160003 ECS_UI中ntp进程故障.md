# ********.2 ALM-1160003 ECS_UI中ntp进程故障

告警解释
告警节点的ntp进程异常时触发该告警。
告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1160003 | 重要 | 是 |
告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源设备名称 | 产生告警信息的设备名称。 |
| 监控系统名称 | 对接系统的类型。 |
| 来源系统 | 对接系统的名称。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 定位信息 | 产生告警信息的定位信息。 |
| 最后发生时间 | 此条告警信息最近一次发生时间。 |
| 附加信息 | 告警附加信息。 |
对系统的影响
时间不同步，导致某些功能不可用。
可能原因
网络故障、节点ntp进程异常等，节点到ntp服务器网络不可达等。
处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 单击“登录”，在界面上方导航栏选择“集中告警 > 当前告警”，在告警的定位信息中获取进程异常的节点IP。
3. 使用“PuTTY”，登录异常节点IP对应的CONSOLE01或CONSOLE02虚拟机节点。
默认帐号：ecm，默认密码：*****。
4. 执行以下命令切换到root用户。
sudo su - root
5. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
6. 执行以下命令检查网络是否正常。
ping ntp服务器IP
- 是，执行7。
- 否，请联系技术支持工程师协助解决。
7. 执行以下命令查看ntp进程是否存在。
ps -ef |grep ntp
- 是，执行8。
- 否，执行命令以下命令启动进程。
service ntp restart
8. 执行以下命令终止ntp进程，系统随后会自动启动进程。
kill -9 进程号
如果长时间未启动进程，请执行以下命令启动进程。
service ntp restart
9. 等待10分钟，检查告警是否已经自动消除。
- 是，执行10。
- 否，请联系技术支持工程师协助解决。
10. 重复3，执行date命令检查告警节点与ntp服务器时间是否同步。
- 是，告警处理完毕。
- 否，执行以下命令从ntp服务器同步时间。
service ntpd start
参考信息
无。
< 上一节