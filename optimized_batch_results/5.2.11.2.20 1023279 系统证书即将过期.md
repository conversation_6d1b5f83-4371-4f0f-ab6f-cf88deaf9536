# 5.2.11.2.20 1023279 系统证书即将过期

5.2.11.2.20 1023279 系统证书即将过期
告警解释
系统证书文件即将过期。
告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1023279 | 重要->紧急 | 是 |
- 证书使用期限小于7天，告警级别为紧急。
- 证书使用期限小于30天，告警级别为重要。
告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 证书全路径 | 即将过期的证书全路径。 |
| 节点IP | 产生告警的节点IP。 |
| 过期时间 | 证书过期时间。 |
对系统的影响
如果系统证书过期，证书将不受信任，可能影响系统功能。
可能原因
证书距离过期时间小于阈值。
处理步骤
- 可能原因：证书距离过期时间小于阈值。
- 查看告警定位信息中的“证书全路径”。
- 根据证书全路径，重新导入对应的新证书。重新导入证书的操作请参见通过ManageOne界面方式单个或批量更换证书更新CSBS_VBS部件的“CSBS_VBS-internal”证书。
- 一天后检查告警是否恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
参考信息
无。