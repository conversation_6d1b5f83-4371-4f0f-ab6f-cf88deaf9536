# 5.2.4.1.29 ALM-0001000400010001 【自定义监控】单板CPU利用率阈值

告警解释
当性能监控检测到单板的单板CPU利用率超过管理员设置的阈值时，产生此通知信息。
阈值需要在“监控中心 > 监控配置 > 性能阈值维护”中手工设置。
告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0001000400010001 | 紧急/重要/次要/提示 | 业务质量告警 |
性能监控阈值通知共有四个级别，分别是：紧急、重要、次要和提示。四个级别的通知处理方式一致。
告警参数
| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 单板 | 单板名称 | 单板的名称。 |
| 单板 | 网络设备 | 网络设备名称。 |
| 单板 | 父资源名称 | 父资源名称。 |
| 单板 | 单板描述 | 单板的描述信息。 |
对系统的影响
可能导致系统功能不可用。
可能原因
阈值条件设置不合理。
处理步骤
1. 检查监控指标的阈值设置。
- 在主菜单中选择“监控中心 > 监控配置 > 性能阈值维护”。
- 在“性能阈值维护”页面，找到对应监控指标的阈值。
- 单击对应监控指标的，查看阈值条件设置是否合理。
- 是：执行3。
- 否：执行2。
2. 修改指标阈值策略。
- 根据实际指标数据修改阈值。
- 等待5分钟，在主菜单选择“集中告警 > 当前告警”，检查通知信息是否清除。
- 是：处理完毕。
- 否：执行3。
3. 业务正常情况下，联系业务运维人员进行扩容、迁移业务，其他情况请联系技术工程师协助解决。
告警清除
修复后，系统会自动清除此通知信息，无需手工清除。
参考信息
无