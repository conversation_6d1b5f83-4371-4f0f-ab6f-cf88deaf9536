# ********.64 0x10E01C0010 GaussDB服务异常

********.64 0x10E01C0010 GaussDB服务异常
告警解释
在HA节点（IP：[Node_Name]）上GaussDB服务异常。
告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C0010 | 次要 | 否 |
告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Node_Name | HA节点IP地址。 |
对系统的影响
可能造成业务中断。
可能原因
- 数据库同步状态异常。
- 有残留的gaussdb进程。
处理步骤
- 可能原因1：数据库同步状态异常。
- 在当前告警界面查看ID为0x10E01C000F（浮动IP服务异常）的告警是否存在。
- 是，执行1.b。
- 否，执行1.d。
- 请参考0x10E01C000F（浮动IP服务异常）这条告警的处理步骤，先清除0x10E01C000F这条告警。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，执行1.d。
- 在当前告警界面查看ID为0x10E01C0011（ibase服务异常）的告警是否存在。
- 是，执行1.e。
- 否，执行2。
- 请参考0x10E01C0011（ibase服务异常）这条告警的处理步骤，先清除0x10E01C0011这条告警。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，执行2。
- 可能原因2：有残留的gaussdb进程。
- 使用PuTTY，通过告警上报的服务器的IP地址登录eBackup服务器。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 执行“ps -ef|grep gaussdb”命令查看是否存在gaussdb进程。
- 是，执行2.d。
- 否，请联系技术支持工程师协助解决。
- 执行“killall -9 gaussdb”命令停掉残留进程。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
参考信息
无