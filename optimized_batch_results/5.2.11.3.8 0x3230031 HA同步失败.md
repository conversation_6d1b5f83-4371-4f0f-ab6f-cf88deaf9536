# ********.8 0x3230031 HA同步失败

********.8 0x3230031 HA同步失败
告警解释
主节点向备节点同步文件失败。
告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230031 | 重要 | 是 |
告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 主节点名称 | 主节点的名称 |
| 主节点IP地址 | 主节点的IP地址 |
| 备节点名称 | 备节点的名称 |
| 备节点IP地址 | 备节点的IP地址 |
对系统的影响
主备两端信息不一致，切换后配置文件可能异常，导致HA无法正常启动。
可能原因
- 备节点处于异常状态（如复位、下电等）。
- 文件同步链路故障。
- 磁盘空间不足。
处理步骤
1. 使用PuTTY，以告警详细信息当中的主节点IP地址登录主节点后台操作系统。
默认帐号：DRManager，默认密码：*****。
2. 执行su - root命令切换到root帐号。
root帐号的默认密码为*****。
3. 检查主节点是否可以ping通备节点。如果是IPv4，执行ping 备节点IP地址命令，如果是IPv6，执行ping6 备节点IP地址命令，检查是否可以ping通。备节点IP地址通过告警详细信息中的备节点IP地址获取。
- 是，转到4。
- 否，请修复主节点和备节点间的网络连接，修复成功后，等待5分钟，检查告警是否自动清除，如果已清除，流程结束，否则请转4。
4. 使用PuTTY，以主节点和备节点字段对应的IP地址登录主节点和备节点。
默认帐号：DRManager，默认密码：*****。
5. 执行su - root命令切换到root帐号。
root帐号的默认密码为*****。
6. 执行cd /opt/BCManager/Runtime/bin;sh showSystemStatus.sh查看主节点和备节点上的服务状态。
如果命令回显中ResStatus列中的值存在非Normal或Active_normal，表示有服务处于未运行状态。请转7。
如果命令回显中ResStatus的值为Normal或Active_normal，表示服务都处于运行状态，请联系技术工程师协助解决。
7. 等待10分钟后，检查服务是否都已经处于运行状态。
- 是，转到8。
- 否，请联系技术工程师协助解决。
8. 在备节点服务器上执行cd /opt/BCManager/Runtime/ha/module/hasync/plugin/conf命令，切换到conf目录，执行df -h命令依次检查conf目录下hasync_mod.xml文件中所有目录是否存在存储空间利用率高于95%的情况。
- 是，请增加备节点目录的磁盘空间，增加磁盘空间后等待10分钟，如果告警已清除，流程结束。如果告警未清除，请转9。
- 否，请转9。
9. 请联系技术支持工程师协助处理。
参考信息
无。