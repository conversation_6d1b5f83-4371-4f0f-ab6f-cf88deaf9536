# 5.2.3.1.99 ALM-1126001 裸金属服务器管理状态不可用

##### 告警解释
当裸金属服务器进行发放、扩容、回收或者格式化时，如果管理状态进入deploy failed、inspect failed、clean failed或者error时，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1126001 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 裸金属服务器节点ID：产生告警的裸金属服务器节点ID |
| 附加信息 | 主机IP：产生告警的主机IP<br>裸金属服务器BMC地址：产生告警的裸金属服务器BMC IP地址<br>当前管理状态：产生告警的裸金属服务器节点的管理状态 |
##### 对系统的影响
此告警产生时，表示该裸金属服务器在发放、扩容、回收或者格式化过程中发生故障，租户将不能再对该裸金属服务器进行重新发放。
##### 可能原因
裸金属服务器在发放、扩容、回收或者格式化过程中，因为网络问题或者DB异常导致其管理状态进入DEPLOY FAILED、INSPECT FAILED、CLEAN FAILED或者ERROR状态。
##### 处理步骤
1. 使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：
- Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。
- Region Type II和Type III场景：ExternalOM-Reverse-Proxy。
2. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
3. 执行以下命令，防止系统超时退出。
TMOUT=0
4. 执行以下命令，导入环境变量。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
5. 输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
默认密码为：*****。
6. 执行 ironic node-show node_uuid | grep provision_state | grep -v target_provision_state 查看该裸金属服务器的“provision_state”状态。
##### “node_uuid”值为告警参数中的“裸金属服务器节点ID”。
- 如果是clean failed，执行7。
- 如果是deploy failed或者error，执行8。
- 如果是inspect failed，执行9。
7. 执行命令ironic node-show node_uuid | grep last_error，在回显结果中查看“last_error”属性的内容以确定clean failed的原因。
##### “node_uuid”值为告警参数中的“裸金属服务器节点ID”。
例如，以下回显表示sdb硬盘只读，需去除硬盘的写保护。
/dev/sdb：failed to open for writing: Read-only file system
- 根据回显内容排查定位原因，排查完后依次执行：
ironic node-set-provision-state node_uuid manage
ironic node-set-provision-state node_uuid provide
重新执行初始化操作。然后等待裸金属服务器状态变成available。告警恢复即处理完毕，否则执行10。
- 如果回显的内容不能帮助定位原因排查，执行10。
8. 执行命令ironic node-show node_uuid | grep last_error，在回显结果中查看“last_error”属性的内容以确定deploy failed或error的原因。
##### “node_uuid”值为告警参数中的“裸金属服务器节点ID”。
- 根据回显内容排查定位原因，排查完后执行命令ironic node-set-provision-state node_uuid deleted, delete过程会重新执行初始化操作，然后等待裸金属服务器状态变成available。告警恢复即处理完毕，否则执行10。
- 如果回显的内容不能帮助定位原因排查，执行10。
9. 执行命令ironic node-show node_uuid | grep last_error，在回显结果中查看“last_error”属性的内容以确定inspect failed的原因。
##### “node_uuid”值为告警参数中的“裸金属服务器节点ID”。
- 根据回显内容排查定位原因，排查完后执行命令ironic node-set-provision-state node_uuid inspect, inspect过程会重新执行扩容操作，然后等待裸金属服务器状态变成manageable。告警恢复即处理完毕，否则执行10。
- 如果回显的内容不能帮助定位原因排查，执行10。
10. 请联系技术支持工程师协助解决。
##### 参考信息
无。