# 5.2.4.3.2 ALM-100551 LVS中断服务告警

##### 告警解释
MORCLVSService服务会定期检测LVS的运行状态，如果检测到LVS没有启动并重新启动失败时，产生该告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 100551 | 紧急 | 处理错误告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 发生告警的云服务名称。 |
| 服务 | 发生告警的服务名称。 |
| 节点名称 | 发生告警的服务名称 |
| IP地址 | 发生告警的服务所在服务器的IP地址。 |
##### 对系统的影响
告警发生之后，依赖LVS的SNMP告警无法上报。
##### 可能原因
- 系统错误或系统文件损坏。
- MORCLVSService服务部署的节点有其他的服务在使用keepalived。
keepalived用于倒换LVS服务部署节点的主备状态。
##### 处理步骤
1. 查看MORCLVSService服务部署节点的“/var/log/messages”日志中回显信息是否出现关键字“error”。
##### - 使用PuTTY，通过regionAlias-ManageOne-Service01的IP地址登录到service01节点。请在参考信息中查询节点对应的IP
默认帐号：sopuser，默认密码：*****
- 执行如下命令，切换到root帐号。
sudo su - root
默认密码：*****
- 执行如下命令，查看回显信息中是否出现关键字“error”。
cat /var/log/messages | grep "*error*"
- 是，请联系技术支持工程师协助解决。
- 否，请执行2。
2. 执行如下命令检查该节点是否有其他服务运行keepalived。
ps -ef | grep keepalive | grep -v "RCLVSService/lvs_install" | grep -v "grep" | awk '{print $2}'
如果有回显产生，则表明有其他服务运行keepalived。
- 是，请联系技术支持工程师协助解决，将MORCLVSService服务与其他服务分不同节点部署，MORCLVSService服务部署的节点将不再有其他的服务使用keepalived。
- 否，请联系技术支持工程师协助解决，重新部署MORCLVSService服务。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
以下介绍查找节点对应的管理IP地址的方法。
3. 启动浏览器，在地址栏中输入https://部署面的客户端登录IP地址:31945，按“Enter”。
4. 输入用户名、密码，单击“登录”。
5. 在主菜单中，选择“产品 > 系统监控”，在“系统监控”页面左上方，光标移至并选择对应的产品。然后进入“系统监控”页面的“节点”页签。
6. 在“节点名称”列查找待查询管理IP地址的节点。
7. 单击节点名称，在节点详情页面上方的IP地址即为该节点的管理IP地址。
< 上一节
******* 容量管理
*******.1 ALM-CloudCapacityMgmt_Base_1001 vCPU分配率超过阈值告警
##### 告警解释
当某个位置（区域、资源池、AZ、集群）的vCPU分配率等于或超过所设置的某个阈值时，上报此类告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| CloudCapacityMgmt_Base_1001 | 提示/次要/重要/紧急 | 业务质量告警 |
各级别告警的默认阈值如下所示。
| 容量指标 | 紧急阈值(%) |
| --- | --- |
| vCPU分配率 | 90 |
提示阈值、次要阈值和重要阈值系统默认未设置，用户可根据自身情况设置。
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 区域 | 上报的告警对应的位置信息。 |
| 资源池 | 上报的告警对应的资源池名称。 |
| 可用分区 | 上报的告警对应的可用分区名称。 |
| 集群 | 上报的告警对应的集群名称。 |
| 阈值级别 | 当前上报告警所属的阈值级别。 |
| 阈值门限 | 用户设定上报告警的门限值。 |
| 当前值 | 本条告警产生时的阈值。 |
##### 对系统的影响
当上报此类阈值告警时，说明对应的资源不足，可能会影响ECS云服务业务发放。
##### 可能原因
- vCPU分配率告警阈值设置太低。
- 当前某个位置对应的vCPU资源不足。
##### 处理步骤
8. 检查“vCPU分配率”的告警阈值。
- 使用浏览器，登录ManageOne运维面。
登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
默认帐号：admin，默认密码：*****
- 在主菜单中选择“监控中心 > 监控配置”。
- 在左侧导航树中选择“容量阈值维护”，进入“容量阈值维护”页面。
##### - 参考告警属性下的各级别告警的默认阈值，检查vCPU分配率的告警阈值是否合理。
- 是：则剩余可用量过小，执行3。
- 否：执行2。
9. 重新设置“vCPU分配率”的告警阈值。
- 单击“操作”列的“修改”，修改告警阈值。
- 等待10分钟，在系统主菜单选择“集中告警 > 当前告警”，检查告警是否清除。
- 是：操作结束。
- 否：当告警级别降低时，该告警还存在，但是会同步在系统上更新“最近发生时间”的信息，执行3。
10. 联系管理员参考《华为云Stack 6.5.1 扩容指南》中的“扩容计算资源池”章节对vCPU资源进行扩容。
告警清除
此告警修复后，系统会自动清除告警，无需手工清除。
##### 参考信息
无。
*******.2 ALM-CloudCapacityMgmt_Base_1002 vMemory分配率超过阈值告警
##### 告警解释
当某个位置（区域、资源池、AZ、集群）的vMemory分配率等于或超过所设置的某个阈值时，上报此类告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| CloudCapacityMgmt_Base_1002 | 提示/次要/重要/紧急 | 业务质量告警 |
各级别告警的默认阈值如下所示。
| 容量指标 | 紧急阈值(%) |
| --- | --- |
| vMemory分配率 | 90 |
提示阈值、次要阈值和重要阈值系统默认未设置，用户可根据自身情况设置。
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 区域 | 上报的告警对应的位置信息。 |
| 资源池 | 上报的告警对应的资源池名称。 |
| 可用分区 | 上报的告警对应的可用分区名称。 |
| 集群 | 上报的告警对应的集群名称。 |
| 阈值级别 | 当前上报告警所属的阈值级别。 |
| 阈值门限 | 用户设定上报告警的门限值。 |
| 当前值 | 本条告警产生时的阈值。 |
##### 对系统的影响
当上报此类阈值告警时，说明vMemory的资源不足，可能会影响ECS云服务业务发放。
##### 可能原因
- vMemory分配率告警阈值设置太低。
- 当前某个位置对应的vMemory资源不足。
##### 处理步骤
11. 检查“vMemory分配率”的告警阈值。
- 使用浏览器，登录ManageOne运维面。
登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
默认帐号：admin，默认密码：*****
- 在主菜单中选择“监控中心 > 监控配置”。
- 在左侧导航树中选择“容量阈值维护”，进入“容量阈值维护”页面。
##### - 参考告警属性下的各级别告警的默认阈值，检查“vMemory分配率”的告警阈值是否合理。
- 是：则剩余可用量过小，执行3。
- 否：执行2。
12. 重新设置“vMemory分配率”的告警阈值。
- 单击“操作”列的“修改”，修改告警阈值。
- 等待10分钟，在系统主菜单选择“集中告警 > 当前告警”，检查告警是否清除。
- 是：操作结束。
- 否：当告警级别降低时，该告警还存在，但是会同步在系统上更新“最近发生时间”的信息，执行3。
13. 联系管理员参考《华为云Stack 6.5.1 扩容指南》中的“扩容计算资源池”章节对vMemory资源进行扩容。
告警清除
此告警修复后，系统会自动清除告警，无需手工清除。
##### 参考信息
无。
*******.3 ALM-CloudCapacityMgmt_Base_1003 存储使用率超过阈值告警
##### 告警解释
当某个位置（区域、资源池、AZ、集群）的存储使用率等于或超过所设置的某个阈值时，上报此类告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| CloudCapacityMgmt_Base_1003 | 提示/次要/重要/紧急 | 业务质量告警 |
各级别告警的默认阈值如下所示。
| 容量指标 | 紧急阈值(%) |
| --- | --- |
| 存储使用率 | 90 |
提示阈值、次要阈值和重要阈值系统默认未设置，用户可根据自身情况设置。
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 区域 | 上报的告警对应的位置信息。 |
| 资源池 | 上报的告警对应的资源池名称。 |
| 可用分区 | 上报的告警对应的可用分区名称。 |
| 存储类型 | 上报的告警对应的存储类型。 |
| 存储池 | 上报的告警对应的存储池名称。 |
| 阈值级别 | 当前上报告警所属的阈值级别。 |
| 阈值门限 | 用户设定上报告警的门限值。 |
| 当前值 | 本条告警产生时的阈值。 |
##### 对系统的影响
当上报此类阈值告警时，说明存储池资源不足，可能会影响EVS云服务业务发放。
##### 可能原因
- 存储池告警阈值设置太低。
- 当前某个位置对应的存储池资源不足。
##### 处理步骤
14. 检查“存储使用率”的告警阈值。
- 使用浏览器，登录ManageOne运维面。
登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
默认帐号：admin，默认密码：*****
- 在主菜单中选择“监控中心 > 监控配置”。
- 在左侧导航树中选择“容量阈值维护”，进入“容量阈值维护”页面。
##### - 参考告警属性下的各级别告警的默认阈值，检查“存储使用率”的告警阈值是否合理。
- 是：则剩余可用量过小，执行3。
- 否：执行2。
15. 重新设置“存储使用率”的告警阈值。
- 单击“操作”列的“修改”，修改告警阈值。
- 等待10分钟，在系统主菜单选择“集中告警 > 当前告警”，检查告警是否清除。
- 是：操作结束。
- 否：当告警级别降低时，该告警还存在，但是会同步在系统上更新“最近发生时间”的信息，执行3。
16. 联系管理员参考《华为云Stack 6.5.1 扩容指南》中的“扩容业务存储资源”章节对存储池资源进行扩容。
告警清除
此告警修复后，系统会自动清除告警，无需手工清除。
##### 参考信息
无。
*******.4 ALM-CloudCapacityMgmt_Base_1004 存储分配率超过阈值告警
##### 告警解释
当某个位置（区域、资源池、AZ、集群）的存储分配率等于或超过所设置的某个阈值时，上报此类告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| CloudCapacityMgmt_Base_1004 | 提示/次要/重要/紧急 | 业务质量告警 |
各级别告警的默认阈值如下所示。
| 容量指标 | 紧急阈值(%) |
| --- | --- |
| 存储池分配率 | 90 |
提示阈值、次要阈值和重要阈值系统默认未设置，用户可根据自身情况设置。
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 区域 | 上报的告警对应的位置信息。 |
| 资源池 | 上报的告警对应的资源池名称。 |
| 可用分区 | 上报的告警对应的可用分区名称。 |
| 存储类型 | 上报的告警对应的存储类型。 |
| 存储池 | 上报的告警对应的存储池名称。 |
| 阈值级别 | 当前上报告警所属的阈值级别。 |
| 阈值门限 | 用户设定上报告警的门限值。 |
| 当前值 | 本条告警产生时的阈值。 |
##### 对系统的影响
当上报此类阈值告警时，说明存储池资源不足，可能会影响EVS云服务业务发放。
##### 可能原因
- 存储池告警阈值设置太低。
- 当前某个位置对应的存储池资源不足。
##### 处理步骤
17. 检查“存储分配率”的告警阈值。
- 使用浏览器，登录ManageOne运维面。
登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
默认帐号：admin，默认密码：*****
- 在主菜单中选择“监控中心 > 监控配置”。
- 在左侧导航树中选择“容量阈值维护”，进入“容量阈值维护”页面。
##### - 参考告警属性下的各级别告警的默认阈值，检查“存储分配率”的告警阈值是否合理。
- 是：则剩余可用量过小，执行3。
- 否：执行2。
18. 重新设置“存储分配率”的告警阈值。
- 单击“操作”列的“修改”，修改告警阈值。
- 等待10分钟，在系统主菜单选择“集中告警 > 当前告警”，检查告警是否清除。
- 是：操作结束。
- 否：当告警级别降低时，该告警还存在，但是会同步在系统上更新“最近发生时间”的信息，执行3。
19. 联系管理员参考《华为云Stack 6.5.1 扩容指南》中的“扩容业务存储资源”章节对存储池资源进行扩容。
告警清除
此告警修复后，系统会自动清除告警，无需手工清除。
##### 参考信息
无。
*******.5 ALM-CloudCapacityMgmt_Base_1005 弹性IP使用率超过阈值告警
##### 告警解释
当某个位置（区域、资源池、AZ、集群）的弹性IP使用率等于或超过所设置的某个阈值时，上报弹性IP使用率超过阈值告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| CloudCapacityMgmt_Base_1005 | 提示/次要/重要/紧急 | 业务质量告警 |
各级别告警的默认阈值如下所示。
| 容量指标 | 紧急阈值(%) |
| --- | --- |
| 弹性IP使用率 | 90 |
提示阈值、次要阈值和重要阈值系统默认未设置，用户可根据自身情况设置。
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 区域 | 上报的告警对应的位置信息。 |
| 资源池 | 上报的告警对应的资源池名称。 |
| 用途 | 上报此告警对应的弹性IP的用途。 |
| 阈值级别 | 当前上报告警所属的阈值级别。 |
| 阈值门限 | 用户设定上报告警的门限值。 |
| 当前值 | 本条告警产生时的阈值。 |
##### 对系统的影响
当上报此类阈值告警时，说明对应的资源不足，可能会影响弹性IP业务发放。
##### 可能原因
- 弹性IP使用率告警阈值设置太低。
- 当前某个位置对应的弹性IP资源不足。
##### 处理步骤
20. 检查“弹性IP使用率”的告警阈值。
- 使用浏览器，登录ManageOne运维面。
登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
默认帐号：admin，默认密码：*****
- 在主菜单中选择“监控中心 > 监控配置”。
- 在左侧导航树中选择“容量阈值维护”，进入“容量阈值维护”页面。
##### - 参考告警属性下的各级别告警的默认阈值，检查“弹性IP使用率”的告警阈值是否合理。
- 是：则剩余可用量过小，执行3。
- 否：执行2。
21. 重新设置“弹性IP使用率”的告警阈值。
- 单击“操作”列的“修改”，修改告警阈值。
- 等待10分钟，在系统主菜单选择“集中告警 > 当前告警”，检查告警是否清除。
- 是：操作结束。
- 否：当告警级别降低时，该告警还存在，但是会同步在系统上更新“最近发生时间”的信息，执行3。
22. 联系管理员参考《华为云Stack 6.5.1 扩容指南》中的“扩容网络节点及网络服务”章节对弹性IP资源进行扩容。
告警清除
此告警修复后，系统会自动清除告警，无需手工清除。
##### 参考信息
无。
*******.6 ALM-CloudCapacityMgmt_Base_1006 数据存储使用率超过阈值告警
##### 告警解释
当某个位置（区域、资源池、集群）的数据存储使用率等于或超过所设置的某个阈值时，上报数据存储使用率超过阈值告警。
数据存储使用率超过阈值告警仅在ManageOne 6.5.1.SPC100及后续版本上报。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| CloudCapacityMgmt_Base_1006 | 提示/次要/重要/紧急 | 业务质量告警 |
各级别告警的默认阈值如下所示。
| 容量指标 | 紧急阈值(%) |
| --- | --- |
| 数据存储使用率 | 90 |
23. 提示阈值、次要阈值和重要阈值系统默认未设置，用户可根据自身情况设置。
24. 数据存储使用率容量阈值仅针对VRM资源池上报告警。
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 区域 | 上报的告警对应的位置信息。 |
| 资源池 | 上报的告警对应的资源池名称。 |
| 数据存储类型 | 上报的告警对应的数据存储类型。 |
| 数据存储 | 上报的告警对应的数据存储名称。 |
| 阈值级别 | 当前上报告警所属的阈值级别。 |
| 阈值门限 | 用户设定上报告警的门限值。 |
| 当前值 | 本条告警产生时的阈值。 |
##### 对系统的影响
当上报此类阈值告警时，说明对应的资源不足，可能会影响数据存储业务发放。
##### 可能原因
- 数据存储使用率告警阈值设置太低。
- 当前某个位置对应的数据存储资源不足。
##### 处理步骤
25. 检查“数据存储使用率”的告警阈值。
- 使用浏览器，登录ManageOne运维面。
登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
默认帐号：admin，默认密码：*****
- 在主菜单中选择“监控中心 > 监控配置”。
- 在左侧导航树中选择“容量阈值维护”，进入“容量阈值维护”页面。
##### - 参考告警属性下的各级别告警的默认阈值，检查“数据存储使用率”的告警阈值是否合理。
- 是：则剩余可用量过小，执行3。
- 否：执行2。
26. 重新设置“数据存储使用率”的告警阈值。
- 单击“操作”列的“修改”，修改告警阈值。
- 等待10分钟，在系统主菜单选择“集中告警 > 当前告警”，检查告警是否清除。
- 是：操作结束。
- 否：当告警级别降低时，该告警还存在，但是会同步在系统上更新“最近发生时间”的信息，执行3。
27. 联系管理员参考《FusionSphere 虚拟化套件 6.5.1 产品文档》中的“添加主机”章节对数据存储资源进行扩容。
告警清除
此告警修复后，系统会自动清除告警，无需手工清除。
##### 参考信息
无。
< 上一节
5.2.4.5 告警管理