# ********.74 0x201000C90024 eBackup服务器到NTP服务器连接异常

********.74 0x201000C90024 eBackup服务器到NTP服务器连接异常
告警解释
eBackup服务器（IP:[IP_addr1]）到NTP服务器（IP/域名:[IP_addr2]）连接异常。
告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000C90024 | 重要 | 否 |
告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| IP_addr1 | eBackup服务器IP地址。 |
| IP_addr2 | NTP服务器IP地址。 |
对系统的影响
不涉及。
可能原因
eBackup服务器与NTP服务器之间的网络连接断开。
处理步骤
- 可能原因1：eBackup服务器与NTP服务器之间的网络连接断开。
- 使用PuTTY，通过告警上报的IP地址登录eBackup服务器。
默认帐户：hcp，默认密码：*****
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 如果是IPv4，执行ping ntp_server_ip -c 3 > /dev/null; echo $?命令，如果是IPv6，执行ping6 ntp_server_ip -c 3 > /dev/null; echo $?命令，查看显示结果是否为0。
- 是，执行2。
- 否，网络不通，请联系机房管理员修复网络连通性。
- 可能原因2：节点NTP服务级数大于15级。
- 使用PuTTY，通过告警上报的IP地址登录eBackup服务器。
默认帐户：hcp，默认密码：*****
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行ntpq -p命令，查看显示结果中“st”字段是否小于等于15。
- 是，eBackup服务器会自动恢复与NTP服务器对时。
- 否，请联系技术支持工程师协助解决。
参考信息
无。