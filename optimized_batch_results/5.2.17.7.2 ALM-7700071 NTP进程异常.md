# ********.2 ALM-7700071 NTP进程异常

告警解释
系统每隔1分钟检查一次NTP服务状态，如果异常，产生此告警。
告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 7700071 | 重要 | 是 |
告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 进程监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
对系统的影响
系统时间不同步，影响问题定位，需尽快处理异常。
可能原因
ntp进程异常。
处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
5. 使用“PuTTY”工具，通过4中获取的节点IP地址登录产生告警的节点。
默认帐号：untp，默认密码：*****。
6. 执行以下命令切换至“root”用户。
sudo su
按提示输入“root”用户的密码，默认密码：*****。
7. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
8. 执行以下命令启动NTP服务。
systemctl start ntpd.service
9. 执行以下命令，查看NTP服务是否正常。
systemctl status ntpd.service
当回显中有active(running)则表示NTP服务正常。
- 是，请执行10。
- 否，请联系技术支持工程师协助解决。
10. 等待10分钟，检查告警是否清除：
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
参考信息
无。
< 上一节