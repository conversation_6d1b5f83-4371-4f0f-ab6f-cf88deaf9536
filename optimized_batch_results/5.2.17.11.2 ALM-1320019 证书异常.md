# *********.2 ALM-1320019 证书异常

告警解释
自定义监控项，系统每隔五分钟检查一次CCS所使用的证书是否过期，如果过期则产生此告警。
告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1320019 | 紧急 | 是 |
告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
对系统的影响
证书过期后系统不可用，无法提供服务，需尽快处理异常。
可能原因
- 阈值为1：证书即将过期。
- 阈值为2：证书过期。
- 阈值为3：证书无效或者证书不存在。
处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
5. 使用PuTTY工具，通过4中获取的主机节点IP地址登录产生告警的节点。
默认帐号：ccs，默认密码：*****。
6. 执行以下命令，并按提示输入“root”用户的密码，默认密码：*****，切换至“root”用户。
sudo su - root
7. 执行以下命令，防止系统超时退出。
TMOUT=0
8. 执行以下命令，进入证书文件所在目录并查看文件。
cd /etc/ccs/server-cert
ll
9. 执行以下命令，停止CCS服务进程。
sh /etc/ccs/init-script/start_ccs_service.sh -A STOP -M VM
10. 在/etc/ccs/server-cert目录下，执行以下命令，修改原证书。
mv ccs_ca.crt ccs_ca.crt.bak
mv ccs_server.crt ccs_server.crt.bak
mv ccs_server.key ccs_server.key.bak
11. 使用网络传输工具（例如“WinSCP”工具），将新的证书拷贝至当前节点的/etc/ccs/server-cert目录下。证书的名字请保持跟原证书一致。
12. 执行以下命令，更新证书文件的属主为docker。
chown docker:docker /etc/ccs/server-cert/*
新替换的证书文件必须与查看的已有证书文件放在同一目录下。
13. 执行以下命令，重新启动CCS服务。
sh /etc/ccs/init-script/start_ccs_service.sh -M VM
查看日志/var/log/ccs/ccs-init/ccs_server_check.log文件，查看是否有证书未生效或失效日志信息输出。
回显如下所示，表明更换证书生效。
14. 在其他CCS服务节点上重复执行5~13。
15. 观察告警是否清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
参考信息
无。
< 上一节