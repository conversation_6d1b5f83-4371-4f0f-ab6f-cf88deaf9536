# ********.85 0x210000000D00 连接远端vpp服务器失败

********.85 0x210000000D00 连接远端vpp服务器失败
告警解释
Proxy（[ipAddr]）连接远端vpp服务器（[vppServerIP]）失败。
告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x210000000D00 | 次要 | 否 |
告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| ipAddr | 本端备份节点IP。 |
| vppServerIP | 远端vpp服务器IP。 |
对系统的影响
不涉及。
可能原因
- Proxy与远端vpp服务器之间的物理连接中断。
- 远端vpp服务器上hcp_vppd进程异常。
处理步骤
- 可能原因1：备份代理与远端vpp服务器之间的物理连接中断。
- 使用PuTTY，通过告警上报的IP地址登录Proxy节点。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 检查Proxy节点与远端vpp服务器之间的连通性。如果是IPv4，执行“ping 远端vpp服务器”，如果是IPv6，执行“ping6 远端vpp服务器”，查看是否可ping通。
- 是，执行2。
- 否，请联系机房管理员修复网络连通性，确保网络正常连接。
- 可能原因2：远端vpp服务器上hcp_vppd进程异常。
- 登录到远端vpp服务器。
- 执行“service hcp_vppd status”命令，查看界面回显是否为“ebackup_vpp_Proxy is running”。
- 是，请联系技术支持工程师协助解决。
- 否，执行2.b。
- 执行“service hcp_vppd restart”启动ebackup_vpp_Proxy进程，当回显显示“ebk_vpp_proxy start successfully”时，则表示重启成功。
参考信息
无