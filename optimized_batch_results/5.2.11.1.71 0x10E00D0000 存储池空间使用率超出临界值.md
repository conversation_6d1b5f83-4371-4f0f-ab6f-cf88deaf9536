# 5.2.11.1.71 0x10E00D0000 存储池空间使用率超出临界值

5.2.11.1.71 0x10E00D0000 存储池空间使用率超出临界值
告警解释
当前存储池（名称：[Storagepool_name]）的空间使用率已超出临界值。
告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E00D0000 | 重要 | 否 |
告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Storagepool_name | 存储池名称。 |
对系统的影响
不涉及。
可能原因
- 存储池的空间使用率已经超出临界值。
- 存储池的空间被占满。
处理步骤
- 可能原因1：存储池的空间使用率已经超出临界值。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“备份存储 > 存储池”，进入“存储池”界面，查看该存储池的“利用率”是否大于“告警阈值”。
- 是，执行1.c。
- 否，请联系技术支持工程师协助解决。
- 在“存储池”界面，选择该存储池，获取该存储池的存储单元信息。
- 请联系管理员对该存储池里的存储单元进行扩容或数据离线迁移。
- 等待3分钟左右，在当前告警界面查看该告警是否恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
参考信息
无