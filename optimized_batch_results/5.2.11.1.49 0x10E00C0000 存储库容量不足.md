# 5.2.11.1.49 0x10E00C0000 存储库容量不足

5.2.11.1.49 0x10E00C0000 存储库容量不足
告警解释
存储库（名称：[Namespace_name]）的容量不足。
告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E00C0000 | 次要 | 是 |
告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Namespace_name | 存储库的名称。 |
对系统的影响
需访问此存储库的备份及恢复任务均将失败。
可能原因
存储库的使用容量已经超出设置容量。
处理步骤
- 可能原因1：存储库的使用容量已经超出设置容量。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“备份存储 > 存储库”，进入“存储库”界面，查看该存储库的“利用率”是否大于“告警阈值”。
- 是，执行1.c。
- 否，请联系技术支持工程师协助解决。
- 在“存储库”界面，选择该存储库，获取该存储库的存储单元信息。
- 请参考《华为云Stack 6.5.1 扩容指南》中的“新增备份存储的容量”对存储单元进行扩容，或联系技术支持工程师进行数据离线迁移。
- 在当前告警界面手动清除该告警。
参考信息
无