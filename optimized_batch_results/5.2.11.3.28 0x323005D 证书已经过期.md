# 5.2.11.3.28 0x323005D 证书已经过期

5.2.11.3.28 0x323005D 证书已经过期
告警解释
证书已经过期，导致证书校验失败。
告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x323005D | 紧急 | 是 |
告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 证书已过期天数 | 证书已经过期后的天数 |
| 组件名称 | 证书的名称 |
对系统的影响
存在被攻击方仿冒的风险。
可能原因
服务证书已过期。
处理步骤
1. 完成证书的替换，若组件名称为eReplication-Portal，请参考更换灾备服务eReplication证书（eReplication-Portal），其他组件名称参考通过ManageOne界面方式单个或批量更换证书。如果告警未清除，请转2。
2. 请联系技术支持工程师协助处理。
参考信息
无。