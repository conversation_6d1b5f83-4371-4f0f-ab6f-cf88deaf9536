# ********.9 0x3230033 HA链路中断

********.9 0x3230033 HA链路中断
告警解释
主节点链路无法连接到备端。
告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230033 | 重要 | 是 |
告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 主节点名称 | 主节点名称 |
| 主节点IP地址 | 主节点IP地址 |
| 主节点端口 | 主节点端口 |
| 备节点名称 | 备节点名称 |
| 备节点IP地址 | 备节点IP地址 |
对系统的影响
同步链路、仲裁链路的可靠性降低。
可能原因
- 当前链路网络连接异常。
- 网络配置发生变更。
处理步骤
1. 使用PuTTY，通过告警详细信息中的主节点IP地址登录到主节点服务器后台操作系统。
默认帐号：DRManager，默认密码：*****。
2. 执行su - root命令切换到root帐号。
root帐号的默认密码为*****。
3. 检查主节点是否可以ping通备节点。如果是IPv4，执行ping 远端节点IP地址命令，如果是IPv6，执行ping6 远端节点IP地址命令，检查是否可以ping通。备节点IP地址通过告警详细信息中的备节点IP地址获取。
- 是，转到4。
- 否，请修复主备节点间的网络连接，修复成功后，等待5分钟，检查告警是否自动清除，如果已清除，流程结束，否则请转4。
4. 查看是否存在丢包的现象。
- 是，联系管理员提高网络性能质量，保证主节点和备节点服务器之间通信稳定。检查告警是否自动清除，如果已清除，流程结束。如果未清除，请转5。
- 否，执行5。
5. 使用PuTTY，以主节点和备节点IP分别登录到主节点和备节点服务器后台操作系统。
默认帐号：DRManager，默认密码：*****。
6. 执行su - root命令切换到root帐号。
root帐号的默认密码为*****。
7. 执行cd /opt/BCManager/Runtime/bin;sh showSystemStatus.sh命令，查看主节点和备节点上的服务状态。
如果命令回显中eReplicationServer的值为Not Normal，表示有服务处于未运行状态。请转8。
如果命令回显中eReplicationServer的值为Normal，表示服务都处于运行状态，请联系技术工程师协助解决。
8. 等待10分钟后，检查服务是否都已经处于运行状态。
- 是，检查告警是否自动清除，如果已清除，流程结束。
- 否，请联系技术工程师协助解决。
参考信息
无。