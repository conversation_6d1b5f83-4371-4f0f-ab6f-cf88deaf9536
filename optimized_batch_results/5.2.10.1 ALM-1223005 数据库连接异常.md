# 5.2.10.1 ALM-1223005 数据库连接异常

5.2.10.1 ALM-1223005 数据库连接异常
告警解释
ELB API管理节点netcluster_elb_api_vm每10秒检测后端数据库服务，如果服务显示为down，上报数据库异常，生成此告警。
告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1223005 | 次要 | 是 |
告警的级别不一定和告警定义时的级别一致，每次发送告警可根据需要动态调整告警级别。
告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 此条告警信息的名称。 |
| 告警源 | 告警来源。 |
| 来源系统 | 对接系统的名称。 |
| 最近发生时间 | 此条告警信息最近一次发生时间。 |
| 定位信息 | 云服务：服务名称。 |
| 定位信息 | 节点类型：告警资源类型。 |
| 附加信息 | 云服务：服务名称。 |
| 附加信息 | 服务：设备节点类型，例如mgmt, lvs, nginx等。 |
| 附加信息 | 本端地址：产生告警信息的ELB API虚拟机的IP地址。 |
| 附加信息 | 对端地址：产生告警信息的ELB虚拟机的IP地址。 |
对系统的影响
单节点数据库对业务无影响，双节点数据库异常会导致业务流量异常。
可能原因
ELB API管理节点netcluster_elb_api_vm健康检查连接数据库出现异常。
处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 查询以下告警信息。
附加信息：
- 对端地址
- 本端地址
5. 使用PuTTY，登录异常数据库节点。
登录地址：4中查出的对端地址。
默认帐户：elb
默认密码：*****。
如果数据库节点无法登录，请联系技术工程师协助解决。
6. 执行以下命令，切换到root用户。root帐户默认密码：*****。
sudo su root
7. 执行以下命令，查看gaussdb进程状态是否为正常。
service had query
命令回显如下，则gaussdb进程状态为normal。
- 是，请执行8。
- 否，请联系技术支持工程师协助解决。
8. 观察告警信息是否清除。
- 是，处理结束。
- 否，执行9。
9. 使用PuTTY，登录ELB管理节点。
登录地址：4中查询到本端地址。
默认帐户：elb
默认密码：*****。
10. 执行以下命令重新加载nginx进程。
cd /usr/local/NSP/etc/elb/bin
./elb_process.sh reload
11. 观察告警信息是否清除。
- 是，处理结束。
- 否，请联系技术工程师协助解决。
参考信息
无。