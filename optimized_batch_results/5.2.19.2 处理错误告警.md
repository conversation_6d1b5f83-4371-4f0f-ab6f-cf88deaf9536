# ******** 处理错误告警

******** 处理错误告警
告警解释
API网关ftpfilesync服务异常或者文件权限被篡改，网关其他组件无法读写FTP文件。
告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48303 | 重要 | 处理错误告警 |
告警参数
| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Fault_Node | 故障节点IP地址 |
| 定位信息 | Source_Component | 告警源组件 |
| 定位信息 | Source_Node | 告警源节点IP地址 |
| 定位信息 | File_Name | 无法访问的文件名 |
对系统的影响
ftpfilesync服务异常或者文件权限被篡改，authadv或orchestration等无法读写ftp文件，无法从ftpfilesync组件获取最新的文件状态。
可能原因
ftpfilesync服务出现异常或者文件权限被篡改。
处理步骤
1. 查看告警的定位信息。
- 以admin帐号登录ManageOne运维面。
登录地址：https://ManageOne运维面主页的访问地址:31943，例如，https://oc.type.com:31943。默认密码：*****。
- 在页面上方的菜单栏中，选择“集中告警 > 当前告警”。
- 在告警列表中，找到待处理的告警记录。单击告警对应的“名称”列，弹出“告警详情和处理建议”窗口。
- 在“告警详情和处理建议”窗口的“基本信息”列表中，查看告警的定位信息。
- Source_Node：表示告警源节点IP地址。
- Fault_Node：表示故障节点IP地址。
如果未能找到节点信息，返回告警列表，单击告警前的“>”展开详细信息，在“被汇聚告警”页签中可找到告警的定位信息。
2. 使用PuTTY，登录故障节点Fault_Node。
默认帐号：paas，默认密码：*****。
3. 执行以下命令，防止会话超时退出。
TMOUT=0
4. 执行以下命令，登录SFTP服务。
sftp -oPort=port 用户@登录节点IP地址
其中port为FTP服务端口，缺省为“2022”；用户为“ftpapimgr”或者“ftpapigw”。
- 登录正常 => 5
- 登录失败 => 参见ALM-48107-无法访问FTP。
5. 查看告警是否清除。
- 是 => 处理完毕
- 否 => 6
6. 获取相关日志，并联系技术支持。
- 执行如下命令，切换到root用户。
sudo su - root
默认密码：*****。
- 执行如下命令，切换到source_component日志目录。
cd /var/log/apigateway/Source_Component/runtime/
- 下载此目录下所有日志到本地，并联系技术支持。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
参考信息
无。
********.2 ALM-48304-证书即将过期
告警解释
API网关某些组件使用的证书即将在30天内过期。
告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48304 | 次要 | 处理错误告警 |
告警参数
| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Node | 告警源节点IP地址 |
| 附加信息 | Certificate_Information | 证书的名称以及到期时间 |
对系统的影响
证书过期后将存在安全风险，且某些服务证书过期可能拒绝连接，影响这些服务的业务功能。
可能原因
当前证书的剩余使用天数低于失效前提醒时间30天。
处理步骤
7. 查看告警的定位信息。
- 以admin帐号登录ManageOne运维面。
登录地址：https://ManageOne运维面主页的访问地址:31943，例如，https://oc.type.com:31943。默认密码：*****。
- 在页面上方的菜单栏中，选择“集中告警 > 当前告警”。
- 在告警列表中，找到待处理的告警记录。单击告警对应的“名称”列，弹出“告警详情和处理建议”窗口。
- 在“告警详情和处理建议”窗口的“基本信息”列表中，查看告警的定位信息。
- Node：表示告警源节点IP地址。
- Certificate_Information：组件的证书名称和证书到期时间。
如果未能找到节点信息，返回告警列表，单击告警前的“>”展开详细信息，在“被汇聚告警”页签中可找到告警的定位信息。
8. 参考《华为云Stack 6.5.1 安全管理指南》“证书管理 > 更换B类&C类证书”章节，替换APIG证书，以免影响相关业务的使用。
9. 查看告警是否清除。
- 是 => 处理完毕
- 否 => 4
10. 保存告警相关信息，并联系技术支持。
- 在告警列表中，勾选当前处理的告警记录，导出并保存告警信息压缩包。
- 保存上述步骤中创建的API网关证书，并联系技术支持。
告警清除
系统每24小时检查一次证书信息，此告警修复后，系统会自动清除此告警，无需手工清除。
参考信息
无。
********.3 ALM-48305-证书已经过期
告警解释
API网关某些组件使用的证书已经过期。
告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48305 | 重要 | 处理错误告警 |
告警参数
| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Node | 告警源节点IP地址 |
| 附加信息 | Certificate_Information | 证书的名称以及到期时间 |
对系统的影响
API网关某些组件的服务证书过期。证书过期后将存在安全风险，且某些服务发现证书过期可能拒绝连接，影响服务的业务功能。
可能原因
证书过期。
处理步骤
11. 查看告警的定位信息。
- 以admin帐号登录ManageOne运维面。
登录地址：https://ManageOne运维面主页的访问地址:31943，例如，https://oc.type.com:31943。默认密码：*****。
- 在页面上方的菜单栏中，选择“集中告警 > 当前告警”。
- 在告警列表中，找到待处理的告警记录。单击告警对应的“名称”列，弹出“告警详情和处理建议”窗口。
- 在“告警详情和处理建议”窗口的“基本信息”列表中，查看告警的定位信息。
- Node：表示告警源节点IP地址。
- Certificate_Information：组件的证书名称和证书到期时间。
如果未能找到节点信息，返回告警列表，单击告警前的“>”展开详细信息，在“被汇聚告警”页签中可找到告警的定位信息。
12. 参考《华为云Stack 6.5.1 安全管理指南》“证书管理 > 更换B类&C类证书”章节，替换APIG证书，以免影响相关业务的使用。
13. 查看告警是否清除。
- 是 => 处理完毕
- 否 => 4
14. 保存告警相关信息，并联系技术支持。
- 在告警列表中，勾选当前处理的告警记录，导出并保存告警信息压缩包。
- 保存上述步骤中创建的API网关证书，并联系技术支持。
告警清除
系统每24小时检查一次证书信息，此告警修复后，系统会自动清除此告警，无需手工清除。
参考信息
无。
********.4 ALM-48306-证书校验失败
告警解释
API网关某些组件使用的证书验证失败。
告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48306 | 紧急 | 处理错误告警 |
告警参数
| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Node | 告警源节点IP地址 |
| 附加信息 | Certificate_Information | 证书的名称以及到期时间 |
对系统的影响
API网关某些组件的服务验证失败。证书验证失败将存在安全风险，且某些服务发现证书不可用可能拒绝连接，影响服务的业务功能。
可能原因
证书过期或证书无效。
处理步骤
15. 查看告警的定位信息。
- 以admin帐号登录ManageOne运维面。
登录地址：https://ManageOne运维面主页的访问地址:31943，例如，https://oc.type.com:31943。默认密码：*****。
- 在页面上方的菜单栏中，选择“集中告警 > 当前告警”。
- 在告警列表中，找到待处理的告警记录。单击告警对应的“名称”列，弹出“告警详情和处理建议”窗口。
- 在“告警详情和处理建议”窗口的“基本信息”列表中，查看告警的定位信息。
- Node：表示告警源节点IP地址。
- Certificate_Information：组件的证书名称和证书到期时间。
如果未能找到节点信息，返回告警列表，单击告警前的“>”展开详细信息，在“被汇聚告警”页签中可找到告警的定位信息。
16. 参考《华为云Stack 6.5.1 安全管理指南》“证书管理 > 更换B类&C类证书”章节，替换APIG证书，以免影响相关业务的使用。
17. 查看告警是否清除。
- 是 => 处理完毕
- 否 => 4
18. 保存告警相关信息，并联系技术支持。
- 在告警列表中，勾选当前处理的告警记录，导出并保存告警信息压缩包。
- 保存上述步骤中创建的API网关证书，并联系技术支持。
告警清除
系统每24小时检查一次证书信息，此告警修复后，系统会自动清除此告警，无需手工清除。
参考信息
无。
********.5 ALM-48316-请求超出单实例流控阈值
告警解释
业务请求量超过单实例的处理能力，系统启动保护机制。
告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48316 | 重要 | 处理错误告警 |
告警参数
| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Node | 告警源节点IP地址 |
| 附加信息 | Node_Rate_Limit | 流控阀值 |
对系统的影响
部分业务调用不能正常执行。
可能原因
- 业务请求量增加，达到流控阈值。
- 外部攻击导致访问量增大，达到流控阈值。
处理步骤
19. 查看告警的定位信息。
- 以admin帐号登录ManageOne运维面。
登录地址：https://ManageOne运维面主页的访问地址:31943，例如，https://oc.type.com:31943。默认密码：*****。
- 在页面上方的菜单栏中，选择“集中告警 > 当前告警”。
- 在告警列表中，找到待处理的告警记录。单击告警对应的“名称”列，弹出“告警详情和处理建议”窗口。
- 在“告警详情和处理建议”窗口的“基本信息”列表中，查看告警的定位信息：Node表示告警源节点IP地址。
如果未能找到节点信息，返回告警列表，单击告警前的“>”展开详细信息，在“被汇聚告警”页签中可找到告警的定位信息。
20. 使用PuTTY，登录故障节点Node。
默认帐号：paas，默认密码：*****。
21. 执行以下命令，切换到root用户。
sudo su - root
默认密码：*****。
22. 执行以下命令，防止会话超时退出。
TMOUT=0
23. 执行以下命令，收集业务运行日志并联系技术支持。
cd /var/log/apigateway/shubao
zip -r shubao_log.zip run
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
参考信息
无。
********.6 ALM-48317-重新加载LB失败
告警解释
LB在reload过程中产生异常，无法加载新配置项。
告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48317 | 重要 | 处理错误告警 |
告警参数
| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Component | 告警源组件 |
| 定位信息 | Node | 告警源节点IP地址 |
对系统的影响
LB无法加载新配置项，导致LB更新异常。
可能原因
创建backend时配置的host异常。
处理步骤
24. 查看告警的定位信息。
- 以admin帐号登录ManageOne运维面。
登录地址：https://ManageOne运维面主页的访问地址:31943，例如，https://oc.type.com:31943。默认密码：*****。
- 在页面上方的菜单栏中，选择“集中告警 > 当前告警”。
- 在告警列表中，找到待处理的告警记录。单击告警对应的“名称”列，弹出“告警详情和处理建议”窗口。
- 在“告警详情和处理建议”窗口的“基本信息”列表中，查看告警的定位信息：Node表示告警源节点IP地址。
如果未能找到节点信息，返回告警列表，单击告警前的“>”展开详细信息，在“被汇聚告警”页签中可找到告警的定位信息。
25. 使用PuTTY，登录故障节点Node。
默认帐号：paas，默认密码：*****。
26. 执行以下命令，先切换到root用户，再切换到apigateway用户。
su - root
默认密码：*****。
su - apigateway
27. 执行以下命令，防止会话超时退出。
TMOUT=0
28. 执行以下命令，检查shubao状态是否正常。
sh /opt/apigateway/resty/shell/health_check.sh
- normal => 7
- abnormal => 6
29. 执行以下命令，重启shubao组件。该重启操作对系统本身无不良影响。
sh /opt/apigateway/resty/shell/restart.sh
显示“xxx start successfully”，表示组件启动成功。
30. 等待1~3分钟，查看告警是否清除。
- 是 => 处理完毕
- 否 => 8
31. 获取相关日志，并联系技术支持。
- 执行如下命令，切换到root用户。
exit
- 执行如下命令，切换到日志目录。
cd /var/log/apigateway/shubao/run/
- 下载日志“error.log”到本地，并联系技术支持。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
参考信息
无。
********.7 ALM-48318-证书回退失败
告警解释
ManageOne下发证书替换任务后，网关替换失败且自动回退失败。
告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48318 | 重要 | 处理错误告警 |
告警参数
| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Component | 回退失败证书所属组件名称 |
| 定位信息 | File_Name | 回退失败证书名称 |
| 定位信息 | Node | 告警源节点IP地址 |
对系统的影响
组件证书无法使用，导致无法正常提供服务。
可能原因
ManageOne下发证书替换任务，节点在替换证书过程中出错，并且证书回退任务执行失败。
处理步骤
32. 查看告警的定位信息。
- 以admin帐号登录ManageOne运维面。
登录地址：https://ManageOne运维面主页的访问地址:31943，例如，https://oc.type.com:31943。默认密码：*****。
- 在页面上方的菜单栏中，选择“集中告警 > 当前告警”。
- 在告警列表中，找到待处理的告警记录。单击告警对应的“名称”列，弹出“告警详情和处理建议”窗口。
- 在“告警详情和处理建议”窗口的“基本信息”列表中，查看告警的定位信息。
- Node：表示告警源节点IP地址。
- Component：表示组件名称。
如果未能找到节点信息，返回告警列表，单击告警前的“>”展开详细信息，在“被汇聚告警”页签中可找到告警的定位信息。
33. 使用PuTTY，登录告警源节点Node。
默认帐号： paas，默认密码：*****。
34. 执行以下命令，切换到root用户。
sudo su - root
默认密码：*****。
35. 执行以下命令，防止会话超时退出。
TMOUT=0
36. 进入到证书目录。检查是否有该证书文件、私钥文件、及对应以“.bak”结尾的备份文件。
参考《华为云Stack 6.5.1 安全管理指南》“帐户管理 -> 帐户一览表”中的“FusionSphere Service”页签，查看API网关证书文件路径。
- 是 => 6
- 否 => 10
37. 执行cp xxx.bak xxx（xxx表示证书或者私钥文件带扩展名的名称，例如server.crt,server.key.encrypt等）命令，将“.bak”结尾的备份文件覆盖当前路径下的证书和私钥文件。
38. 执行以下命令，切换到相应组件运行用户。
su - 用户名
除apimgr组件使用apigw_apimgr用户外，其他组件使用apigateway用户。
39. 执行以下命令，重启组件。该重启操作不会对系统造成不良影响。
sh /opt/apigateway/Component/shell/restart.sh
显示xxx start successfully，表示服务启动成功。
40. 执行以下命令，检查组件运行状态。
sh /opt/apigateway/Component/shell/health_check.sh
- normal => 手动清除该告警
- abnormal => 10
41. 获取Component组件相关日志，并联系技术支持。
- 执行如下命令，切换到root用户。
exit
- 执行如下命令，切换到日志目录。
- 故障组件（Component对应值）为“shubao” => 执行命令：cd /var/log/apigateway/shubao/run
- 故障组件（Component对应值）为其他组件 => 执行命令：cd /var/log/apigateway/Component/runtime
- 下载日志“Component.log”到本地，并联系技术支持。
如果目录下不存在“Component.log”，下载“Component_shell.log”即可。
告警清除
手动清除。
参考信息
无。
********.8 ALM-48319-证书无法生效
告警解释
ManageOne下发证书替换任务后，ManageOne下发的证书无法正常使用。
告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48319 | 重要 | 处理错误告警 |
告警参数
| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Component | 未生效证书所属组件名称 |
| 定位信息 | File_Name | 未生效证书名称 |
| 定位信息 | Node | 告警源节点IP地址 |
对系统的影响
组件证书无法使用，组件无法正常提供服务。
可能原因
ManageOne下发证书替换任务，可能由于系统时间未到达证书生效时间、已超过证书失效时间、证书格式错误等原因导致。
处理步骤
42. 查看告警的定位信息。
- 以admin帐号登录ManageOne运维面。
登录地址：https://ManageOne运维面主页的访问地址:31943，例如，https://oc.type.com:31943。默认密码：*****。
- 在页面上方的菜单栏中，选择“集中告警 > 当前告警”。
- 在告警列表中，找到待处理的告警记录。单击告警对应的“名称”列，弹出“告警详情和处理建议”窗口。
- 在“告警详情和处理建议”窗口的“基本信息”列表中，查看告警的定位信息。
- Node：表示告警源节点IP地址。
- Component：表示组件名称。
如果未能找到节点信息，返回告警列表，单击告警前的“>”展开详细信息，在“被汇聚告警”页签中可找到告警的定位信息。
43. 使用PuTTY，登录告警源节点Node。
默认帐号： paas，默认密码：*****。
44. 执行以下命令，切换到root用户。
su - root
默认密码：*****。
45. 执行以下命令，防止会话超时退出。
TMOUT=0
46. 执行以下命令，切换到相应组件运行用户。
su - 用户
除apimgr组件使用apigw_apimgr用户外，其他组件使用apigateway用户。
47. 以相应组件用户，执行以下命令，检查组件运行状态。
sh /opt/apigateway/Component/shell/health_check.sh
- 正常 => 7
- 异常 => 12
48. 执行date命令，检查APIG节点系统时间是否准确。
- 正常 => 10
- 异常 => 8
49. 执行如下命令，检查NTP服务并修正系统时间。
- 执行以下命令，切换到apigateway用户。
exit
su - apigateway
- 执行以下命令，修正系统时间。
sh /opt/apigateway/ntp/shell/ntpdate.sh
回显信息示例：
1 Mar 20:31:14 ntpdate[24700]: step time server 10.109.164.136 offset 1.904335 sec
50. 手动清除告警。
51. 参考《华为云Stack 6.5.1 安全管理指南》“证书管理 > 更换B类&C类证书”查看CA参数和证书规格，并替换证书。
52. 查看告警是否清除。
- 是 => 处理完毕
- 否 => 12
53. 获取Component组件相关日志，并联系技术支持。
- 执行如下命令，切换到root用户。
exit
- 执行如下命令，切换到日志目录。
- 故障组件（Component对应值）为“shubao” => 执行命令：cd /var/log/apigateway/shubao/run
- 故障组件（Component对应值）为其他组件 => 执行命令：cd /var/log/apigateway/Component/runtime
- 下载日志“Component.log”到本地，并联系技术支持。
如果目录下不存在“Component.log”，下载“Component_shell.log”即可。
告警清除
手动清除。
参考信息
无。
********.9 ALM-48320-证书即将过期
告警解释
API网关某些组件使用的证书即将在10天内过期。
告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48320 | 重要 | 处理错误告警 |
告警参数
| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Node | 告警源节点IP地址 |
| 附加信息 | Certificate_Information | 证书的名称以及到期时间 |
对系统的影响
证书过期后将存在安全风险，且某些服务证书过期可能拒绝连接，影响这些服务的业务功能。
可能原因
当前证书的剩余使用天数少于10天。
处理步骤
54. 查看告警的定位信息。
- 以admin帐号登录ManageOne运维面。
登录地址：https://ManageOne运维面主页的访问地址:31943，例如，https://oc.type.com:31943。默认密码：*****。
- 在页面上方的菜单栏中，选择“集中告警 > 当前告警”。
- 在告警列表中，找到待处理的告警记录。单击告警对应的“名称”列，弹出“告警详情和处理建议”窗口。
- 在“告警详情和处理建议”窗口的“基本信息”列表中，查看告警的定位信息。
- Node：表示告警源节点IP地址。
- Certificate_Information：组件的证书名称和证书到期时间。
如果未能找到节点信息，返回告警列表，单击告警前的“>”展开详细信息，在“被汇聚告警”页签中可找到告警的定位信息。
55. 参考《华为云Stack 6.5.1 安全管理指南》“证书管理 > 更换B类&C类证书”章节，替换APIG证书，以免影响相关业务的使用。
56. 查看告警是否清除。
- 是 => 处理完毕
- 否 => 4
57. 保存告警相关信息，并联系技术支持。
- 在告警列表中，勾选当前处理的告警记录，导出并保存告警信息压缩包。
- 保存上述步骤中创建的API网关证书，并联系技术支持。
告警清除
系统每24小时检查一次证书信息，此告警修复后，系统会自动清除此告警，无需手工清除。
参考信息
无。
< 上一节