# ********.1 ALM-2001101 lvs_keepalived进程不存在

告警解释
LVS使用keepalived绑定浮动IP，当keepalived进程不存在时，系统产生此告警，提示系统故障或者风险。
告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 2001101 | 紧急 | 是 |
告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
对系统的影响
当登录或者跳转各个云服务管理控制台界面时访问异常。
可能原因
Keepalived进程停止。
处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
5. 使用“PuTTY”，登录4中确认的虚拟机。
默认帐号：ulb，默认密码：*****。
6. 执行以下命令，防止PuTTY超时退出。
TMOUT=0
7. 执行下列命令切换到root用户。
sudo su - root
默认帐号：root，默认密码：*****。
8. 执行以下命令，重启keepalived后观察是否仍有告警。
service keepalived restart
- 是，请联系技术支持工程师协助解决。
- 否，处理完毕。
参考信息
无。