# 5.2.3.1.47 ALM-70201 DHCP-agent管理的network数量超过阈值

##### 告警解释
当DHCP-agent管理的network数量大于系统设定(200*0.85)时，系统产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70201 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID<br>云服务：服务类型，固定值为VPC |
| 附加信息 | 异常信息：Networks Exceeds Limitation on a DHCP agent |
##### 对系统的影响
- 可能导致该DHCP-agent对应主机的内存占用过高，影响该主机其他业务正常运行。
- 如果当前系统中所有DHCP-agent管理的network数量都已经大于系统设定（200）时，则系统中会存在部分network没有绑定到DHCP-agent上，使用这些network创建的虚拟机无法通过neutron提供的dhcp功能来自动获取ip地址。
##### 可能原因
- AZ内创建的network数量过多。
- 手动或自动触发了DHCP重调度，导致特定DHCP-agent管理的network过多。
- 节点存在冗余neutron命名空间。
##### 处理步骤
1. 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
2. 在“概要”界面，根据告警附加信息中的“主机ID”或“主机名”，在“管理IP地址”一栏获得主机的管理IP地址。
3. 使用PuTTY，通过主机的管理IP地址，登录告警所在主机。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
4. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
5. 执行以下命令，防止系统超时退出。
TMOUT=0
6. 执行以下命令，导入环境变量。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
7. 输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
默认密码为：*****。
8. 查看系统中network和DHCP-agent数量。
- 执行命令neutron agent-list | grep neutron-dhcp-agent | grep $host_id，在agent列表中可以查找已部署的DHCP-agent。
##### $host_id为告警参数中的定位信息。
- 执行命令neutron net-list-on-dhcp-agent $dhcp_agent_id，查看某个DHCP-agent管理的所有network。其中，$dhcp_agent_id为8.a查询的id。
- 统计部署的agent和每个agent绑定network数量，判断是否DHCP-agent部署不足。
若数量过多，可借助Linux的wc命令等辅助统计数量。
- 执行命令neutron agent-list | grep neutron-dhcp-agent | wc –l，统计DHCP-agent数量。
- 执行命令neutron net-list-on-dhcp-agent $dhcp_agent_id | wc -l ，统计DHCP-agent绑定的网络数量，查询的结果减去4就是该DHCP-agent绑定的网络数量。
- 分别执行以下两条命令，查看系统内未绑定到DHCP-agent的network。
net_id=`neutron net-list | awk '{print $2}'`
for i in $net_id;do if [ "$i" != "id" ];then c=`neutron dhcp-agent-list-hosting-net $i | awk '{print $6}' | tr "\n" " "`; if [ "$c" = " " ];then echo unbonded network_id is $i;fi;fi;done
9. 根据部署场景，判断是否为分布式DHCP。
- 是，执行11。
- 否，执行10。
判断方式如下：
- Region Type I：
判断部署的场景是级联层还是被级联层的方法：在OpenStack首节点，执行命令cps productinfo-show，查看product_type的取值，cascading表示级联层，cascaded表示被级联层。
- Region Type I级联层默认为集中式DHCP。
- Region Type I被级联层，执行如下命令：
cps template-params-show --service network-agent neutron-dhcp-agent
根据回显信息，查看dhcp_distributed配置项是否为True，如果是则为分布式DHCP。
- Region Type II场景执行如下命令：
cps host-role-list $host_id | grep dhcp
根据回显信息，查看第一列是否为dhcp，如果是则为集中式DHCP。
- Region Type III，请执行如下命令：
cps template-params-show --service neutron neutron-dhcp-agent
根据回显信息，查看dhcp_distributed配置项是否为True，如果是则为分布式DHCP。
10. 根据network和DHCP-agent数量关系，判断是否因为DHCP-agent部署不足导致了本次告警的触发。
判断方法：在8.c的查询结果中，如果DHCP-agent平均绑定的网络个数，即8.c的所有的DHCP-agent绑定的网络数量和除以8.c中DHCP-agent个数的结果超过了系统设定(200*0.85)，则DHCP-agent部署不足。
- 是，增加AZ内的DHCP角色，根据未绑定到DHCP-agent的network的数量，确定需要扩容的DHCP-agent个数，并手动将超过阈值的DHCP-agent管理的network重调度到新扩容的DHCP-agent中。
- 否，手动将超过阈值的DHCP-agent管理的network重调度到其他负荷较低的DHCP-agent中,注意在重调度过程中不要下发业务。
重调度示例：
- 执行命令：neutron dhcp-agent-network-remove $dhcp_agent_id $network_id，将该network从旧的DHCP-agent移除。其中，$network_id为8.b中查询的id。
- 执行命令neutron dhcp-agent-network-add $dhcp_agent_id $network_id，将该network绑定到新的DHCP-agent。
- 执行命令neutron dhcp-agent-list-hosting-net $network_id，查询重调度是否成功。
11. 根据network和DHCP-agent数量关系，判断是否因为DHCP-agent部署不足导致了本次告警的触发。
判断方法：在8.c的查询结果中，如果DHCP-agent平均绑定的网络个数，即8.c的所有的DHCP-agent绑定的网络数量和除以8.c中DHCP-agent个数的结果超过了系统设定(200*0.85)，则DHCP-agent部署不足。
- 是，扩容主机，并在Service OM界面，将上报告警所在主机的虚拟机迁移到新的主机上。
- 否，手动在Service OM界面，将上报告警所在主机的虚拟机迁移到指定的没有超阈值的主机上。
热迁移虚拟机会造成短暂的业务闪断。
12. 重调度DHCP-agent可能会导致命名空间残留，根据当前环境部署的场景，获取对应的“手动审计”章节，重新触发对冗余neutron命名空间（dhcp命名空间和router命名空间）的审计。
- Region Type I：
- 级联层：手动审计
- KVM虚拟化（被级联层）：手动审计
- Region Type II&Region Type III：
- FusionCompute虚拟化：手动审计
- KVM虚拟化：手动审计
13. 审计结束后查看审计报告，如果存在冗余命名空间，请参考“处理冗余neutron命名空间”章节进行处理。
- Region Type I KVM虚拟化（被级联层）：处理冗余neutron命名空间
- Region Type II&Region Type III：
- FusionCompute虚拟化：处理冗余neutron命名空间
- KVM虚拟化：处理冗余neutron命名空间