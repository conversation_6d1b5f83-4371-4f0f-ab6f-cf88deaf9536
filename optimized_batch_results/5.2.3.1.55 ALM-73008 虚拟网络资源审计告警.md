# 5.2.3.1.55 ALM-73008 虚拟网络资源审计告警

##### 告警解释
当执行系统审计时发现虚拟网络资源存在异常时，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73008 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务<br>服务：产生告警的服务<br>审计名称：<br>info-collect-server：产生告警的审计项为dhcp名空间资源冗余<br>neutron：产生告警的审计项为存在野port资源<br>audit_neutron_port：产生告警的审计项为port资源冗余 |
| 附加信息 | 详细信息：<br>redundant_dhcpns_count xxx：产生告警的冗余dhcp名空间个数<br>Wild_ports xxx：产生告警的野port<br>stale_ports xxx：产生告警的冗余port |
##### 对系统的影响
此告警产生时，系统中存在状态不正常的虚拟网络资源，影响系统对虚拟网络资源的管理。
##### 可能原因
- 系统中存在虚拟机假port。
- 系统中存在冗余的neutron命名空间（包括dhcp命名空间或router命名空间）。
- 系统中存在虚拟网络野端口。
##### 请在告警的详细信息中获取引发告警的具体审计问题，并参考处理步骤完成相应问题的处理。
##### 处理步骤
1. 获取告警详情中“附加信息”参数中的“详细信息”取值，并参考表1，获取对应的审计报告名称。
| 表1 详细信息与审计报告的对应关系 | 表1 详细信息与审计报告的对应关系 |
| --- | --- |
| 详细信息 | 审计报告 |
| stale_ports | stale_ports.csv |
| redundant_dhcp_namespaces（且结果不为0） | redundant_namespaces.csv |
| redundant_router_namespaces（且结果不为0） | redundant_namespaces.csv |
| wild_ports | neutron_wild_ports.csv |
2. 确定当前环境部署的场景，获取审计报告。
- Region Type I：
- 判断部署的场景是级联层还是被级联层的方法：在OpenStack首节点，执行命令cps productinfo-show，查看product_type的取值，cascading表示级联层，cascaded表示被级联层。
- 如果审计类告警出现在被级联层，则无论级联层是否同时出现告警，都应当先处理被级联层告警，待告警恢复后，再次执行级联层的审计（可手动触发审计，或等待级联层每日自动进行的审计），以确认级联层与被级联层之间的信息同步。
- 级联层：收集审计报告
- KVM虚拟化（被级联层）：收集审计报告
- Region Type II&Region Type III：
- FusionCompute虚拟化：收集审计报告
- KVM虚拟化：收集审计报告
3. 确定当前环境部署的场景，获取对应的“审计结果定位”章节。查找对应审计报告名称的处理方式，并按之处理审计项。
- Region Type I：
- 级联层：审计结果定位
- KVM虚拟化（被级联层）：审计结果定位
- Region Type II&Region Type III：
- FusionCompute虚拟化：审计结果定位
- KVM虚拟化：审计结果定位
4. 根据当前环境部署的场景，获取对应的“手动审计”章节，重新触发系统审计。
- Region Type I：
- 级联层：手动审计
- KVM虚拟化（被级联层）：手动审计
- Region Type II&Region Type III：
- FusionCompute虚拟化：手动审计
- KVM虚拟化：手动审计
5. 查看告警是否清除。
- 是，处理完毕。
- 否，执行6。
6. 请联系技术支持工程师协助解决。
##### 参考信息
无。