# ********.92 0x6000840003 访问ManageOne失败

********.92 0x6000840003 访问ManageOne失败
告警解释
访问ManageOne（IP：[IPAddress]）失败。
告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x6000840003 | 重要 | 否 |
告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| IPAddress | ManageOne的IP地址 |
对系统的影响
无法使用ManageOne替换证书。
可能原因
- 网络异常。
- 连接ManageOne的用户名或密码错误。
处理步骤
- 可能原因1：网络异常。
- 使用PuTTY，以管理IP地址登录eBackup-Manager节点（Manager角色所在节点）或Server角色所在节点。
登录地址：
- eBackup-Manager节点的管理IP地址为“Workflow-PublicService-IP0”字段对应的IP地址。
- Server角色所在节点的管理IP地址为“datamover_externalom_iplist”字段对应的IP地址。
默认帐户：hcp，默认密码：*****
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行cat /opt/huawei-data-protection/ebackup/microservice/ebk_governance/conf/hcpconf.ini | grep ManageOneAddr命令，获取ManageOne的IP地址。
- 如果是IPv4，执行ping ManageOne的IP地址命令，如果是IPv6，执行ping6 ManageOne的IP地址命令，检查网络通信是否正常。
- 是，执行2。
- 否，执行1.e。
- 联系管理员修复网络问题。
- 等待15分钟左右，在当前告警界面查看该告警是否恢复。
- 是，处理结束。
- 否，执行2。
- 可能原因2：连接ManageOne的用户名或密码错误。
- 使用PuTTY，以管理IP地址登录eBackup-Manager节点（Manager角色所在节点）或Server角色所在节点。
登录地址：
- eBackup-Manager节点的管理IP地址为“Workflow-PublicService-IP0”字段对应的IP地址。
- Server角色所在节点的管理IP地址为“datamover_externalom_iplist”字段对应的IP地址。
默认帐户：hcp，默认密码：*****
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行cd /opt/huawei-data-protection/ebackup/microservice/ebk_governance/script命令，进入微服务脚本目录。
- 执行sh change_manage_one_info.sh命令，根据提示修改连接ManageOne的信息。
- 等待3分钟左右，在当前告警界面查看该告警是否恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
参考信息
无