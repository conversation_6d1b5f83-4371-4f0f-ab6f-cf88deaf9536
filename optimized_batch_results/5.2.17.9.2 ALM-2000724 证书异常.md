# 5.******** ALM-2000724 证书异常

##### 告警解释
当任务中心证书即将过期、已过期、证书无效或不存在时产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 2000724 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
任务中心服务不可用。
##### 可能原因
- 阈值为1：证书即将过期。
- 阈值为2：证书过期。
- 阈值为3：证书无效或者证书不存在。
前提条件
- 已获取根证书、签名后证书以及生成证书库时输入的密码。例如，根证书名称为ca.crt，签名后证书为server.crt。
- 已准备跨平台远程访问工具，如“PuTTY”。
- 已准备网络传输工具，如“WinSCP”。
- 已获取待更换证书的任务中心服务节点的管理IP地址，apitask和root帐户的登录密码。
“root”用户的默认密码是“*****”，“apitask”用户的默认密码是“*****”，“server.keystore”证书的默认密码为“*****”。
操作步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
5. 使用“PuTTY”工具，通过4中获取的节点IP地址登录产生告警的节点。
默认帐号：apitask，默认密码：*****。
6. 使用网络传输工具（例如“WinSCP”工具），将获取的证书上传到任务中心服务节点的“/home/<USER>
7. 执行如下命令，并按提示输入“root”用户的密码，切换到“root”用户。
sudo su - root
8. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
9. 执行以下命令，进入证书库所在目录。
cd /opt/taskcenter/taskcenter-service/resources/keystore
10. 执行以下命令，备份原有证书库。
mv server.keystore server.keystore.bak
11. 执行以下命令，生成新的证书库文件server.keystore。
cd /home/<USER>
source /etc/profile
openssl pkcs12 -export -in 证书名称 -inkey 私钥名称 -out server.keystore -name tomcat_server
例如：
openssl pkcs12 -export -in server.crt -inkey server.key -out server.keystore -name tomcat_server
12. 根据提示两次输入证书库密码，该密码必须和私钥文件密码一致。
13. 执行以下命令，导入根证书。
keytool -import -v -trustcacerts -alias ca_root -file CA证书名称 -keystore server.keystore
例如导入server.crt：
keytool -import -v -trustcacerts -alias ca_root -file ca.crt -keystore server.keystore
显示如下信息：
Enter keystore password:
14. 输入证书库密码，按“Enter”。
15. 输入“yes”，按“Enter”。
显示如下信息时，完成CA证书的导入：
Certificate reply was installed in keystore
[Storing server.keystore]
16. 执行以下命令，并按提示输入证书库密码，检查证书是否导入成功。
keytool -list -v -keystore server.keystore
回显中包含如下信息时，表示证书正确导入。
Your keystore contains 2 entries
如果回显信息显示keytool命令不存在，则需要执行命令source /etc/profile导入环境变量。
17. 执行以下命令，修改证书库权限。
chmod 600 server.keystore
chown apitask:apitask server.keystore
18. 执行以下命令，将导入证书后的证书库server.keystore拷贝到keystore文件夹下。
cp /home/<USER>/server.keystore /opt/taskcenter/taskcenter-service/resources/keystore
19. 执行以下命令，删除“/home/<USER>
rm server.keystore ca.crt server.crt
20. 执行以下命令，加密私钥密码。
cd /opt/taskcenter/taskcenter-service/bin
sh kspass.sh ks
显示如下信息：
Please input the keystore password:
Please input the keystore password again:
Please input the key password (Press 'ENTER' if same as keystore password):
21. 输入证书库密码和私钥密码，按“Enter”。
回显信息如下时，表示修改成功。
Result: success.
随机生成的私钥密码的密文同时保存在任务中心服务配置文件中。
22. 执行如下命令，切换到“apitask”用户。
su - apitask
默认密码：*****
23. 执行以下命令，重启任务中心服务。
sh stop.sh
sh startup.sh
24. 执行以下命令，查看任务中心进程是否正常。
ps -ef | grep taskcenter | grep -v grep
回显类似如下，所示则表示进程正常。
apitask 13773 1 0 11:47 ? 00:00:15 /opt/common/jre/bin/java -Dname=taskcenter -classpath ...
- 是，证书替换成功，执行25。
- 否，请联系技术支持工程师协助解决。
25. 观察告警是否消除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
< 上一节
********* SDR
*********.1 计量话单告警参考
*********.1.1 ALM-2000301 计量话单生成话单失败
##### 告警解释
计量话单服务会在每个整点时刻过15分钟后生成话单文件。生成话单文件时，当数据源异常（数据源无法访问或者本身存在异常）会导致话单生成失败，服务会启动失败重试机制，当重试次数等于服务设定的阈值时，就会产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 2000301 | 紧急 | 否 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 区域 | 产生告警信息的区域。 |
| 定位信息 | 云服务 | 产生告警信息的云服务。 |
| 附加信息 | 云服务 | 产生告警信息的云服务。 |
| 附加信息 | 失败话单总数 | 生成话单失败的告警次数。 |
| 附加信息 | 失败资源类型 | 生成话单失败的资源类型。 |
| 附加信息 | 失败原因 | 生成告警的原因，例如“Query data from ceilometer error”。 |
| 附加信息 | 开始时间 | 开始时间对应的时间戳。 |
| 附加信息 | 结束时间 | 结束时间对应的时间戳。 |
| 附加信息 | 虚拟机名称 | 产生告警信息的虚拟机名称。 |
##### 对系统的影响
计费系统无法采集到本周期话单，影响正常计费。
##### 可能原因
- 数据源异常，数据源有两种：一种是ceilometer提供的；另外一种是非ceilometer的，即服务自身提供的。
- 上传通道异常，即连接ManageOne的SFTP异常。
##### 处理步骤
26. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
27. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
28. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
29. 从告警的“定位信息”中获取当时告警所在Region。
30. 使用PuTTY，登录4中所获取Region下的PUB-SRV03节点。
PUB-SRV03节点IP地址请在导出的包含IP地址和参数信息汇总文件《xxx_export_all_CN.xlsm》的“2.1 工具生成的IP参数”页签搜索“PUB-SRV03”获取。
默认帐号：meteradmin，默认密码：*****。
31. 执行以下命令，手动生成话单文件并清除告警，检查回显信息中是否包含“all task successfully”。
sh /home/<USER>/meterticket-controller/bin/handleAlarm.sh
- 是，执行7。
- 否，请联系技术支持工程师协助解决。
32. 观察告警是否清除。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
如无计费需求，请参考配置屏蔽规则屏蔽本条告警。
*********.1.2 ALM-2000317 计量话单服务异常
##### 告警解释
计量话单健康检查异常，就会产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 2000317 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
话单文件可能无法正常生成。
##### 可能原因
计量话单进程未启动或者数据库连接异常。
##### 处理步骤
33. 使用PuTTY，登录PUB-DB01节点。
默认帐号：gaussdb，默认密码：*****
34. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
35. 执行以下命令，输入root密码，切换到root用户下。
sudo su - root
执行以下命令，查询数据库状态。
service had query
- 数据库正常，请执行4。
- 数据库异常，请联系技术支持工程师协助解决。
36. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
37. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
38. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
39. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
40. 使用“PuTTY”工具，通过7中获取的节点IP地址登录产生告警的节点。
默认帐号：meteradmin，默认密码：*****。
41. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
42. 执行以下命令，查询服务进程。
ps -ef |grep meteradmin
- 存在，执行11后再执行13。
- 不存在，执行12。
43. 执行以下命令，重启服务进程。
sh /home/<USER>/meterticket-*/bin/stop.sh
sh /home/<USER>/meterticket-*/bin/startup.sh
44. 执行以下命令，启动服务进程。
sh /home/<USER>/meterticket-*/bin/startup.sh
45. 执行如下命令，检查服务进程运行是否正常。
ps -ef |grep meteradmin
- 是，执行14。
- 否，请联系技术支持工程师协助解决。
46. 等待10分钟，检查告警是否已自动清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
*********.1.3 ALM-2000327 计量话单证书告警
##### 告警解释
计量话单服务证书过期，就会产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 2000327 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
影响接口调用。
##### 可能原因
计量话单节点证书已经过期或者三十天之内过期。
##### 处理步骤
47. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
48. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
49. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
50. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
51. 通过4中获取的节点IP地址判断上报告警的节点。
- 如果是Controller节点，请执行6~17。
- 如果是Agent节点，请执行18~29。
更换Controller节点的证书
52. 使用网络传输工具（例如“WinSCP”工具），将获取的证书上传到Controller节点的“/home/<USER>
53. 使用Putty，以“meteradmin”用户登录Controller节点。
“root”用户的默认密码是“*****”，“meteradmin”用户的默认密码是“*****”，server.crt的默认密码为*****。
54. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
55. 执行以下命令，备份原有证书库文件server.keystore。
mv /home/<USER>/meterticket-controller/resources/keystore/server.keystore /home/<USER>/meterticket-controller/resources/keystore/server.keystore.bak
56. 执行以下命令，生成新的证书库文件server.keystore。
keytool -genkey -alias 证书库别名 -keypass 别名密码 -keyalg 算法 -keysize 密钥长度 -validity 有效期（天） -keystore 指定生成证书的位置和证书名称 -storepass 获取keystore信息的密码 -dname "C=单位的两字母国家代码, ST=州或省份名称, L=城市或区域名称, O=组织名称, OU=组织单位名称, CN=名字与姓氏"
例如，生成server.keystore：
keytool -genkey -alias sdr_jetty -keypass Huadan@szx666 -keyalg RSA -keysize 2048 -validity 3650 -keystore server.keystore -storepass Huadan@szx666 -dname "C=CN, ST=Shaanxi, L=Xi'an, O=Huixin, OU=IT, CN=PRIVATE_CLOUD"
57. 执行以下命令，导入根证书。
keytool -import -v -trustcacerts -alias ca_root -file CA证书名称 -keystore server.keystore
例如导入ca.crt：
keytool -import -v -trustcacerts -alias ca_root -file ca.crt -keystore server.keystore
显示如下信息：
Enter keystore password:
如果回显信息显示keytool命令不存在，则需要执行如下命令导入环境变量。
source /etc/profile
58. 输入证书库密码，按“Enter”。
59. 输入“yes”，按“Enter”。
显示如下信息时，完成CA证书的导入：
Certificate was added to keystore
[Storing server.keystore]
60. 执行以下命令，导入签名后证书。
keytool -import -v -trustcacerts -alias ca_server -file 证书名称 -keystore server.keystore
例如导入server.crt：
keytool -import -v -trustcacerts -alias ca_server -file server.crt -keystore server.keystore
显示如下信息：
Enter keystore password:
61. 输入证书库密码，按“Enter”。
62. 输入“yes”，按“Enter”。
63. 证书替换后，如果告警仍未消除，请联系技术支持工程师协助解决。
更换Agent节点的证书
64. 使用网络传输工具（例如“WinSCP”工具），将获取的证书上传到Agent节点的“/home/<USER>
65. 使用Putty，以“meteradmin”用户登录Agent节点。
“root”用户的默认密码是“*****”，“meteradmin”用户的默认密码是“*****”，server.crt的默认密码为*****。
66. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
67. 执行以下命令，备份原有证书库文件server.keystore。
mv /home/<USER>/meterticket-agent/resources/keystore/server.keystore /home/<USER>/meterticket-agent/resources/keystore/server.keystore.bak
68. 执行以下命令，生成新的证书库文件server.keystore。
keytool -genkey -alias 证书库别名 -keypass 别名密码 -keyalg 算法 -keysize 密钥长度 -validity 有效期（天） -keystore 指定生成证书的位置和证书名称 -storepass 获取keystore信息的密码 -dname "C=单位的两字母国家代码, ST=州或省份名称, L=城市或区域名称, O=组织名称, OU=组织单位名称, CN=名字与姓氏"
例如，生成server.keystore：
keytool -genkey -alias sdr_jetty -keypass Huadan@szx666 -keyalg RSA -keysize 2048 -validity 3650 -keystore server.keystore -storepass Huadan@szx666 -dname "C=CN, ST=Shaanxi, L=Xi'an, O=Huixin, OU=IT, CN=PRIVATE_CLOUD"
69. 执行以下命令，导入根证书。
keytool -import -v -trustcacerts -alias ca_root -file CA证书名称 -keystore server.keystore
例如导入ca.crt：
keytool -import -v -trustcacerts -alias ca_root -file ca.crt -keystore server.keystore
显示如下信息：
Enter keystore password:
如果回显信息显示keytool命令不存在，则需要执行如下命令导入环境变量。
source /etc/profile
70. 输入证书库密码，按“Enter”。
71. 输入“yes”，按“Enter”。
显示如下信息时，完成CA证书的导入：
Certificate was added to keystore
[Storing server.keystore]
72. 执行以下命令，导入签名后证书。
keytool -import -v -trustcacerts -alias ca_server -file 证书名称 -keystore server.keystore
例如导入server.crt：
keytool -import -v -trustcacerts -alias ca_server -file server.crt -keystore server.keystore
显示如下信息：
Enter keystore password:
73. 输入证书库密码，按“Enter”。
74. 输入“yes”，按“Enter”。
75. 证书替换后，如果告警仍未消除，请联系技术支持工程师协助解决。
*********.1.4 ALM-2000328 计量话单证书告警
##### 告警解释
计量话单服务证书过期，就会产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 2000328 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
影响接口调用。
##### 可能原因
计量话单节点证书已经过期或者三十天之内过期。
##### 处理步骤
76. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
77. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
78. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
79. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
80. 通过4中获取的节点IP地址判断上报告警的节点。
- 如果是Controller节点，请执行6~17。
- 如果是Agent节点，请执行18~29。
更换Controller节点的证书
81. 使用网络传输工具（例如“WinSCP”工具），将获取的证书上传到Controller节点的“/home/<USER>
82. 使用Putty，以“meteradmin”用户登录Controller节点。
“root”用户的默认密码是“*****”，“meteradmin”用户的默认密码是“*****”，server.crt的默认密码为*****。
83. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
84. 执行以下命令，备份原有证书库文件server.keystore。
mv /home/<USER>/meterticket-controller/resources/keystore/server.keystore /home/<USER>/meterticket-controller/resources/keystore/server.keystore.bak
85. 执行以下命令，生成新的证书库文件server.keystore。
keytool -genkey -alias 证书库别名 -keypass 别名密码 -keyalg 算法 -keysize 密钥长度 -validity 有效期（天） -keystore 指定生成证书的位置和证书名称 -storepass 获取keystore信息的密码 -dname "C=单位的两字母国家代码, ST=州或省份名称, L=城市或区域名称, O=组织名称, OU=组织单位名称, CN=名字与姓氏"
例如，生成server.keystore：
keytool -genkey -alias sdr_jetty -keypass Huadan@szx666 -keyalg RSA -keysize 2048 -validity 3650 -keystore server.keystore -storepass Huadan@szx666 -dname "C=CN, ST=Shaanxi, L=Xi'an, O=Huixin, OU=IT, CN=PRIVATE_CLOUD"
86. 执行以下命令，导入根证书。
keytool -import -v -trustcacerts -alias ca_root -file CA证书名称 -keystore server.keystore
例如导入ca.crt：
keytool -import -v -trustcacerts -alias ca_root -file ca.crt -keystore server.keystore
显示如下信息：
Enter keystore password:
如果回显信息显示keytool命令不存在，则需要执行如下命令导入环境变量。
source /etc/profile
87. 输入证书库密码，按“Enter”。
88. 输入“yes”，按“Enter”。
显示如下信息时，完成CA证书的导入：
Certificate was added to keystore
[Storing server.keystore]
89. 执行以下命令，导入签名后证书。
keytool -import -v -trustcacerts -alias ca_server -file 证书名称 -keystore server.keystore
例如导入server.crt：
keytool -import -v -trustcacerts -alias ca_server -file server.crt -keystore server.keystore
显示如下信息：
Enter keystore password:
90. 输入证书库密码，按“Enter”。
91. 输入“yes”，按“Enter”。
92. 证书替换后，如果告警仍未消除，请联系技术支持工程师协助解决。
更换Agent节点的证书
93. 使用网络传输工具（例如“WinSCP”工具），将获取的证书上传到Agent节点的“/home/<USER>
94. 使用Putty，以“meteradmin”用户登录Agent节点。
“root”用户的默认密码是“*****”，“meteradmin”用户的默认密码是“*****”，server.crt的默认密码为*****。
95. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
96. 执行以下命令，备份原有证书库文件server.keystore。
mv /home/<USER>/meterticket-agent/resources/keystore/server.keystore /home/<USER>/meterticket-agent/resources/keystore/server.keystore.bak
97. 执行以下命令，生成新的证书库文件server.keystore。
keytool -genkey -alias 证书库别名 -keypass 别名密码 -keyalg 算法 -keysize 密钥长度 -validity 有效期（天） -keystore 指定生成证书的位置和证书名称 -storepass 获取keystore信息的密码 -dname "C=单位的两字母国家代码, ST=州或省份名称, L=城市或区域名称, O=组织名称, OU=组织单位名称, CN=名字与姓氏"
例如，生成server.keystore：
keytool -genkey -alias sdr_jetty -keypass Huadan@szx666 -keyalg RSA -keysize 2048 -validity 3650 -keystore server.keystore -storepass Huadan@szx666 -dname "C=CN, ST=Shaanxi, L=Xi'an, O=Huixin, OU=IT, CN=PRIVATE_CLOUD"
98. 执行以下命令，导入根证书。
keytool -import -v -trustcacerts -alias ca_root -file CA证书名称 -keystore server.keystore
例如导入ca.crt：
keytool -import -v -trustcacerts -alias ca_root -file ca.crt -keystore server.keystore
显示如下信息：
Enter keystore password:
如果回显信息显示keytool命令不存在，则需要执行如下命令导入环境变量。
source /etc/profile
99. 输入证书库密码，按“Enter”。
100. 输入“yes”，按“Enter”。
显示如下信息时，完成CA证书的导入：
Certificate was added to keystore
[Storing server.keystore]
101. 执行以下命令，导入签名后证书。
keytool -import -v -trustcacerts -alias ca_server -file 证书名称 -keystore server.keystore
例如导入server.crt：
keytool -import -v -trustcacerts -alias ca_server -file server.crt -keystore server.keystore
显示如下信息：
Enter keystore password:
102. 输入证书库密码，按“Enter”。
103. 输入“yes”，按“Enter”。
104. 证书替换后，如果告警仍未消除，请联系技术支持工程师协助解决。
< 上一节
##### *********.2 参考信息
对于设备上报而不需要关注的告警/事件，可以通过创建屏蔽规则，使后续上报且符合屏蔽规则的告警/事件不会显示在当前告警和事件列表中。
< 上一节
*********.2.1 配置屏蔽规则
对于设备上报而不需要关注的告警/事件，可以通过创建屏蔽规则，使后续上报且符合屏蔽规则的告警/事件不会显示在当前告警和事件列表中。
前提条件
105. 已具备“告警设置”的操作权限。
106. 用户只能对管理对象的规则进行启停、增删改和导入，对非管理对象的规则只能查看和导出。
背景信息
107. 屏蔽规则只会屏蔽建立规则之后上报的告警/事件，对于已上报的告警/事件不会有影响。
108. 停用、删除或修改屏蔽规则后，不会重复上报停用、删除或修改之前已经被屏蔽的告警/事件。
109. 屏蔽规则是以告警的原始属性（重定义前的属性）来进行屏蔽的。如果针对某条告警既设置了级别重定义规则，又设置了屏蔽规则，则屏蔽规则先起作用，然后重定义规则才起作用。
110. 最多支持创建200条屏蔽规则。
111. 同时满足以下条件的屏蔽规则会屏蔽所有告警/事件，将导致告警/事件无法上报。
- 屏蔽规则已启用。
- 屏蔽规则选择了所有告警/事件源。
- 屏蔽规则选择了所有告警/事件级别。
- 屏蔽规则没有选择生效开始时间、生效结束时间。
112. 创建事件屏蔽规则与创建告警屏蔽规则的操作类似，本操作以创建告警屏蔽规则为例，如需创建事件屏蔽规则请在“屏蔽规则”页面中单击“创建” ，选择“ 事件屏蔽规则”。
操作步骤
113. 在主菜单中选择“集中告警 > 集中告警 > 告警设置”。
114. 在左侧导航树中选择“屏蔽规则”。
115. 在“屏蔽规则”页面中，单击“创建” ，选择“ 告警屏蔽规则”。
116. 设置规则的名称、告警源、规则生效的告警级别，用户还可在“指定告警”中选择需要屏蔽的告警。
- 只有管理对象为所有资源的用户可以选择“所有告警源”。
- 在设置“告警源”时，选择“所有告警源”将对系统自身和所有管理对象产生的符合条件的告警进行屏蔽，请谨慎使用。
##### 117. 在“条件”区域中，设置告警参数条件来筛选需要屏蔽的告警。
例如需要根据告警的定位信息进行筛选，则选择“定位信息”、“包含”，并输入对应的目标告警的定位信息关键字段。
118. 设置规则执行的时间，用户可根据需要选择规则的生效时间和生效周期。
“时间条件”中所有条件缺省为去勾选，表示规则在所有时间下生效。
119. 屏蔽后的告警可选择“丢弃”或显示在“被屏蔽告警”列表中。
创建事件屏蔽规则时，被屏蔽的事件只能被丢弃。
120. 设置规则的优先级，当两条屏蔽规则屏蔽同一条告警时，优先级高的规则生效。
121. 选择是否启用规则，单击“确定”完成配置。
相关任务
122. 若需要重新接收已屏蔽的告警/事件，在主菜单中选择“集中告警 > 集中告警 > 告警设置”。在左侧导航树中选择“屏蔽规则”，在屏蔽规则列表中选择要停止的屏蔽规则，单击“删除”或“停止”。“删除”将永久删除该屏蔽规则；“停止”将停止执行该屏蔽规则，在需要重新启用屏蔽规则时可单击“启用”。
123. 用户可导入/导出屏蔽规则，以便统计、审核、迁移规则。在“屏蔽规则”页面中，单击“导入规则”或“导出规则”进行操作。
- 导入的文件格式要求是XLS，大小不能超过1M。最多导入200条规则，若导入的规则名称已存在，则替代原有规则。
- 导出文件已隐藏第一行，请不要修改第一行的文字，否则会导致导入失败。
任务示例
场景描述
运维人员计划于20XX年4月的每周一凌晨1点对设备A进行调测操作，每次调测在2个小时以内完成，设备A承载的业务届时会中断。这个期间产生的告警是可预测到的，且不需要被处理，需要配置屏蔽规则屏蔽这些告警，以避免这些告警被关注。
配置步骤
124. 在“屏蔽规则”页面中，单击“创建”，选择“告警屏蔽规则”。
125. 设置“规则名称”。
126. 选择“自定义告警源”，在自定义告警源中添加设备A。
127. 在“时间条件”区域中，设置“生效时间”为“20XX-04-01 00:00:00到20XX-04-30 23:59:59”，设置“生效周期”的“按天”为“星期一”，“按时段”为“01:00到03:00”。
128. 在“动作”区域中，选择“屏蔽后的告警”为“显示在“被屏蔽告警”中”。
129. 其他参数“条件”、“告警级别”、“启用”保持缺省设置。
130. 单击“确定”。
操作结果
设备A在20XX年4月的每周一凌晨1~3点产生的告警在“当前告警”、“告警日志”、“历史告警”中均不可见。在“被屏蔽告警”页面可查看到这些被屏蔽的告警。
5.2.17.11 CCS