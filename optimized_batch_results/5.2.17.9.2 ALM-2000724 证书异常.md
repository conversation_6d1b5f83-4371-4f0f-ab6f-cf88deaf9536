# ********.2 ALM-2000724 证书异常

告警解释
当任务中心证书即将过期、已过期、证书无效或不存在时产生此告警。
告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 2000724 | 紧急 | 是 |
告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
对系统的影响
任务中心服务不可用。
可能原因
- 阈值为1：证书即将过期。
- 阈值为2：证书过期。
- 阈值为3：证书无效或者证书不存在。
前提条件
- 已获取根证书、签名后证书以及生成证书库时输入的密码。例如，根证书名称为ca.crt，签名后证书为server.crt。
- 已准备跨平台远程访问工具，如“PuTTY”。
- 已准备网络传输工具，如“WinSCP”。
- 已获取待更换证书的任务中心服务节点的管理IP地址，apitask和root帐户的登录密码。
“root”用户的默认密码是“*****”，“apitask”用户的默认密码是“*****”，“server.keystore”证书的默认密码为“*****”。
操作步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
5. 使用“PuTTY”工具，通过4中获取的节点IP地址登录产生告警的节点。
默认帐号：apitask，默认密码：*****。
6. 使用网络传输工具（例如“WinSCP”工具），将获取的证书上传到任务中心服务节点的“/home/<USER>
7. 执行如下命令，并按提示输入“root”用户的密码，切换到“root”用户。
sudo su - root
8. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
9. 执行以下命令，进入证书库所在目录。
cd /opt/taskcenter/taskcenter-service/resources/keystore
10. 执行以下命令，备份原有证书库。
mv server.keystore server.keystore.bak
11. 执行以下命令，生成新的证书库文件server.keystore。
cd /home/<USER>
source /etc/profile
openssl pkcs12 -export -in 证书名称 -inkey 私钥名称 -out server.keystore -name tomcat_server
例如：
openssl pkcs12 -export -in server.crt -inkey server.key -out server.keystore -name tomcat_server
12. 根据提示两次输入证书库密码，该密码必须和私钥文件密码一致。
13. 执行以下命令，导入根证书。
keytool -import -v -trustcacerts -alias ca_root -file CA证书名称 -keystore server.keystore
例如导入server.crt：
keytool -import -v -trustcacerts -alias ca_root -file ca.crt -keystore server.keystore
显示如下信息：
Enter keystore password:
14. 输入证书库密码，按“Enter”。
15. 输入“yes”，按“Enter”。
显示如下信息时，完成CA证书的导入：
Certificate reply was installed in keystore
[Storing server.keystore]
16. 执行以下命令，并按提示输入证书库密码，检查证书是否导入成功。
keytool -list -v -keystore server.keystore
回显中包含如下信息时，表示证书正确导入。
Your keystore contains 2 entries
如果回显信息显示keytool命令不存在，则需要执行命令source /etc/profile导入环境变量。
17. 执行以下命令，修改证书库权限。
chmod 600 server.keystore
chown apitask:apitask server.keystore
18. 执行以下命令，将导入证书后的证书库server.keystore拷贝到keystore文件夹下。
cp /home/<USER>/server.keystore /opt/taskcenter/taskcenter-service/resources/keystore
19. 执行以下命令，删除“/home/<USER>
rm server.keystore ca.crt server.crt
20. 执行以下命令，加密私钥密码。
cd /opt/taskcenter/taskcenter-service/bin
sh kspass.sh ks
显示如下信息：
Please input the keystore password:
Please input the keystore password again:
Please input the key password (Press 'ENTER' if same as keystore password):
21. 输入证书库密码和私钥密码，按“Enter”。
回显信息如下时，表示修改成功。
Result: success.
随机生成的私钥密码的密文同时保存在任务中心服务配置文件中。
22. 执行如下命令，切换到“apitask”用户。
su - apitask
默认密码：*****
23. 执行以下命令，重启任务中心服务。
sh stop.sh
sh startup.sh
24. 执行以下命令，查看任务中心进程是否正常。
ps -ef | grep taskcenter | grep -v grep
回显类似如下，所示则表示进程正常。
apitask 13773 1 0 11:47 ? 00:00:15 /opt/common/jre/bin/java -Dname=taskcenter -classpath ...
- 是，证书替换成功，执行25。
- 否，请联系技术支持工程师协助解决。
25. 观察告警是否消除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
参考信息
无。
< 上一节