# 5.2.4.19.2 ALM-0004000700010009 IAM鉴权失败

ALM-0004000700010009 IAM鉴权失败的告警，仅在ManageOne 6.5.1.SPC200及后续版本支持。
##### 告警解释
服务监控定时5分钟巡检调用IAM鉴权Token接口，如果连续3次接口返回失败响应，则产生该告警。鉴权Token接口失败可能导致在ManageOne运营面登录、创建、修改、删除操作失败及云服务等业务到IAM鉴权失败。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0004000700010009 | 重要 | 业务质量告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 节点IP地址 | 被检测IAM鉴权失败所在服务器节点IP。 |
##### 对系统的影响
- 可能导致在ManageOne运营面登录、创建、修改、删除操作失败。
- 可能导致客户云服务（ECS、EVS、OBS等）创建、重启、扩容、迁移等操作失败。
##### 可能原因
- IAMCacheProxyService服务异常无法正常生成Token。
- IAMCacheProxyService服务连接Redis失败或操作Redis失败。
- IAMCacheProxyService服务连接数据库失败或操作数据库失败。
##### 处理步骤
1. 使用PuTTY，登录告警发生所在节点。在该告警的“定位信息”列获取告警发生的节点IP。
默认账号：sopuser，默认密码：*****。
2. 执行如下命令，切换root用户。
sudo su root
默认密码：*****。
3. 执行如下命令，重启IAMCacheProxyService服务尝试恢复业务。
su ossadm -c "/opt/oss/manager/agent/bin/ipmc_adm -cmd restartapp -app IAMCacheProxyService"
- 回显信息如下所示，表示重启服务成功。
等待5-10分钟，查看告警是否清除。
- 清除，结束。
- 未清除，执行4。
Stopping process iamcacheproxy-x-x ... success
Starting process iamcacheproxy-x-x ... success
- 否则，表示重启服务失败，执行4。
4. 执行如下命令，收集IAMCacheProxyService服务运行日志信息。
tar -zcvf /tmp/IAMCacheProxyService-节点ip地址-log.tar.gz /var/log/oss/Product/IAMCacheProxyService/iamcacheproxy*/log/root.log
5. 执行如下命令，收集IAMCoreService服务运行日志信息。
tar -zcvf /tmp/IAMCoreService-节点ip地址-log.tar.gz /var/log/oss/Product/IAMCoreService/root.log
6. 根据4和5收集的日志信息，联系技术支持工程师协助解决。
告警清除
此告警修复后，系统会自动清除此告警。
告警参考
无。
< 上一节
5.2.6 组合API
******* 组合API