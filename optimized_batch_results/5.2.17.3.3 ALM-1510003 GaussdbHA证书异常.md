# ********.3 ALM-1510003 GaussdbHA证书异常

##### 告警解释
证书过期、无效和证书即将过期时产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1510003 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
数据库证书异常或者证书即将过期。
##### 可能原因
- 阈值为1：证书即将过期。
- 阈值为2：证书过期。
- 阈值为3：证书无效或者证书不存在。
前提条件
- 已获取对应服务的数据库主备节点IP。
- 已获取需要替换的证书，如果没有及时获取可以暂时使用GaussdbHA发布的默认证书。
一套证书应该包含5个证书文件：
- cacert.pem表示签发服务器证书和客户端证书的根证书文件；
- server.crt表示服务端证书文件；
- server.key表示服务端私钥文件；
- client.crt表示客户端证书文件；
- client.key表示客户端私钥文件。
目前GaussdbHA仅支持到三级证书。
- 已经获取证书的其他相关信息，获取的相关信息以变量表示，在后续步骤中会引用相关的变量请注意按实际信息替换。
| 表1 变量说明 | 表1 变量说明 | 表1 变量说明 |
| --- | --- | --- |
| 变量名 | 变量说明 | 默认证书中的值 |
| CERT_PATH | 表示新证书上传到环境上的临时路径，本文档以/home/<USER>/certs 为例。需要保证目录有上传文件的权限。 | 以实际规划为准 |
| CERT_CN | 表示证书生成时的组织信息。 | www.huawei.com |
| CERT_CLIENT_KEY_PWD | 表示客户端证书私钥的保护密码，可以为空。 | ***** |
| CERT_SERVER_KEY_PWD | 表示服务端证书私钥的保护密码，可以为空。 | ***** |
- 已准备跨平台远程访问工具，如 “PuTTY”。
- 已准备网络传输工具，如 “WinSCP”。
- 已获取对应服务的数据库安装用户的密码，root用户的密码。
##### 处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
5. 使用“PuTTY”工具，通过4中获取的节点IP地址登录产生告警的节点。
默认帐号：gaussdb，默认密码：*****。
6. 执行如下命令，并按提示输入“root”用户的密码，默认密码：*****，切换到“root”用户。
sudo su - root
7. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
8. 执行以下命令，查询数据库的版本号，确认当前版本是否支持证书替换。
cat /opt/gaussdb/version.json
当前仅高于1.1.9（包含1.1.9）的版本支持替换证书，低版本不需要执行证书替换。
上传证书和停止数据库服务。
9. 使用“PuTTY”工具，分别登录数据库的两个节点。
默认帐号：gaussdb，默认密码：*****。
10. 切换到root用户下执行如下命令，创建保存新证书的临时目录。
CERT_PATH='/home/<USER>/certs'
mkdir -p $CERT_PATH
chmod 777 $CERT_PATH
命令中变量CERT_PATH已经在前提条件中获取，需要替换为对应值，需要保证规划的目录有上传文件的权限。
11. 使用网络传输工具（例如“WinSCP”），将证书文件上传到/home/<USER>/certs目录下，上传目录为10中创建的实际规划目录。
12. 使用“PuTTY”工具，登录数据库的两个节点，切换到root用户下执行如下命令，确认数据库主备节点。
service gaussdb query|grep LOCAL_ROLE
如果回显信息中包含 Standby 信息则表示为数据库备节点，如果回显信息中包含 Primary 信息则表示为数据库主节点。
13. 根据获取的主备节点信息，分别在数据库备节点、主节点执行如下命令，停止数据库。
source /etc/profile
haStopAll -a
替换数据库节点的证书
14. 替换数据库证书，包含替换数据库主备节点的证书，分别在数据库备节点、主节点执行如下步骤替换对应节点的证书。
登录数据库节点，在root用户下执行如下命令替换证书文件，命令中变量CERT_PATH、CERT_CN替换为在前提条件中获取的实际值。变量替换完成后，复制如下命令并一起执行。
unset HISTFILE
CERT_PATH='/home/<USER>/certs'
CERT_CN='www.huawei.com'
alias cp='cp'
mkdir -p /opt/backup/cert_old
cp -fr /opt/gaussdb/data/certs/* /opt/backup/cert_old/
rm -rf /opt/gaussdb/data/certs/*
cp -fr $CERT_PATH/* /opt/gaussdb/data/certs/
cp /opt/gaussdb/data/certs/server* /opt/gaussdb/data/db/
cp /opt/gaussdb/data/certs/cacert.pem /opt/gaussdb/data/db/
su - dbadmin -c "gs_guc set -c repl_force_cert_check=\"'repl_All_peer_cn=$CERT_CN'\""
chown dbadmin: /opt/gaussdb/data/certs/ -R
chmod 700 /opt/gaussdb/data/certs
chmod 600 /opt/gaussdb/data/certs/*
15. 如果在前提条件中获取的CERT_SERVER_KEY_PWD信息不为空，在root用户下执行如下命令生成服务端证书密码文件，复制如下命令并一起执行，命令执行过程中按提示输入CERT_SERVER_KEY_PWD信息。
unset HISTFILE
rm -rf /opt/gaussdb/data/db/server.key.rand
rm -rf /opt/gaussdb/data/db/server.key.cipher
su - dbadmin -c "gs_guc encrypt -M server -k"
16. 如果在前提条件中获取的CERT_SERVER_KEY_PWD信息不为空，执行如下命令替换服务端证书密码文件，复制如下命令并一起执行。
cp /opt/gaussdb/data/db/server.key.cipher /opt/gaussdb/data/certs/
cp /opt/gaussdb/data/db/server.key.rand /opt/gaussdb/data/certs/
chown dbadmin: /opt/gaussdb/data/certs/ -R
chmod 700 /opt/gaussdb/data/certs
chmod 600 /opt/gaussdb/data/certs/*
17. 如果在前提条件中获取的CERT_CLIENT_KEY_PWD信息不为空，在root用户下执行如下命令生成客户端证书密码文件，复制如下命令并一起执行，命令执行过程中按提示输入CERT_CLIENT_KEY_PWD信息。
unset HISTFILE
rm -rf /opt/gaussdb/data/db/client.key.rand
rm -rf /opt/gaussdb/data/db/client.key.cipher
su - dbadmin -c "gs_guc encrypt -M client -k"
18. 如果在前提条件中获取的CERT_CLIENT_KEY_PWD信息不为空，执行如下命令替换客户端证书密码文件，复制如下命令并一起执行。
cp /opt/gaussdb/data/db/client.key.rand /opt/gaussdb/data/certs/
cp /opt/gaussdb/data/db/client.key.cipher /opt/gaussdb/data/certs/
rm -rf /opt/gaussdb/data/db/client.key.rand
rm -rf /opt/gaussdb/data/db/client.key.cipher
chown dbadmin: /opt/gaussdb/data/certs/ -R
chmod 700 /opt/gaussdb/data/certs
chmod 600 /opt/gaussdb/data/certs/*
重启数据库服务
19. 分别在数据库主节点、备节点执行如下命令。
source /etc/profile
haStartAll -a
20. 在数据库主节点执行如下命令，检查证书替换后数据库状态是否恢复正常。
source /etc/profile
service had query
service gaussdb query
Ha state:
LOCAL_ROLE                     : Primary
STATIC_CONNECTIONS             : 1
DB_STATE                       : Normal
DETAIL_INFORMATION             : Normal
Senders info:
SENDER_PID                     : 22772
LOCAL_ROLE                     : Primary
PEER_ROLE                      : Standby
PEER_STATE                     : Normal
STATE                          : streaming
SENDER_SENT_LOCATION           : 0/B71326C8
SENDER_WRITE_LOCATION          : 0/B71326C8
SENDER_FLUSH_LOCATION          : 0/B71326C8
SENDER_REPLAY_LOCATION         : 0/B71326C8
RECEIVER_RECEIVED_LOCATION     : 0/B71326C8
RECEIVER_WRITE_LOCATION        : 0/B71326C8
RECEIVER_FLUSH_LOCATION        : 0/B71326C8
RECEIVER_REPLAY_LOCATION       : 0/B712D138
SYNC_PERCENT                   : 99%
SYNC_STATE                     : async
SYNC_PRIORITY                  : 0
CHANNEL                        : 192.168.0.0-->192.168.1.0
Receiver info:
No information
命令查询结果类似上图表示证书替换后数据库服务正常。
删除上传的证书文件
21. 登录数据库主备节点执行如下命令。
rm -f /home/<USER>/certs/*
/home/<USER>/certs/为本文档中的证书文件上传路径，需要以实际操作中的上传路径为准。
22. 观察告警是否清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。