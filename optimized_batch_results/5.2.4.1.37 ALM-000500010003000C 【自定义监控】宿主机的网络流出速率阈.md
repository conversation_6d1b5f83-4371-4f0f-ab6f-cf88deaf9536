# 5.2.4.1.37 ALM-000500010003000 未知告警

##### 告警解释
当性能监控检测到宿主机的网络流出速率超过管理员设置的阈值时，产生此通知信息。
阈值需要在“监控中心 > 监控配置 > 性能阈值维护”中手工设置。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 000500010003000C | 紧急/重要/次要/提示 | 业务质量告警 |
性能监控阈值通知共有四个级别，分别是：紧急、重要、次要和提示。四个级别的通知处理方式一致。
##### 告警参数
| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 宿主机 | 虚拟化类型 | 宿主机的虚拟化类型。 |
| 宿主机 | 主机组 | 主机组名称。 |
| 宿主机 | 管理IP地址 | 宿主机的管理IP地址。 |
| 宿主机 | 宿主机名称 | 宿主机的名称。 |
##### 对系统的影响
可能会造成系统运行速度慢或网络故障。
##### 可能原因
网络流出速率告警阈值设置过低。
主机网络繁忙，传输流量过大。
网络风暴，安全攻击。
##### 处理步骤
1. 检查监控指标的阈值设置。
- 在主菜单中选择“监控中心 > 监控配置 > 性能阈值维护”。
- 在“性能阈值维护”页面，找到对应监控指标的阈值。
- 单击对应监控指标的，查看阈值条件设置是否合理（阈值设置的参考值分别为：重要 90%，次要 80%）。
- 是：执行3。
- 否：执行2。
2. 修改指标阈值策略。
- 根据实际指标数据修改阈值。
- 等待5分钟，在主菜单选择“集中告警 > 当前告警”，检查通知信息是否清除。
- 是：处理完毕。
- 否：执行3。
3. 业务正常情况下，联系业务运维人员进行扩容、迁移业务，其他情况请联系技术工程师协助解决。
告警清除
修复后，系统会自动清除此通知信息，无需手工清除。
##### 参考信息
无