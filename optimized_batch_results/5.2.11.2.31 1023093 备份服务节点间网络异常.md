# 5.2.11.2.31 1023093 备份服务节点间网络异常

5.2.11.2.31 1023093 备份服务节点间网络异常
告警解释
当网络通信丢包率或者网络延迟过高时，上报此告警。
告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1023093 | 重要 | 是 |
告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 设备名称 | 用于网络通信的网络设备名称。 |
| 目标IP | 与当前节点通信异常的目标节点IP。 |
| 节点IP | 产生告警的节点IP。 |
| 故障原因 | 导致上报告警的网络状态检测结果。 |
| 额外信息 | 网络状态检测结果的说明信息。 |
对系统的影响
可能会造成系统响应超时，甚至影响业务。
可能原因
- 虚拟机状态异常。
- ip地址不存在。
- 防火墙禁用访问。
- 路由设置不当。
- 网络设备故障。
处理步骤
- 可能原因：虚拟机状态异常。
- 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
- 在“集中告警”页面，查看当前告警的“区域”。
- 在“运维地图”页面右边的“快速访问”导航栏中，单击“Service OM”，选择区域后进入Service OM界面。
- 选择“资源 > 计算资源 > 虚拟机”。
- 按IP搜索“节点IP”对应的虚拟机。
- 确认虚拟机的状态、电源状态是否均为运行中。
- 否，请执行1.g。
- 是，请执行2。
- 恢复虚拟机的电源并重启虚拟机。
等待节点系统启动后，再等待一分钟，查看告警是否恢复。
- 是，处理结束。
- 否，请执行2。
- 可能原因：ip地址不存在。
- 单击“更多 > VNC登录”登录Karbor节点。
- 执行ifconfig命令检查是否存在ip地址。
- 否，执行service network restart命令重启网络，查看告警是否恢复。
- 是，请执行3。
- 可能原因：防火墙禁用访问。
执行iptables -nL命令检查防火墙规则，查看告警定位信息“目标IP”所在网段是否被禁用。
- 是，执行iptables命令删除禁用的条目。可执行iptables -h命令查看该命令的详细使用说明。
- 否，请执行4。
- 可能原因：路由设置不当。
执行route -n命令，请收集命令执行结果后联系技术支持工程师协助解决。
- 可能原因：网络设备故障。
请联系技术支持工程师协助解决。
参考信息
无。