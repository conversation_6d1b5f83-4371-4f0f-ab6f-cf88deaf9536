# 5.2.11.2.26 1023097 磁盘使用率超过阈值

5.2.11.2.26 1023097 磁盘使用率超过阈值
告警解释
当磁盘空间占用率超过指定阈值时，上报此告警。
告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1023097 | 重要 | 是 |
告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 分区名 | 使用率超过阈值的磁盘分区名。 |
| 节点IP | 产生告警的节点IP。 |
| 总量 | 磁盘分区总量。 |
| 已使用 | 磁盘分区已使用量。 |
| 阈值 | 产生告警需要超过磁盘分区已使用/总量的百分比。 |
| 清除阈值 | 清除告警需要低于磁盘分区已使用/总量的百分比。 |
对系统的影响
可能会造成系统的性能下降，并且无法存储系统新产生的数据。
可能原因
磁盘上存储的文件占用空间过大。
处理步骤
- 可能原因：磁盘上存储的文件占用空间过大。
- 查看告警信息，确定有哪些分区的使用率过大。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行以下命令，进入使用率过大的分区。
cd 使用率过大的分区路径
- 执行以下命令，清理无用文件。
rm -rf 无用文件名字
- 执行以下命令，查看分区使用率。
df -h
显示每个分区的空间使用情况，查看第6列（Mounted on），分区目录对应的第5列（Use%），已用磁盘空间是否超过告警阈值。
- 是，请执行1.e。
- 否，请执行1.g。
- 根据实际情况清理分区，再次执行df -h查看是否还有分区的使用率超过告警阈值。
- 是，请执行1.d。
- 否，请执行1.h。
- 等待1分钟，检查告警是否自动恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
参考信息
无。