# 5.2.4.8.9 ALM-999999998 License销售项达到或超过100%阈值

##### 告警解释
当License销售项使用率达到或超过100%阈值时，产生该告警。当License销售项使用率小于100%阈值时，该告警会自动清除。
该告警仅适用于3.00版本的License文件。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 999999998 | 重要 | 业务质量告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| SALE | 销售项ID。 |
| 阈值 | 销售项的容量阈值，此处阈值=100%。 |
| 容量 | License限定销售项可使用的最大数量。 |
| 使用量 | 当前已消耗的销售项数量。 |
##### 对系统的影响
该告警发生时，若不及时处理，当License销售项的使用达到最大限制值时，则不能继续接入License资源。
##### 可能原因
License销售项使用率达到或超过100%阈值。
##### 处理步骤
1. 在当前告警列表中查看此告警的定位信息，确认并记录定位信息中显示的License销售项的ID值。
2. 查看1中确认的License销售项使用率是否达到或超过100%阈值。
- 在主菜单选择“系统管理 > 系统设置 > License管理”。
- 在左侧导航树中选择“License信息”。
- 在“License信息”页面的“销售信息项”页签中，根据1中所记录的License销售项的ID值进行搜索，确认License销售项使用率是否达到或超过100%阈值。
- 是，执行3。
- 否，联系华为技术支持工程师。
3. 申请License文件。
- 获取License失效码。
- 执行此操作需要具备“失效License”的操作权限。
- 失效License的操作不可恢复。设置当前License失效后，该License进入宽限期，宽限期参见License文件。宽限期后该License将无法使用。
- 在左侧导航树中选择“License文件”。
- 单击目标License文件“操作”列的“失效License”。
- 在“确认”对话框中，单击“确定”。
- 在License文件列表中，单击目标License文件的查看并获取“失效码”。
- 联系华为技术支持工程师使用获取的License失效码申请新的License文件。
4. 更新License文件。
- 在左侧导航树中选择“更新License”。
- 在“更新License”页面中，单击“License文件”后面的。
- 选择已申请的License文件，单击“打开”。
- 单击“上传”，查看License更新前后的结果对比。
- 单击“应用”，立即应用新的License文件。
5. 查看本告警是否已清除。
- 是，告警处理结束。
- 否，请收集上述告警处理过程中的信息，联系华为技术支持工程师。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。