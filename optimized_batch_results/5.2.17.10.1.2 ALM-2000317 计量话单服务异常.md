# *********.1 ALM-2000317 计量话单服务异常

*********.1.2 ALM-2000317 计量话单服务异常
告警解释
计量话单健康检查异常，就会产生此告警。
告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 2000317 | 紧急 | 是 |
告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
对系统的影响
话单文件可能无法正常生成。
可能原因
计量话单进程未启动或者数据库连接异常。
处理步骤
1. 使用PuTTY，登录PUB-DB01节点。
默认帐号：gaussdb，默认密码：*****
2. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
3. 执行以下命令，输入root密码，切换到root用户下。
sudo su - root
执行以下命令，查询数据库状态。
service had query
- 数据库正常，请执行4。
- 数据库异常，请联系技术支持工程师协助解决。
4. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
5. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
6. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
7. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
8. 使用“PuTTY”工具，通过7中获取的节点IP地址登录产生告警的节点。
默认帐号：meteradmin，默认密码：*****。
9. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
10. 执行以下命令，查询服务进程。
ps -ef |grep meteradmin
- 存在，执行11后再执行13。
- 不存在，执行12。
11. 执行以下命令，重启服务进程。
sh /home/<USER>/meterticket-*/bin/stop.sh
sh /home/<USER>/meterticket-*/bin/startup.sh
12. 执行以下命令，启动服务进程。
sh /home/<USER>/meterticket-*/bin/startup.sh
13. 执行如下命令，检查服务进程运行是否正常。
ps -ef |grep meteradmin
- 是，执行14。
- 否，请联系技术支持工程师协助解决。
14. 等待10分钟，检查告警是否已自动清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。