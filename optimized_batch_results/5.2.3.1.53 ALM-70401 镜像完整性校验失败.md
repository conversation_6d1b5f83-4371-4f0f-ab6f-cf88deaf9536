# 5.2.3.1.53 ALM-70401 镜像完整性校验失败

##### 告警解释
创建虚拟机或创建镜像卷时，需要先下载镜像。下载后需要对镜像进行完整性校验，如果校验失败，则产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70401 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 镜像uuid：产生告警的镜像uuid |
| 附加信息 | 镜像名称：产生告警的镜像名称<br>证书uuid：镜像对应的证书uuid<br>证书名称：镜像对应的证书名称 |
##### 对系统的影响
此告警产生时，创建虚拟机或系统卷失败。
##### 可能原因
镜像被非法篡改。
##### 处理步骤
1. 登录Service OM界面。
具体操作请参见登录和注销Service OM界面。
##### 2. 选择“监控 > 告警 > 告警列表 > OpenStack告警”，获取告警参数中的镜像uuid。
3. 选择“资源 > 镜像资源 > 镜像列表”界面，定位到镜像所在行，单击“删除”，删除镜像。
4. 检查告警是否消除。
- 是，任务结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。