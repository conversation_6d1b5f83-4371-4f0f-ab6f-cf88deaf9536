# 5.2.4.8.5 ALM-999999990 软件服务年费已经过期

告警解释
当前时间已经超过License文件软件升级截止日期时，产生该告警。当前时间在License文件软件升级截止日期的使用期限内时，该告警会自动清除。
- 发生此告警之前都会发生一条ALM-999999989 软件服务年费即将过期。上报此告警后，ALM-999999989 软件服务年费即将过期的“清除状态”自动更新为“已清除”。
- 软件服务年费的期限以有效期为准。
- 该告警适用于License 2.00和License 3.00且包含软件服务年费信息的License文件。
告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 999999990 | 紧急 | 业务质量告警 |
告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 产品名称 | 产品名称。 |
对系统的影响
该告警发生时，若不及时处理，影响软件支持和升级服务。
可能原因
当前License文件软件服务年费已经过期。
处理步骤
1. 在主菜单选择“系统管理 > 系统设置 > License管理”。
2. 在“License信息”页面中，查看当前时间是否已经达到或超过License文件的软件升级截止日期。
- 是，执行3。
- 否，请联系华为技术支持工程师处理。
3. 申请License文件。
- 获取License失效码。
- 执行此操作需要具备“失效License”的操作权限。
- 失效License的操作不可恢复。设置当前License失效后，该License进入宽限期，宽限期参见License文件。宽限期后该License将无法使用。
- 在左侧导航树中选择“License文件”。
- 单击目标License文件“操作”列的“失效License”。
- 在“确认”对话框中，单击“确定”。
- 在License文件列表中，单击目标License文件的查看并获取“失效码”。
- 联系华为技术支持工程师使用获取的License失效码申请新的License文件。
4. 更新License文件。
- 在左侧导航树中选择“更新License”。
- 在“更新License”页面中，单击“License文件”后面的。
- 选择已申请的License文件，单击“打开”。
- 单击“上传”，查看License更新前后的结果对比。
- 单击“应用”，立即应用新的License文件。
5. 查看本告警是否清除。
- 是，告警处理结束。
- 否，请收集上述告警处理过程中的信息，联系华为技术支持工程师处理。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
参考信息
无。