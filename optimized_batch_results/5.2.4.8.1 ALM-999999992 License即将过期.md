# 5.2.4.8.1 ALM-999999992 License即将过期

告警解释
当License文件的剩余使用天数小于等于到期提醒时间（默认为15天）时，产生该告警。当License文件的剩余使用天数大于到期提醒时间或License文件已经过期时，该告警会自动清除。
该告警适用于License 2.00和License 3.00所有文件类型的License文件。
告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 999999992 | 重要 | 业务质量告警 |
告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| LSN | License序列号。 |
对系统的影响
该告警发生时，若不及时处理，License将会过期。License过期后用户将无法正常登录系统。
可能原因
当前License文件的剩余使用天数小于等于到期提醒时间。
处理步骤
1. 重新登录系统，在拦截页面查看当前License文件的剩余使用天数，确认当前License文件的剩余使用天数是否小于等于设置的到期提醒时间。
- 是，执行2。
- 否，请联系华为技术支持工程师处理。
2. 申请License文件。
- 获取License失效码。
- 执行此操作需要具备“失效License”的操作权限。
- 失效License的操作不可恢复。设置当前License失效后，该License进入宽限期，宽限期参见License文件。宽限期后该License将无法使用。
- 在主菜单选择“系统管理 > 系统设置 > License管理”。
- 在左侧导航树中选择“License文件”。
- 单击目标License文件“操作”列的“失效License”。
- 在“确认”对话框中，单击“确定”。
- 在License文件列表中，单击目标License文件的查看并获取“失效码”。
- 联系华为技术支持工程师使用获取的License失效码申请新的License文件。
3. 更新License文件。
- 在左侧导航树中选择“更新License”。
- 在“更新License”页面中，单击“License文件”后面的。
- 选择已申请的License文件，单击“打开”。
- 单击“上传”，查看License更新前后的结果对比。
- 单击“应用”，立即应用新的License文件。
4. 查看该告警是否已清除。
- 是，告警处理结束。
- 否，请收集上述告警处理过程中的信息，联系华为技术支持工程师。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
参考信息
无。