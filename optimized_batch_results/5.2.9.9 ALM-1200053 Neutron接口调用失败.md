# ******* ALM-1200053 Neutron接口调用失败

******* ALM-1200053 Neutron接口调用失败
告警解释
系统每隔30秒检查一次VPC Service节点与FusionSphere OpenStack节点的通信状态，如果连续3次持续不通，产生此告警。
告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200053 | 重要 | 是 |
告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源系统 | 告警来源。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 此条告警信息最近发生时间。 |
| 定位信息 | 产生告警的定位信息。 |
| 附加信息 | 告警附加信息。 |
对系统的影响
VPC Service节点无法与FSP节点通信，将导致网络配置无法处理，需尽快处理。
可能原因
- VPC Service节点的网络不通。
- FusionSphere OpenStack Neutron服务异常。
处理步骤
1. 参考ALM-73203 组件故障和ALM-73201 HAProxy代理服务不可用章节，排查是否为FusionSphere OpenStack Neutron服务异常导致的告警。
- 是，请参考对应章节进行处理。
- 否，可能原因为VPC Service节点的网络不通，执行2进行处理。
- 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
- 单击“登录”。
- 在页面上方的菜单栏，选择“集中告警”。
- 在告警列表中，找到待处理的告警记录，单击“名称”列，弹出“告警详情和处理建议”窗口。
- 在“告警详情”页的“定位信息”中，“主机IP”即告警节点IP地址。
当出现多条相同告警时，告警信息被汇聚。此时在“被汇聚告警”页签查看告警节点IP地址。
- 使用“PuTTY”，登录告警节点。
默认帐号： vpc，默认密码： *****
- 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
- 执行以下命令，获取neutron域名。
cat /home/<USER>/network/WEB-INF/classes/config/networkConfig.xml | grep neutron-endpoint
回显如下所示。
[vpc@PUB-SRV01 ~]$cat /home/<USER>/network/WEB-INF/classes/config/networkConfig.xml | grep
neutron-endpoint
<neutron-endpoint>https://network.az0.dc0.type2env.network.com:443&lt;/neutron-endpoint>
其中，neutron域名为network.az0.dc0.type2env.network.com。
- 执行以下命令，PING9中获取到的neutron域名，查看是否可以PING通。
ping neutron域名
举例：
ping network.az0.dc0.type2env.network.com
回显如下所示，表示可以PING通。
[vpc@PUB-SRV01 ~]$ ping network.az0.dc0.type2env.network.com
PING network.az0.dc0.type2env.network.com (**************) 56(84) bytes of data.
64 bytes from ************** (**************): icmp_seq=1 ttl=63 time=0.151 ms
64 bytes from ************** (**************): icmp_seq=2 ttl=63 time=0.195 ms
64 bytes from ************** (**************): icmp_seq=3 ttl=63 time=0.196 ms
^C
--- network.az0.dc0.type2env.network.com ping statistics ---
3 packets transmitted, 3 received, 0% packet loss, time 2001ms
rtt min/avg/max/mdev = 0.151/0.180/0.196/0.026 ms
- 如果neutron域名无法PING通，请执行11，继续排查本节点的DNS是否设置正确。
- 如果neutron域名可以PING通，请联系技术支持工程师协助解决。
- 排查本节点的DNS是否设置正确。
- 执行以下命令，查看DNS配置。
cat /etc/resolv.conf | grep nameserver
回显如下所示。
[vpc@PUB-SRV01 ~]$ cat /etc/resolv.conf | grep nameserver
nameserver 192.168.109.124
nameserver 192.168.109.125
- 与LLD中规划的DNS-Internal节点信息比对。
- 如果不一致，按照LLD中规划信息修改resolv.conf文件。
- 如果一致，说明DNS设置正确，请联系技术支持工程师协助解决。
- 执行以下命令，输入节点root密码，修改DNS配置。
sudo vim /etc/resolv.conf
- 按Insert键开始修改文件内容。
- 按Esc键，输入:wq并回车，保存文件的修改。
- 再次尝试PING neutron域名，如果仍然无法PING通，请联系技术支持工程师协助解决。
- 使用“PuTTY”，登录FusionSphere OpenStack节点。
默认帐号： fsp，默认密码： *****
- 导入环境变量。
具体操作请参见导入环境变量章节。
- 执行下面命令，其中域名使用9中获取的neutron域名。
curl -i --insecure -X GET 'https://neutron域名:443' -H "Accept: application/json"
举例：
curl -i --insecure -X GET 'https://network.az0.dc0.type2env.network.com:443' -H "Accept: application/json"
回显如下所示。
[vpc@PUB-SRV01 ~]$ curl -i --insecure -X GET 'https://network.az0.dc0.type2env.network.com:443' -H "Accept: application/json"
HTTP/1.1 200 OK
如果该接口返回码非200，请联系技术支持工程师协助解决。
- 等待3分钟后检查告警是否自动清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
参考信息
无。