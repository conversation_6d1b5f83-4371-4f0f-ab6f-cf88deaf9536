# 5.2.9.12.1 5.2.9.12.1 导入环境变量

5.2.9.12.1 导入环境变量
操作场景
系统在安装完成后，默认打开鉴权模式，在执行CPS及OpenStack命令前，需要先导入环境变量才能操作。
系统提供了导入环境变量的快捷脚本，但需要先确认默认的环境变量是否与系统匹配。
前提条件
- 已登录AZ内任意一台主机。
- 已完成所有主机的安装。
- 已完成FusionSphere OpenStack的安装部署。
操作步骤
1. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：*****。
2. 执行以下命令，防止系统超时退出。
TMOUT=0
3. 确认是否直接执行导入当前系统保存的环境变量脚本。
默认环境变量如下：
export OS_USERNAME=dc1_admin
export OS_TENANT_NAME=dc_system_dc1
export OS_REGION_NAME=az1.dc1
export OS_AUTH_URL=https://identity.az1.dc1.domainname.com:443/identity/v2.0
export OS_PASSWORD=$password
export NOVA_ENDPOINT_TYPE=internalURL
export OS_ENDPOINT_TYPE=internalURL
export CINDER_ENDPOINT_TYPE=internalURL
export OS_VOLUME_API_VERSION=2
export BASE_BOND=brcps
环境变量中各参数含义可参考表1。
如果已修改默认环境变量，可通过cat /usr/bin/install_tool/set_env | grep export命令查看当前环境变量。
- 是，执行8。
- 否，执行4。
4. 执行以下命令，使用VI编辑器打开系统预置的环境变量脚本。
vi /usr/bin/install_tool/set_env
5. 按“i”进入编辑模式。
6. 修改环境变量中的以下斜体内容为实际配置的AZ名称和DC名称。
7. export OS_AUTH_URL=https://identity.az1.dc1.domainname.com:443/identity/v2.0
8. export OS_USERNAME=dc1_admin
9. export OS_TENANT_NAME=dc_system_dc1
export OS_REGION_NAME=az1.dc1
环境变量中的参数解释如表1所示。
| 表1 环境变量参数说明 | 表1 环境变量参数说明 |
| --- | --- |
| 参数 | 含义 |
| OS_AUTH_URL | 鉴权地址，对应Keystone服务的endpoint。<br>使用publicURL的endpoint。<br>需修改的部分包括AZ名、DC名、以及域名后缀。 |
| OS_USERNAME | 执行操作时使用的DC管理员的帐号。DC管理员在安装完成后自动创建，格式为dcname_admin。例如dcname为dc1，则DC管理员用户名为dc1_admin。 |
| OS_TENANT_NAME | 执行操作的DC管理员所属租户信息。租户信息在安装完成后自动创建，格式为dc_system_dcname。 |
| OS_REGION_NAME | AZ的域名，如az1.dc1。 |
| OS_ENDPOINT_TYPE | endpoint的类型，在使用OpenStack的命令时需要导入，请使用“internalURL”。 |
| NOVA_ENDPOINT_TYPE | endpoint的类型，在使用OpenStack的命令时需要导入，请使用“internalURL”。 |
| CINDER_ENDPOINT_TYPE | endpoint的类型，在使用OpenStack的命令时需要导入，请使用“internalURL”。 |
| OS_VOLUME_API_VERSION | 使用的VOLUME版本，需要输入2，表示使用V2版本。 |
10. 按“Esc”，输入“:wq”，并按“Enter”。
保存设置并退出VI编辑器。
11. 执行以下命令，并按提示输入“OS_USERNAME”的密码，导入环境变量。
source set_env
默认密码：“*****”。
回显如下类似信息：
...
Enter CPS_USERNAME=
12. 输入CPS鉴权用户“cps_admin”，并按“Enter”。
回显如下类似信息：
Enter CPS_PASSWORD=
13. 输入“cps_admin”的密码，并按“Enter”。
默认密码：“*****”。
输入完成后如果有cps host-list和nova list命令的自动回显信息，则表示环境变量导入成功。