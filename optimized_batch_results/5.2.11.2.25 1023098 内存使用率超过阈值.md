# ********.25 1023098 内存使用率超过阈值

********.25 1023098 内存使用率超过阈值
告警解释
当内存占用率超过指定阈值时，上报此告警。
告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1023098 | 重要 | 是 |
告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 节点IP | 产生告警的节点IP。 |
| 总量 | 内存总量。 |
| 已使用 | 内存已使用量。 |
| 阈值 | 产生告警需要超过内存已使用/总量的百分比。 |
| 清除阈值 | 清除告警需要低于内存已使用/总量的百分比。 |
对系统的影响
可能会造成系统运行速度慢。
可能原因
主机业务繁忙负载过重。
处理步骤
- 可能原因：主机业务繁忙负载过重。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行top命令，查看内存占用率过高的进程，并记录其PID。
- 执行kill PID命令强制结束进程。
- 等待若干分钟后，再次执行top命令，查看内存占用率是否明显下降。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
参考信息
无。