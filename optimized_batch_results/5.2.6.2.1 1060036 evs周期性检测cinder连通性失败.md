# 5.2.6.2.1 1060036 evs周期性检测cinder连通性失败

5.2.6.2.1 1060036 evs周期性检测cinder连通性失败
告警解释
EVS周期性检测cinder连通性失败。
告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1060036 | 重要 | 是 |
告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 告警ID | 告警ID | 告警ID |
| 通知类型 | 通知类型 | 告警 |
| 告警级别 | 告警级别 | 告警的严重级别：<br>1 紧急<br>2 重要<br>3 次要<br>4 提示 |
| 最近发生时间 | 最近发生时间 | 此条告警信息最近一次发生时间。 |
| 定位信息 | 云服务 | 云服务名 |
| 定位信息 | 主机地址 | 发送告警的主机地址 |
| 附加信息 | 云服务 | 云服务名 |
| 附加信息 | 服务 | 服务名 |
| 附加信息 | Cinder URL | 无法连通的Cinder节点的URL |
| 附加信息 | 错误信息 | 错误信息 |
对系统的影响
EVS与Cinder连接失败，EVS服务不可用。
可能原因
- Cinder 服务故障。
- Cinder的网络故障。
- Cinder URL的配置错误。
处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在上方导航栏选择“集中告警 > 当前告警”，单击“过滤”，通过告警名称过滤该告警。
3. 查询以下告警信息：
- IP地址
- 定位信息（主机地址）
- 最后发生时间
- 附加信息
4. 查询附加信息，若ErrorMessage中包含“statusCode is Unauthorized (401) - The request requires user authentication”，说明组合API节点配置帐号op_svc_evs在IAM中不存在或节点配置的帐号密码错误，请联系IAM运维人员，查看IAM中是否存在op_svc_evs帐号。
- 如果op_svc_evs帐号不存在，请向IAM运维人员提变更，新增该帐号并获取帐号密码；如果帐号存在，请获取该帐号的密码。
- 获取到帐号密码之后，根据修改配置文件中对接其他组件或服务的帐户密码章节，修改组合API节点iam.evs.password配置项的值。
- 配置修改成功后，等待6分钟后，如果告警已清除，则问题已解决，否则执行5。
5. 查询附加信息，检查Cinder URL是否正确。
- 若ErrorMessage中包含“java.net.ConnectException: Connection refused”、“java.net.NoRouteToHostException: No route to host (Host unreachable)”、“java.net.UnknownHostException:”、“Name or service not known”等描述与Cinder间连接不可达的信息，说明Cinder URL不正确，请申请配置变更，变更该节点的Cinder URL，具体操作可参考如下步骤：
- Region Type I场景，使用PuTTY，以“Cascading-Reverse-Proxy”字段（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）对应的IP地址登录级联层FusionSphere OpenStack节点。
Region Type II或Region Type III场景，使用PuTTY，以“Reverse-Proxy”（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）字段对应的IP地址登录FusionSphere OpenStack节点。
默认帐号：fsp，默认密码：*****
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行以下命令，导入环境变量。
source set_env
导入过程中，请输入1，选择环境变量“openstack environment variable (keystone v3)”，默认密码为*****。
- 执行以下命令查询cinder安装部署时的地址，服务名为cinder，接口属于public范围的URL地址为cinder真实的地址：
openstack endpoint list | grep cinder
- 使用PuTTY，依次以“CPT-SRV01”字段（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）对应的IP地址登录“CPT-SRV01”节点，以“CPT-SRV02”字段（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）对应的IP地址登录“CPT-SRV02”节点。
默认帐户：apicom，默认密码：*****
- 执行以下命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
sudo su root
- 执行以下命令查看到云硬盘配置文件：
vim /opt/apicom/tomcat/taskmgr/WEB-INF/classes/taskmgr-config.properties
在配置文件中查找“cinder.endpoint.publicurl”配置项的值，即EVS配置的cinder地址：
对比两者，若不一致，取cinder真实的地址在配置文件中编辑更新该配置项。
- 若ErrorMessage中包含“503 Service Unavailable”、“No server is available to handle this request.”等描述服务不可用的信息，说明Cinder API层的服务异常，无法响应请求，请联系FusionSphere OpenStack Cinder运维人员定位并解决问题。
- 若Cinder URL正确，请参考典型Cinder问题定位指导定位问题，如果还无法解决请将定位信息提供给FusionSphere OpenStack Cinder运维人员定位并解决问题。
6. 问题解决并等待6分钟后，告警将自动清除。
参考信息
无。