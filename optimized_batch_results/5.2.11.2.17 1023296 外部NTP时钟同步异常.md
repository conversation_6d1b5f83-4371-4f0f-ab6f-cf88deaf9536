# ********.17 1023296 外部NTP时钟同步异常

********.17 1023296 外部NTP时钟同步异常
告警解释
当系统与外部NTP服务器时钟同步异常时，上报此告警。
告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1023296 | 重要 | 是 |
告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| NTP服务器IP | 同步异常的NTP服务器IP。 |
| 节点IP | 产生告警的节点IP。 |
对系统的影响
系统不再与外部时钟源进行时间同步。
可能原因
- 系统与外部NTP服务器网络通信异常。
- 外部NTP服务器运行异常。
处理步骤
- 可能原因：系统与外部NTP服务器网络通信异常。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行ntpq -p命令获取外部NTP服务器的IP地址列表。
- 执行ping 外部NTP服务器的IP地址（ipv6场景下使用ping6）命令，检查与外部NTP服务器之间的网络通信是否正常。
- 是，请执行2。
- 否，请执行1.e。
- 联系网络管理员进行网络故障的排除，等待NTP自动同步，完成后检查该告警是否清除。
- 是，处理结束。
- 否，请执行2。
- 可能原因：外部NTP服务器运行异常。
- 通过SSH登录NTP服务器节点，华为云Stack的NTP服务器默认帐号：untp，默认密码：*****。
- 执行sudo su命令切换到root，默认密码：*****。
- 执行systemctl status ntpd.service命令查看NTP服务是否正常，当回显中有“active(running)”则表示NTP服务正常。
- 是，请联系技术支持工程师协助解决。
- 否，执行systemctl start ntpd.service命令重启NTP服务。
- 等待10分钟，检查告警是否清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
参考信息
无。