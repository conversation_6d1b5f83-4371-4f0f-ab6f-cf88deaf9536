# ******* ALM-1200034 zookeeper进程重复运行

******* ALM-1200034 zookeeper进程重复运行
告警解释
系统每隔2分钟检查一次节点是否存在多余的zookeeper进程，如果存在多于1个进程，产生此告警。
告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200034 | 重要 | 是 |
告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源系统 | 告警来源。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 此条告警信息最近发生时间。 |
| 定位信息 | 产生告警信息的定位信息。 |
| 附加信息 | 告警附加信息。 |
对系统的影响
多个zookeeper进程运行时，只有一个能提供服务，对系统正常运行无影响。
可能原因
同时启动了多个zookeeper进程。
处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 单击“登录”。
3. 在页面上方的菜单栏，选择“集中告警”。
4. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
5. 在“告警详情”页的“定位信息”中，“主机IP”即告警节点IP地址。
6. 使用“PuTTY”，登录告警节点。
默认帐号： vpc，默认密码： *****
7. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
8. 执行下面命令，获取所有zookeeper进程pid。
ps -ef |grep zookeeper
回显如下所示。其中，可获取zookeeper进程pid为7228。
[vpc@PUB-SRV01 ~]$ ps  -ef | grep zookeeper
vpc       7228     1  015:04 ?        00:00:07
/opt/common/jre/bin/java -Dzookeeper.log.dir=logs -Dzookeeper.root.logger=INFO,ROLLINGFILE
-cp /home/<USER>/zookeeper/bin/../build/classes:/home/<USER>/zookeeper/bin/../build/lib/*.jar:/home/<USER>/zookeeper/bin/../lib/slf4j-log4j12-1.7.25.jar:/home/<USER>/zookeeper/bin/../lib/slf4j-api-1.7.25.jar:/home/<USER>/zookeeper/bin/../lib/netty-3.10.6.Final.jar:/home/<USER>/zookeeper/bin/../lib/log4j-1.2.17.jar:/home/<USER>/zookeeper/bin/../lib/jline-0.9.94.jar:/home/<USER>/zookeeper/bin/../lib/audience-annotations-0.5.0.jar:/home/<USER>/zookeeper/bin/../zookeeper-3.4.13.jar:/home/<USER>/zookeeper/bin/../src/java/lib/*.jar:/home/<USER>/zookeeper/bin/../conf:
org.apache.zookeeper.server.quorum.QuorumPeerMain
/home/<USER>/zookeeper/bin/../conf/zoo.cfg
9. 执行下面命令，停止所有zookeeper进程。
kill -9 pid
pid为8中执行命令返回信息中获取的进程id。
进程保护机制会将进程重新启动。
10. 等待5分钟，执行下面命令，检查是否只有一个zookeeper进程存在。
ps -ef |grep zookeeper
- 是，执行11。
- 否，请联系技术支持工程师协助解决。
回显如下所示时，表示只有一个zookeeper进程存在。
[vpc@PUB-SRV01 ~]$ ps  -ef | grep zookeeper
vpc       7228     1  015:04 ?        00:00:07
/opt/common/jre/bin/java -Dzookeeper.log.dir=logs -Dzookeeper.root.logger=INFO,ROLLINGFILE
-cp /home/<USER>/zookeeper/bin/../build/classes:/home/<USER>/zookeeper/bin/../build/lib/*.jar:/home/<USER>/zookeeper/bin/../lib/slf4j-log4j12-1.7.25.jar:/home/<USER>/zookeeper/bin/../lib/slf4j-api-1.7.25.jar:/home/<USER>/zookeeper/bin/../lib/netty-3.10.6.Final.jar:/home/<USER>/zookeeper/bin/../lib/log4j-1.2.17.jar:/home/<USER>/zookeeper/bin/../lib/jline-0.9.94.jar:/home/<USER>/zookeeper/bin/../lib/audience-annotations-0.5.0.jar:/home/<USER>/zookeeper/bin/../zookeeper-3.4.13.jar:/home/<USER>/zookeeper/bin/../src/java/lib/*.jar:/home/<USER>/zookeeper/bin/../conf:
org.apache.zookeeper.server.quorum.QuorumPeerMain
/home/<USER>/zookeeper/bin/../conf/zoo.cfg
11. 等待10分钟，检查告警是否已自动清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
参考信息
无。