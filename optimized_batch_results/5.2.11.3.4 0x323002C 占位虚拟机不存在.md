# 5.2.11.3.4 0x323002C 占位虚拟机不存在

5.2.11.3.4 0x323002C 占位虚拟机不存在
告警解释
服务实例所在虚拟机的占位虚拟机不存在。
告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x323002C | 重要 | 是 |
告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 服务实例名称 | 服务实例的名称 |
| 虚拟机名称 | 服务实例中的生产虚拟机名称 |
| 服务实例类型 | 服务实例的类型 |
对系统的影响
导致故障恢复或计划性迁移失败，业务无法恢复。
可能原因
占位虚拟机被删除。
处理步骤
1. 确认该实例是否需要删除，删除实例操作请参见删除CSHA实例，CSDR和VHA+CSDR服务实例删除操作请参考云服务器容灾服务“删除实例”的操作方法。待该执行任务完成后，检查告警是否自动清除。
- 是，该操作结束。
- 否，请转2。
2. 请联系技术支持工程师协助解决。
参考信息
无。