# 5.2.11.1.81 0x10E01A0019 删除备份副本失败

5.2.11.1.81 0x10E01A0019 删除备份副本失败
告警解释
删除备份副本（任务ID：[Task_id]，备份副本ID：[Snap_id]）失败。
告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01A0019 | 次要 | 是 |
告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Task_id | 任务ID。 |
| Snap_id | 备份副本ID。 |
对系统的影响
备份副本会占用备份存储空间。
可能原因
- 存储单元不可访问。
- 备份副本被删除或损坏。
处理步骤
- 可能原因1：存储单元不可访问。
- 在当前告警界面查看ID为 0x10E00E0000（连接存储单元失败） 或者0x10E00E0001（访问存储单元失败）的告警是否上报。
- 是，处理1.a所述的告警，保证存储单元可访问。
- 否，执行2。
- 可能原因2：备份副本被删除或损坏。
请联系技术支持工程师协助解决。
参考信息
无。