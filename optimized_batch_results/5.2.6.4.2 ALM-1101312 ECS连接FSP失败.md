# *******.2 ALM-1101312 ECS连接FSP失败

##### 告警解释
##### ECS服务连接fsp失败时产生此告警，可能原因为ECS的URL配置不正确，需要变更该节点的URL配置。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1101312 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 告警名称 | 告警名称 | 此条告警信息的名称 |
| 来源设备名称 | 来源设备名称 | 产生告警信息的设备名称 |
| 监控系统名称 | 监控系统名称 | 对接系统的类型 |
| 来源系统 | 来源系统 | 对接系统的名称 |
| IP地址 | IP地址 | 产生告警信息的业务服务器的IP地址 |
| 发生时间 | 发生时间 | 此条告警信息首次发生时间 |
| 定位信息 | Service | 产生此告警的服务名 |
| 附加信息 | 云服务 | 云服务名 |
| 附加信息 | 服务 | 服务名 |
| 附加信息 | Fsp组件 | 组件名 |
##### 对系统的影响
ECS服务无法正常使用。
##### 可能原因
ECS的URL配置不正确。
##### 处理步骤
1. 使用PuTTY，以“Reverse-Proxy”（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）字段对应的IP地址登录FusionSphere OpenStack节点。
默认帐户：fsp，默认密码：*****
2. 执行以下命令，输入root帐号密码，切换至root帐号，默认密码为*****。
sudo su root
3. 执行以下命令，导入环境变量。导入过程中，请输入1，选择环境变量“openstack environment variable (keystone v3)”，默认密码为*****。
source set_env
4. 执行以下命令查询nova安装部署时的地址。以下图为例，图示中的URL地址为nova的准确地址：
openstack endpoint list | grep nova
5. 使用PuTTY，依次以“CPT-SRV01”和“CPT-SRV02”字段（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）对应的IP地址登录两个节点。
默认帐户：apicom，默认密码：*****
6. 执行以下命令，输入root帐号密码，切换至root帐号，默认密码为*****。
sudo su root
7. 执行以下命令查看ECS的配置文件：
vim /opt/apicom/tomcat/taskmgr/WEB-INF/classes/taskmgr-config.properties
在配置文件中查找“nova.endpoint.publicurl”配置项的值，即ECS配置的nova地址：
对比4中获取的地址，若不一致，参考4中获取的地址更新该配置项。
8. 执行如下命令重启apicom的进程：
sh /opt/apicom/tomcat/bin/shutdown.sh
sh /opt/apicom/tomcat/bin/startup.sh
9. 等待5分钟后，查看告警是否自动清除。
- 是，操作处理完毕。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
*******.3 登录FusionSphere OpenStack后台
在系统经过SSH安全加固后，禁用了SSH通过口令认证的访问方式。本操作指导用户使用PuTTY工具和认证所需的私钥，来登录待访问的节点。
前提条件
10. 待访问节点已完成SSH的安全加固，包括：
- 启用SSH通过公私钥对认证的方式，且节点已经配置好公钥证书。
- 已禁用SSH通过口令认证的方式。
11. 已获取与公钥证书匹配的私钥证书。如果私钥证书为加密证书，还需要获取私钥证书的密码。
默认的私钥证书获取方式如下：
访问http://support.huawei.com/并登录，网页右上角搜索“FusionSphere默认公私钥”，通过搜索结果中的第一个条目（FusionSphere 默认公私钥【文档】）获取证书。
操作步骤
12. 在当前的本地PC上，是否已经使用PuTTY工具，通过公私钥对认证方式登录过待访问的节点？
- 是，执行7。
- 否或无法确定，执行2。
13. 运行PuTTY工具，在主界面，输入待访问节点的IP地址和SSH端口号（默认为22）。
14. 在左侧“Category”区域选择“Connection > SSH > Auth”。
进入SSH的认证配置界面。
15. 单击“Browse”，在弹出窗口中选择已获取的私钥证书，单击“打开”。
私钥证书文件名为“*.ppk”。当前默认的私钥证书为从版本发布路径获取的“id_rsa.ppk”。
配置后如下图所示。
图1 配置私钥证书
16. 在左侧“Category”区域选择“Session”。
进入主界面。
17. 为方便后续多次访问，在“Saved Sessions”中自定义会话名称，单击“Save”保存会话。
如下图所示。
图2 保存会话
该步骤执行完成后，跳转至8。
- Host Name (or IP address)输入框ip（例如***************）的获取方式：在“FCD生成的LLD”文件中，搜索Reverse-Proxy，找到网络平面为external_api对应的IP地址。该IP地址即为FusionSphere OpenStack的后台节点。
18. 选择已保存的会话，单击“Load”加载会话。
19. 单击“Open”开启会话。
20. 按要求输入访问节点的登录用户名，即可开始访问节点。
如果使用的私钥证书为加密证书，则还要按提示输入私钥证书的密码。
版本配套的默认私钥证书为加密证书，默认密码为“*****”。
- FusionSphere OpenStack后台节点默认用户为fsp，默认密码为“*****”。
- 登录fsp用户后，输入命令su，切换到root用户，默认密码为“*****”。
- 切换到root用户后，一般需要参考导入环境变量，导入环境变量。
*******.4 导入环境变量
系统在安装完成后，默认打开鉴权模式，在执行CPS及OpenStack命令前，需要先导入环境变量才能操作。系统提供了导入环境变量的快捷脚本，可直接执行脚本即可。
前提条件
21. 已完成FusionSphere OpenStack第一台主机的安装。
22. 已通过root用户登录主机。
操作步骤
23. 执行以下命令，导入环境变量。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
24. 选择鉴权方式。
目前提供四种方式可供用户选择，使用内置DC管理员的Keystone V3鉴权、CPS鉴权、Keystone V2鉴权、使用内置云管理员的Keystone V3鉴权。请根据以下详细介绍按照实际需求选择。
- 使用内置DC管理员的OpenStack Keystone V3鉴权，输入“1”，按“Enter”，并按提示输入“OS_USERNAME”的密码。
默认密码：“*****”。
密码输入成功后如果有cps host-list和nova list命令的自动回显信息，则表示环境变量导入成功。导入成功后系统使用内置DC管理员的Keystone V3鉴权。可以正常执行CPS命令和OpenStack命令。
在OpenStack的Mitaka版本中，Keystone命令行接口做了归一化处理，全部封装为OpenStack命令。在Identity service注册为V3路径时，原Keystone命令行在执行tenant/role/service相关命令时会存在兼容性问题。请优先使用Keystone V3鉴权，并使用同等功能的openstack project/role/service命令。
- 使用CPS的鉴权，输入“2”，按“Enter”，并按提示输入“CPS_USERNAME”的用户名和密码。
用户名：“cps_admin”，默认密码：“*****”。
输入完成后如果有cps host-list命令的自动回显信息，则表示环境变量导入成功。使用CPS鉴权导入环境变量后只能执行CPS相关命令，无法执行OpenStack命令，所以回显信息会在执行完nova list后提示“ERROR...”。
在未完全部署完成FusionSphere OpenStack之前，OpenStack鉴权还未启用，或Keystone鉴权异常时，可使用CPS鉴权。
- 使用OpenStack的Keystone V2鉴权，输入“3”，按“Enter”，并按提示输入“OS_USERNAME”的密码。
默认密码：“*****”。
密码输入成功后如果有cps host-list和nova list命令的自动回显信息，则表示环境变量导入成功。导入成功后系统使用Keystone V2鉴权。可以正常执行CPS命令和OpenStack命令。
为了兼容之前版本的鉴权方式，仍然支持Keystone V2鉴权，建议优先使用Keystone V3鉴权。
- 使用内置云管理员帐号的OpenStack Keystone V3鉴权，输入“4”，按“Enter”，并按提示输入“cloud_admin”（内置的云管理员帐号）的密码。
默认密码：“*****”。
密码输入成功后如果有cps host-list和nova list命令的自动回显信息，则表示环境变量导入成功。导入成功后系统使用内置云管理员帐号的Keystone V3鉴权。可以正常执行CPS命令和OpenStack命令。
- 在对接Service OM或ManageOne的情况下，请使用V3版本的鉴权方式。
- 如果执行的操作需要使用“cloud_admin”帐户权限（例如，使用PasswordManager命令重置GaussDB数据库帐户密码），请使用内置云管理员帐号的OpenStack Keystone V3鉴权。
- 如果导入环境变量时选择使用Keystone鉴权，但环境还未部署nova-api，将导致cps host-list回显成功，nova list回显异常，部署nova-api服务后即可正常使用nova相关命令。
25. 在多DC场景下，如果每个DC都部署了独立的glance，在每个DC导入环境变量时，还需要执行以下命令，导入该DC的OS_IMAGE_URL。
export OS_IMAGE_URL=https://image.az.dc.domainname.com:443
az.dc.domainname.com:443为该DC中设置的glance域名。
< 上一节
*******.1 OBS Console
*******.1.1 ALM-600000007 OBS Console 的tomcat进程异常
##### 告警解释
当被监控对象的Tomcat进程中断时，上报此警告。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 600000007 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 区域 | 产生告警信息的区域名称。 |
| 定位信息 | 云服务 | 产生告警信息的云服务名称。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 模板ID | 监控模板唯一ID。 |
| 附加信息 | 指标名称 | 产生告警信息的指标名称。 |
| 附加信息 | 监控类型 | 业务监控。 |
| 附加信息 | 详细信息 | 最近几个周期的监控数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
OBS Console服务无法正常使用。
##### 可能原因
OBS Console服务的Tomcat进程启动失败。
##### 处理步骤
26. 使用PuTTY，以“CONSOLE01”或“CONSOLE02”字段对应的IP地址登录OBS Console节点。
默认帐户：obs_admin，默认密码：*****。
IP地址请在HUAWEI CLOUD Stack Deploy部署工具配置部署参数后导出的参数信息汇总文件《xxx_export_all_CN.xlsm》的“工具生成的IP参数”页签搜索“CONSOLE01”或“CONSOLE02”获取。
27. 执行以下命令，切换到root帐户。
sudo su
在“password for root:”后输入root帐户的密码，root帐户默认密码为*****。
28. 执行以下命令检查OBS服务是否已正常启动。
sh /etc/obs/console/control.sh status
- 是，OBS服务正常启动，回显显示“Console is running”，请执行5。
- [root@CONSOLE02 obs_admin]# sh /etc/obs/console/control.sh status
Console is running
- 否，OBS服务启动失败，回显显示“Console is not running”，请执行4。
- [root@CONSOLE02 obs_admin]# sh /etc/obs/console/control.sh status
Console is not running
29. 执行以下命令后，根据回显结果确认是否成功重启OBS服务。
sh /etc/obs/console/control.sh restart
- 是，OBS服务重启成功，回显显示“Start console successfully”，请执行5。
- [root@CONSOLE02 obs_admin]# sh /etc/obs/console/control.sh restart
- Stop console successfully
- Starting console component...
- Console started successfully
Start console successfully
- 否，请联系技术支持工程师协助解决。
30. 请检查告警是否在下一个监控周期（约3分钟后）消除。
- 是，告警消除，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
*******.1.2 ALM-600000008 OBS Console 的tomcat端口未监听
##### 告警解释
当被监控对象的Tomcat 7583端口绑定失败时，上报此警告。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 600000008 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 区域 | 产生告警信息的区域名称。 |
| 定位信息 | 云服务 | 产生告警信息的云服务名称。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 模板ID | 监控模板唯一ID。 |
| 附加信息 | 指标名称 | 产生告警信息的指标名称。 |
| 附加信息 | 监控类型 | 业务监控。 |
| 附加信息 | 详细信息 | 最近几个周期的监控数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
OBS Console服务无法正常使用。
##### 可能原因
OBS Console服务的Tomcat 7583端口绑定失败。
##### 处理步骤
31. 使用PuTTY，以“CONSOLE01”或“CONSOLE02”字段对应的IP地址登录OBS Console节点。
默认帐户：obs_admin，默认密码：*****。
IP地址请在HUAWEI CLOUD Stack Deploy部署工具配置部署参数后导出的参数信息汇总文件《xxx_export_all_CN.xlsm》的“工具生成的IP参数”页签搜索“CONSOLE01”或“CONSOLE02”获取。
32. 执行以下命令，切换到root用户。
sudo su
在“password for root:”后输入root用户的密码，root用户默认密码为*****。
33. 执行以下命令检查OBS服务是否已正常启动。
sh /etc/obs/console/control.sh status
- 是，OBS服务正常启动，回显显示如下所示，执行4。
- [root@CONSOLE02 obs_admin]# sh /etc/obs/console/control.sh status
Console is running
- 否，OBS服务启动失败，回显显示“Console is not running”，请执行5。
- [root@CONSOLE02 obs_admin]# sh /etc/obs/console/control.sh status
Console is not running
34. 执行以下命令检查Tomcat相应端口是否正常绑定。
netstat -ntl
- 是，OBS服务端口绑定正常，回显显示有OBS Console服务的端口（7583），请执行6。
- [root@CONSOLE02 obs_admin]# netstat -ntl
- Active Internet connections (only servers)
- Proto Recv-Q Send-Q Local Address           Foreign Address         State
- tcp        0      0 **********:22             0.0.0.0:*               LISTEN
- tcp        0      0 **********:21700          0.0.0.0:*               LISTEN
- tcp6       0      0 **********:7563           :::*                    LISTEN
- tcp6       0      0 **********:7443           :::*                    LISTEN
- tcp6       0      0 127.0.0.1:2324          :::*                    LISTEN
tcp6       0      0 **********:7583           :::*                    LISTEN
- 否，OBS服务端口绑定失败，回显没有OBS Console服务的端口（7583），请执行5。
35. 执行以下命令后，根据回显结果确认是否成功重启OBS服务。
sh /etc/obs/console/control.sh restart
- 是，OBS服务重启成功，回显显示“Start console successfully”，请执行6。
- [root@CONSOLE02 obs_admin]# sh /etc/obs/console/control.sh restart
- Stop console successfully
- Starting console component...
- Console started successfully
Start console successfully
- 否，请联系技术支持工程师协助解决。
36. 请检查告警是否在下一个监控周期（约3分钟后）消除。
- 是，告警消除，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
*******.1.3 ALM-600000100 OBS Console tomcat证书异常
##### 告警解释
当被监控的OBS Console Tomcat证书有效期即将过期，或者已过期，或者监控异常时，上报此警告。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 600000100 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 区域 | 产生告警信息的区域名称。 |
| 定位信息 | 云服务 | 产生告警信息的云服务名称。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 模板ID | 监控模板唯一ID。 |
| 附加信息 | 指标名称 | 产生告警信息的指标名称。 |
| 附加信息 | 监控类型 | 业务监控。 |
| 附加信息 | 详细信息 | 最近几个周期的监控数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
影响OBS Console服务的正常使用。
##### 可能原因
- OBS Console Tomcat证书有效期即将过期。
- OBS Console Tomcat证书有效期已过期。
- OBS Console Tomcat证书监控异常。
##### 处理步骤
37. 使用浏览器，登录ManageOne运维面。
登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
默认帐号：admin，默认密码：*****。
38. 在页面上方选择“运维工具箱 > 服务监控”，进入服务监控页面。
39. 在左侧导航栏选择“实例列表”，在页面右上方搜索栏中搜索参数“OBS”。
40. 单击待查看实例所在行的实例名称，进入实例监控页面。
41. 在左侧导航栏选择“keystore”，查看当前节点OBS Console Tomcat证书监控状态值。
- 值为1时，表示证书有效期即将过期，请执行6。
- 值为2时，表示证书有效期已过期，请执行6。
- 值大于等于3时，表示证书有效期监控异常，请联系技术支持工程师协助解决。
42. 执行Tomcat证书替换操作，具体操作请参见更换OBS Console Tomcat的TLS认证证书。
43. 证书替换成功后，告警是否在下一个监控周期（约3分钟后）消除。
- 是，告警消除，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
< 上一节
*******.3 OBS LVS
< 上一节
*******.3.1 ALM-600000201 OBS LVS节点存在未连通网口
##### 告警解释
当OBS LVS节点存在未连通网口的时候，上报此警告。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 600000201 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 区域 | 产生告警信息的区域名称。 |
| 定位信息 | 云服务 | 产生告警信息的云服务名称。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 模板ID | 监控模板唯一ID。 |
| 附加信息 | 指标名称 | 产生告警信息的指标名称。 |
| 附加信息 | 监控类型 | 业务监控。 |
| 附加信息 | 详细信息 | 最近几个周期的监控数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
影响OBS LVS服务的可靠性。
##### 可能原因
- 网口未启动。
- 网络闪断等原因导致交换机端口Down。
- 网口连线存在问题。
- 网卡故障。
- 接口频繁闪断触发交换机链路振荡保护功能。
##### 处理步骤
44. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
45. 在页面上方的菜单栏，选择“集中告警 > 当前告警”。
46. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
47. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
##### - 可能原因：网口未启动。
- 使用“PuTTY”工具，通过4中获取的节点IP地址登录产生告警的节点。
默认帐号：udslvs，默认密码：*****。
- 执行以下命令，并按提示输入“root”用户的密码，默认密码：*****，切换至“root”用户。
sudo su
- 执行以下命令查看并获取bond中的网口名。
cat /proc/net/bonding/bond0
- 使用ifconfig ethx up命令启动bond中的网口。其中ethx为5.c获取的网口名。
- 执行ifconfig ethx，查看输出结果。其中ethx为5.c获取的网口名。
- 出现提示UP BROADCAST RUNNING MULTICAST、UP BROADCAST RUNNING SLAVE MULTICAST或者UP BROADCAST RUNNING MASTER MULTICAST表示网口已正常启动，请执行5.f。
- 未出现此提示，网口存在异常，请执行6。
- 查看告警是否在下一个监控周期（约3分钟后）消除。
- 是，处理结束。
- 否，请执行6。
##### - 可能原因：网络闪断等原因导致交换机端口Down。
- 登录交换机，在故障接口所连交换机接口的接口视图中，先执行命令shutdown，然后再执行命令undo shutdown。
- 查看告警是否在下一个监控周期（约3分钟后）消除。
- 是，处理结束。
- 否，请执行7。
##### - 可能原因：网口连线存在问题。
- 检查网口物理连接，找到故障网口，将告警网口的网线重新插拔一下，确保网线两头均插紧。检查网口连接指示灯是否常亮。
- 是，请执行7.d。
- 否，请执行7.b。
- 更换网线。检查网口连接指示灯是否常亮。
- 是，请7.d。
- 否，请执行7.c。
- 如果存在光模块，更换光模块。检查网口连接指示灯是否常亮。
- 是，请7.d。
- 否，请执行8。
- 查看告警是否在下一个监控周期（约3分钟后）消除。
- 是，处理结束。
- 否，请执行8。
##### - 可能原因：网卡故障。
- 关闭该故障设备。
- 设备已经关闭后，拔掉该设备电源。
- 取下顶部外壳螺丝，打开顶部外壳，取下网卡螺丝，更换网卡，插紧螺丝，盖好顶部外壳，插上网线，然后将设备上电。
该操作属于高危操作，请在操作前联系技术支持工程师。
- 重新检查网口，查看网卡状态是否正常。
- 是，请执行8.e。
- 否，请执行9。
- 查看告警是否在下一个监控周期（约3分钟后）消除。
- 是，处理结束。
- 否，请执行9。
##### - 可能原因：接口频繁闪断触发交换机链路振荡保护功能。
- 登录交换机，在故障接口所连交换机接口的接口视图中，依次执行命令shutdown和undo shutdown，或者执行命令restart，重启接口，具体操作命令参见对应型号交换机的产品文档。
- 查看告警是否在下一个监控周期（约3分钟后）消除。
- 是，处理结束。
- 否，请执行10。
- 如果仍然存在故障，联系技术支持工程师。
##### 参考信息
无。
*******.3.2 ALM-600000200 keepalived进程未启动
##### 告警解释
当被监控的OBS LVS节点keepalived进程未启动的时候，上报此警告。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 600000200 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 区域 | 产生告警信息的区域名称。 |
| 定位信息 | 云服务 | 产生告警信息的云服务名称。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 模板ID | 监控模板唯一ID。 |
| 附加信息 | 指标名称 | 产生告警信息的指标名称。 |
| 附加信息 | 监控类型 | 业务监控。 |
| 附加信息 | 详细信息 | 最近几个周期的监控数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
影响OBS LVS服务的正常使用。
##### 可能原因
keepalived进程未启动。
##### 处理步骤
48. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
49. 在页面上方的菜单栏，选择“集中告警 > 当前告警”。
50. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
51. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
52. 使用“PuTTY”工具，通过4中获取的节点IP地址登录产生告警的节点。
默认帐号：udslvs，默认密码：*****。
53. 执行以下命令，并按提示输入“root”用户的密码，默认密码：*****，切换至“root”用户。
su - root
54. 执行以下命令重启keepalived服务。
systemctl restart keepalived.service
55. 执行以下命令查看keepalived服务状态。
systemctl status keepalived.service
- 出现提示Active: active (running)，表示重启keepalived服务成功，请执行9。
- 出现提示Active: inactive (dead)，表示重启keepalived服务失败，联系技术支持工程师。
56. 执行以下命令查看crond服务状态。
systemctl status crond.service
- 出现提示Active: active (running)，表示crond服务正常，请执行12。
- 出现提示Active: inactive (dead)，表示crond服务不正常，请执行10。
57. 执行以下命令重启crond服务。
systemctl restart crond.service
58. 再次执行以下命令查看crond服务状态。
systemctl status crond.service
- 出现提示Active: active (running)，表示重启crond服务成功，请执行12。
- 出现提示Active: inactive (dead)，表示重启crond服务失败，联系技术支持工程师。
59. 执行以下命令查看crond服务的配置。
cat /etc/crontab
SHELL=/bin/bash
PATH=/sbin:/bin:/usr/sbin:/usr/bin
MAILTO=root
# For details see man 4 crontabs
# Example of job definition:
# .---------------- minute (0 - 59)
# |  .------------- hour (0 - 23)
# |  |  .---------- day of month (1 - 31)
# |  |  |  .------- month (1 - 12) OR jan,feb,mar,apr ...
# |  |  |  |  .---- day of week (0 - 6) (Sunday=0 or 7) OR sun,mon,tue,wed,thu,fri,sat
# |  |  |  |  |
# *  *  *  *  * user-name  command to be executed
*/1 * * * * root sh /opt/obs/script/lvs/lvsMonitor.sh >> /dev/null 2>&1
*/1 * * * * root sh /opt/obs/script/lvs/nicoffMonitor.sh >> /dev/null 2>&1
*/1 * * * * root sh /opt/obs/script/lvs/vipMonitor.sh >> /dev/null 2>&1
*/1 * * * * root /home/<USER>/bin/manual/mstart.sh
*/2 * * * * root /opt/moopsagent/python-1.6.13/bin/python3.7 /opt/moopsagent/agent_watchdog.py >/dev/null 2>&1
- 定时执行lvsMonitor.sh脚本时间阀值为一分钟则表示配置正确，请执行15。
- 定时执行lvsMonitor.sh脚本时间阀值不为一分钟则表示配置不正确，请执行13。
60. 执行以下命令将定时执行lvsMonitor.sh脚本时间阀值修改为一分钟，保存退出。
vim /etc/crontab
SHELL=/bin/bash
PATH=/sbin:/bin:/usr/sbin:/usr/bin
MAILTO=root
# For details see man 4 crontabs
# Example of job definition:
# .---------------- minute (0 - 59)
# |  .------------- hour (0 - 23)
# |  |  .---------- day of month (1 - 31)
# |  |  |  .------- month (1 - 12) OR jan,feb,mar,apr ...
# |  |  |  |  .---- day of week (0 - 6) (Sunday=0 or 7) OR sun,mon,tue,wed,thu,fri,sat
# |  |  |  |  |
# *  *  *  *  * user-name  command to be executed
*/1 * * * * root sh /opt/obs/script/lvs/lvsMonitor.sh >> /dev/null 2>&1
*/1 * * * * root sh /opt/obs/script/lvs/nicoffMonitor.sh >> /dev/null 2>&1
*/1 * * * * root sh /opt/obs/script/lvs/vipMonitor.sh >> /dev/null 2>&1
*/1 * * * * root /home/<USER>/bin/manual/mstart.sh
*/2 * * * * root /opt/moopsagent/python-1.6.13/bin/python3.7 /opt/moopsagent/agent_watchdog.py >/dev/null 2>&1
61. 执行以下命令重启crond服务。
systemctl restart crond.service
62. 完成keepalived服务重启和crond服务配置后，查看告警是否在下一个监控周期（约3分钟后）消除。
- 是，告警消除，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
< 上一节
5.2.9 虚拟私有云
******* ALM-1200025 ntp进程故障
##### 告警解释
系统每隔60秒检查一次NTP进程是否存在，如果不存在，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200025 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源系统 | 告警来源。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 此条告警信息最近发生时间。 |
| 定位信息 | 产生告警信息的定位信息。 |
| 附加信息 | 告警附加信息。 |
##### 对系统的影响
系统时间不同步，影响计费及问题定位，需尽快处理异常。
##### 可能原因
NTP进程异常。
##### 处理步骤
63. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
64. 单击“登录”。
65. 在页面上方的菜单栏，选择“集中告警”。
66. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
67. 在“告警详情”页的“定位信息”中，“主机IP”即告警节点IP地址。
68. 使用“PuTTY”，登录告警节点。
默认帐号： vpc，默认密码： *****
69. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
70. 执行下面命令，切换到root用户。
sudo su - root
71. 执行下面命令，检查NTP进程是否存在。
ps -ef |grep ntp
- 是，执行10。
- 否，执行12。
回显如下所示时，表示NTP进程存在。回显具体内容与系统相关，请以实际为准。
[root@PUB-SRV01 vpc]# ps -ef | grep ntp
ntp      10898     1  0 Jun20 ?        00:00:02 /usr/sbin/ntpd -u ntp:ntp -g
72. 执行下面命令，重启NTP进程。
service ntp restart
回显如下所示时，表示NTP进程重启成功。回显具体内容与系统相关，请以实际为准。
[root@PUB-SRV01 vpc]# service ntp restart
Redirecting to /bin/systemctl restart ntp.service
73. 检查NTP进程是否重启成功。
- 是，执行14。
- 否，请联系技术支持工程师协助解决。
74. 执行下面命令，启动NTP进程。
service ntp start
回显如下所示时，表示NTP进程启动成功。回显具体内容与系统相关，请以实际为准。
[root@PUB-SRV01 vpc]# service ntp start
Redirecting to /bin/systemctl start ntp.service
75. 检查NTP进程是否启动成功。
- 是，执行14。
- 否，请联系技术支持工程师协助解决。
76. 等待10分钟，检查告警是否已自动清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。
******* ALM-1200027 tomcat进程CPU占用率超过阈值
##### 告警解释
系统每隔2分钟检查一次节点tomcat进程cpu占用是否超过设定的阈值，如果在30分钟内持续超过阈值，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200027 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源系统 | 告警来源。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 此条告警信息最近发生时间。 |
| 定位信息 | 产生告警信息的定位信息。 |
| 附加信息 | 告警附加信息。 |
##### 对系统的影响
系统时间不同步，影响计费及问题定位，需尽快处理异常。
##### 可能原因
- tomcat进程运行异常。
- 服务容量不够，处理能力不足，无法满足业务需求。
##### 处理步骤
77. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
78. 单击“登录”。
79. 在页面上方的菜单栏，选择“集中告警”。
80. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
81. 在“告警详情”页的“定位信息”中，“主机IP”即告警节点IP地址。
82. 使用“PuTTY”，登录告警节点。
默认帐号： vpc，默认密码： *****
83. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
84. 执行下面命令，获取tomcat进程pid。
ps -ef | grep vpc | grep tomcat
回显如下所示。其中，可获取tomcat进程pid为17600。
[vpc@PUB-SRV01 ~]# ps -ef | grep vpc | grep tomcat
vpc      17600     1  6
15:35 pts/0    00:03:18
/opt/common/jre/bin/java -Dnop -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
-Djdk.tls.ephemeralDHKeySize=2048
-Dorg.apache.catalina.security.SecurityListener.UMASK=0027
-Djava.endorsed.dirs=/home/<USER>/local/tomcat/endorsed -classpath
/home/<USER>/local/tomcat/bin/bootstrap.jar:/home/<USER>/local/tomcat/bin/tomcat-juli.jar
-Dcatalina.base=/home/<USER>/local/tomcat -Dcatalina.home=/home/<USER>/local/tomcat
-Djava.io.tmpdir=/home/<USER>/local/tomcat/temp
org.apache.catalina.startup.Bootstrap start
85. 执行下面命令，停止tomcat进程。
kill -9 pid
pid为8中执行命令返回信息中获取的进程id。
进程保护机制会将进程重新启动。
86. 等待5分钟，执行下面命令，检查tomcat进程是否存在。
ps -ef | grep vpc | grep tomcat
- 是，执行11。
- 否，请联系技术支持工程师协助解决。
回显如下所示时，表示tomcat进程存在。
[vpc@PUB-SRV01 ~]# ps -ef | grep vpc | grep tomcat
vpc      17600     1  6
15:35 pts/0    00:03:18
/opt/common/jre/bin/java -Dnop -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
-Djdk.tls.ephemeralDHKeySize=2048
-Dorg.apache.catalina.security.SecurityListener.UMASK=0027
-Djava.endorsed.dirs=/home/<USER>/local/tomcat/endorsed -classpath
/home/<USER>/local/tomcat/bin/bootstrap.jar:/home/<USER>/local/tomcat/bin/tomcat-juli.jar
-Dcatalina.base=/home/<USER>/local/tomcat -Dcatalina.home=/home/<USER>/local/tomcat
-Djava.io.tmpdir=/home/<USER>/local/tomcat/temp
org.apache.catalina.startup.Bootstrap start
87. 等待10分钟，检查告警是否已自动清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。
******* ALM-1200028 tomcat进程内存占用率超过阈值
##### 告警解释
系统每隔2分钟检查一次节点tomcat进程内存占用是否超过设定的阈值，如果在30分钟内持续超过阈值，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200028 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源系统 | 告警来源。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 此条告警信息最近发生时间。 |
| 定位信息 | 产生告警信息的定位信息。 |
| 附加信息 | 告警附加信息。 |
##### 对系统的影响
对系统正常运行无影响，但后续如果业务量增加，可能导致无法处理新增业务。需尽快处理。
##### 可能原因
- tomcat进程运行异常。
- 服务容量不够，处理能力不足，无法满足业务需求。
##### 处理步骤
88. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
89. 单击“登录”。
90. 在页面上方的菜单栏，选择“集中告警”。
91. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
92. 在“告警详情”页的“定位信息”中，“主机IP”即告警节点IP地址。
93. 使用“PuTTY”，登录告警节点。
默认帐号： vpc，默认密码： *****
94. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
95. 执行下面命令，获取tomcat进程pid。
ps -ef | grep vpc | grep tomcat
回显如下所示。其中，可获取tomcat进程pid为17600。
[vpc@PUB-SRV01 ~]# ps -ef | grep vpc | grep tomcat
vpc      17600     1  6
15:35 pts/0    00:03:18
/opt/common/jre/bin/java -Dnop -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
-Djdk.tls.ephemeralDHKeySize=2048
-Dorg.apache.catalina.security.SecurityListener.UMASK=0027
-Djava.endorsed.dirs=/home/<USER>/local/tomcat/endorsed -classpath
/home/<USER>/local/tomcat/bin/bootstrap.jar:/home/<USER>/local/tomcat/bin/tomcat-juli.jar
-Dcatalina.base=/home/<USER>/local/tomcat -Dcatalina.home=/home/<USER>/local/tomcat
-Djava.io.tmpdir=/home/<USER>/local/tomcat/temp
org.apache.catalina.startup.Bootstrap start
96. 执行下面命令，停止tomcat进程。
kill -9 pid
pid为8中执行命令返回信息中获取的进程id。
进程保护机制会将进程重新启动。
97. 等待5分钟，执行下面命令，检查tomcat进程是否存在。
ps -ef | grep vpc | grep tomcat
- 是，执行11。
- 否，请联系技术支持工程师协助解决。
回显如下所示时，表示tomcat进程存在。
[vpc@PUB-SRV01 ~]# ps -ef | grep vpc | grep tomcat
vpc      17600     1  6
15:35 pts/0    00:03:18
/opt/common/jre/bin/java -Dnop -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
-Djdk.tls.ephemeralDHKeySize=2048
-Dorg.apache.catalina.security.SecurityListener.UMASK=0027
-Djava.endorsed.dirs=/home/<USER>/local/tomcat/endorsed -classpath
/home/<USER>/local/tomcat/bin/bootstrap.jar:/home/<USER>/local/tomcat/bin/tomcat-juli.jar
-Dcatalina.base=/home/<USER>/local/tomcat -Dcatalina.home=/home/<USER>/local/tomcat
-Djava.io.tmpdir=/home/<USER>/local/tomcat/temp
org.apache.catalina.startup.Bootstrap start
98. 等待10分钟，检查告警是否已自动清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。
******* ALM-1200030 tomcat进程无响应
##### 告警解释
系统每隔2分钟检查一次节点tomcat进程是否无响应，如果无响应，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200030 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源系统 | 告警来源。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 此条告警信息最近发生时间。 |
| 定位信息 | 产生告警信息的定位信息。 |
| 附加信息 | 告警附加信息。 |
##### 对系统的影响
进程无响应的节点无法正常提供服务，需尽快处理。
##### 可能原因
系统异常。
##### 处理步骤
99. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
100. 单击“登录”。
101. 在页面上方的菜单栏，选择“集中告警”。
102. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
103. 在“告警详情”页的“定位信息”中，“主机IP”即告警节点IP地址。
104. 使用“PuTTY”，登录告警节点。
默认帐号： vpc，默认密码： *****
105. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
106. 执行下面命令，获取tomcat进程pid。
ps -ef | grep vpc | grep tomcat
回显如下所示。其中，可获取tomcat进程pid为17600。
[vpc@PUB-SRV01 ~]# ps -ef | grep vpc | grep tomcat
vpc      17600     1  6
15:35 pts/0    00:03:18
/opt/common/jre/bin/java -Dnop -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
-Djdk.tls.ephemeralDHKeySize=2048
-Dorg.apache.catalina.security.SecurityListener.UMASK=0027
-Djava.endorsed.dirs=/home/<USER>/local/tomcat/endorsed -classpath
/home/<USER>/local/tomcat/bin/bootstrap.jar:/home/<USER>/local/tomcat/bin/tomcat-juli.jar
-Dcatalina.base=/home/<USER>/local/tomcat -Dcatalina.home=/home/<USER>/local/tomcat
-Djava.io.tmpdir=/home/<USER>/local/tomcat/temp
org.apache.catalina.startup.Bootstrap start
107. 执行下面命令，停止tomcat进程。
kill -9 pid
pid为8中执行命令返回信息中获取的进程id。
进程保护机制会将进程重新启动。
108. 等待5分钟，执行下面命令，检查tomcat进程是否存在。
ps -ef | grep vpc | grep tomcat
- 是，执行11。
- 否，请联系技术支持工程师协助解决。
回显如下所示时，表示tomcat进程存在。
[vpc@PUB-SRV01 ~]# ps -ef | grep vpc | grep tomcat
vpc      17600     1  6
15:35 pts/0    00:03:18
/opt/common/jre/bin/java -Dnop -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
-Djdk.tls.ephemeralDHKeySize=2048
-Dorg.apache.catalina.security.SecurityListener.UMASK=0027
-Djava.endorsed.dirs=/home/<USER>/local/tomcat/endorsed -classpath
/home/<USER>/local/tomcat/bin/bootstrap.jar:/home/<USER>/local/tomcat/bin/tomcat-juli.jar
-Dcatalina.base=/home/<USER>/local/tomcat -Dcatalina.home=/home/<USER>/local/tomcat
-Djava.io.tmpdir=/home/<USER>/local/tomcat/temp
org.apache.catalina.startup.Bootstrap start
109. 等待10分钟，检查告警是否已自动清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。
******* ALM-1200032 zookeeper进程CPU占用率超过阈值
##### 告警解释
系统每隔2分钟检查一次节点zookeeper进程cpu占用是否超过设定的阈值，如果在30分钟内都超过阈值，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200032 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源系统 | 告警来源。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 此条告警信息最近发生时间。 |
| 定位信息 | 产生告警信息的定位信息。 |
| 附加信息 | 告警附加信息。 |
##### 对系统的影响
对系统正常运行无影响，但后续如果业务量增加，可能导致无法处理新增业务。需尽快定位原因。
##### 可能原因
- zookeeper进程运行异常。
- 服务容量不够，处理能力不足，无法满足业务需求。
##### 处理步骤
110. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
111. 单击“登录”。
112. 在页面上方的菜单栏，选择“集中告警”。
113. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
114. 在“告警详情”页的“定位信息”中，“主机IP”即告警节点IP地址。
115. 使用“PuTTY”，登录告警节点。
默认帐号： vpc，默认密码： *****
116. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
117. 执行下面命令，获取zookeeper进程pid。
ps -ef |grep zookeeper
回显如下所示。其中，可获取zookeeper进程pid为7228。
[vpc@PUB-SRV01 ~]$ ps  -ef | grep zookeeper
vpc       7228     1  015:04 ?        00:00:07
/opt/common/jre/bin/java -Dzookeeper.log.dir=logs -Dzookeeper.root.logger=INFO,ROLLINGFILE
-cp /home/<USER>/zookeeper/bin/../build/classes:/home/<USER>/zookeeper/bin/../build/lib/*.jar:/home/<USER>/zookeeper/bin/../lib/slf4j-log4j12-1.7.25.jar:/home/<USER>/zookeeper/bin/../lib/slf4j-api-1.7.25.jar:/home/<USER>/zookeeper/bin/../lib/netty-3.10.6.Final.jar:/home/<USER>/zookeeper/bin/../lib/log4j-1.2.17.jar:/home/<USER>/zookeeper/bin/../lib/jline-0.9.94.jar:/home/<USER>/zookeeper/bin/../lib/audience-annotations-0.5.0.jar:/home/<USER>/zookeeper/bin/../zookeeper-3.4.13.jar:/home/<USER>/zookeeper/bin/../src/java/lib/*.jar:/home/<USER>/zookeeper/bin/../conf:
org.apache.zookeeper.server.quorum.QuorumPeerMain
/home/<USER>/zookeeper/bin/../conf/zoo.cfg
118. 执行下面命令，停止zookeeper进程。
kill -9 pid
pid为8中执行命令返回信息中获取的进程id。
进程保护机制会将进程重新启动。
119. 等待5分钟，执行下面命令，检查zookeeper进程是否存在。
ps -ef |grep zookeeper
- 是，执行11。
- 否，请联系技术支持工程师协助解决。
回显如下所示时，表示zookeeper进程存在。
[vpc@PUB-SRV01 ~]$ ps  -ef | grep zookeeper
vpc       7228     1  015:04 ?        00:00:07
/opt/common/jre/bin/java -Dzookeeper.log.dir=logs -Dzookeeper.root.logger=INFO,ROLLINGFILE
-cp /home/<USER>/zookeeper/bin/../build/classes:/home/<USER>/zookeeper/bin/../build/lib/*.jar:/home/<USER>/zookeeper/bin/../lib/slf4j-log4j12-1.7.25.jar:/home/<USER>/zookeeper/bin/../lib/slf4j-api-1.7.25.jar:/home/<USER>/zookeeper/bin/../lib/netty-3.10.6.Final.jar:/home/<USER>/zookeeper/bin/../lib/log4j-1.2.17.jar:/home/<USER>/zookeeper/bin/../lib/jline-0.9.94.jar:/home/<USER>/zookeeper/bin/../lib/audience-annotations-0.5.0.jar:/home/<USER>/zookeeper/bin/../zookeeper-3.4.13.jar:/home/<USER>/zookeeper/bin/../src/java/lib/*.jar:/home/<USER>/zookeeper/bin/../conf:
org.apache.zookeeper.server.quorum.QuorumPeerMain
/home/<USER>/zookeeper/bin/../conf/zoo.cfg
120. 等待10分钟，检查告警是否已自动清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。
5.2.9.6 ALM-1200033 zookeeper进程内存占用率超过阈值
##### 告警解释
系统每隔2分钟检查一次节点zookeeper进程内存占用是否超过设定的阈值，如果在30分钟内持续超过阈值，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200033 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源系统 | 告警来源。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 此条告警信息最近发生时间。 |
| 定位信息 | 产生告警信息的定位信息。 |
| 附加信息 | 告警附加信息。 |
##### 对系统的影响
对系统正常运行无影响，但后续如果业务量增加，可能导致无法处理新增业务。需尽快处理。
##### 可能原因
- zookeeper进程运行异常。
- 服务容量不够，处理能力不足，无法满足业务需求。
##### 处理步骤
121. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
122. 单击“登录”。
123. 在页面上方的菜单栏，选择“集中告警”。
124. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
125. 在“告警详情”页的“定位信息”中，“主机IP”即告警节点IP地址。
126. 使用“PuTTY”，登录告警节点。
默认帐号： vpc，默认密码： *****
127. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
128. 执行下面命令，获取zookeeper进程pid。
ps -ef |grep zookeeper
回显如下所示。其中，可获取zookeeper进程pid为7228。
[vpc@PUB-SRV01 ~]$ ps  -ef | grep zookeeper
vpc       7228     1  015:04 ?        00:00:07
/opt/common/jre/bin/java -Dzookeeper.log.dir=logs -Dzookeeper.root.logger=INFO,ROLLINGFILE
-cp /home/<USER>/zookeeper/bin/../build/classes:/home/<USER>/zookeeper/bin/../build/lib/*.jar:/home/<USER>/zookeeper/bin/../lib/slf4j-log4j12-1.7.25.jar:/home/<USER>/zookeeper/bin/../lib/slf4j-api-1.7.25.jar:/home/<USER>/zookeeper/bin/../lib/netty-3.10.6.Final.jar:/home/<USER>/zookeeper/bin/../lib/log4j-1.2.17.jar:/home/<USER>/zookeeper/bin/../lib/jline-0.9.94.jar:/home/<USER>/zookeeper/bin/../lib/audience-annotations-0.5.0.jar:/home/<USER>/zookeeper/bin/../zookeeper-3.4.13.jar:/home/<USER>/zookeeper/bin/../src/java/lib/*.jar:/home/<USER>/zookeeper/bin/../conf:
org.apache.zookeeper.server.quorum.QuorumPeerMain
/home/<USER>/zookeeper/bin/../conf/zoo.cfg
129. 执行下面命令，停止zookeeper进程。
kill -9 pid
pid为8中执行命令返回信息中获取的进程id。
进程保护机制会将进程重新启动。
130. 等待5分钟，执行下面命令，检查zookeeper进程是否存在。
ps -ef |grep zookeeper
- 是，执行11。
- 否，请联系技术支持工程师协助解决。
回显如下所示时，表示zookeeper进程存在。
[vpc@PUB-SRV01 ~]$ ps  -ef | grep zookeeper
vpc       7228     1  015:04 ?        00:00:07
/opt/common/jre/bin/java -Dzookeeper.log.dir=logs -Dzookeeper.root.logger=INFO,ROLLINGFILE
-cp /home/<USER>/zookeeper/bin/../build/classes:/home/<USER>/zookeeper/bin/../build/lib/*.jar:/home/<USER>/zookeeper/bin/../lib/slf4j-log4j12-1.7.25.jar:/home/<USER>/zookeeper/bin/../lib/slf4j-api-1.7.25.jar:/home/<USER>/zookeeper/bin/../lib/netty-3.10.6.Final.jar:/home/<USER>/zookeeper/bin/../lib/log4j-1.2.17.jar:/home/<USER>/zookeeper/bin/../lib/jline-0.9.94.jar:/home/<USER>/zookeeper/bin/../lib/audience-annotations-0.5.0.jar:/home/<USER>/zookeeper/bin/../zookeeper-3.4.13.jar:/home/<USER>/zookeeper/bin/../src/java/lib/*.jar:/home/<USER>/zookeeper/bin/../conf:
org.apache.zookeeper.server.quorum.QuorumPeerMain
/home/<USER>/zookeeper/bin/../conf/zoo.cfg
131. 等待10分钟，检查告警是否已自动清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。
******* ALM-1200034 zookeeper进程重复运行
##### 告警解释
系统每隔2分钟检查一次节点是否存在多余的zookeeper进程，如果存在多于1个进程，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200034 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源系统 | 告警来源。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 此条告警信息最近发生时间。 |
| 定位信息 | 产生告警信息的定位信息。 |
| 附加信息 | 告警附加信息。 |
##### 对系统的影响
多个zookeeper进程运行时，只有一个能提供服务，对系统正常运行无影响。
##### 可能原因
同时启动了多个zookeeper进程。
##### 处理步骤
132. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
133. 单击“登录”。
134. 在页面上方的菜单栏，选择“集中告警”。
135. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
136. 在“告警详情”页的“定位信息”中，“主机IP”即告警节点IP地址。
137. 使用“PuTTY”，登录告警节点。
默认帐号： vpc，默认密码： *****
138. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
139. 执行下面命令，获取所有zookeeper进程pid。
ps -ef |grep zookeeper
回显如下所示。其中，可获取zookeeper进程pid为7228。
[vpc@PUB-SRV01 ~]$ ps  -ef | grep zookeeper
vpc       7228     1  015:04 ?        00:00:07
/opt/common/jre/bin/java -Dzookeeper.log.dir=logs -Dzookeeper.root.logger=INFO,ROLLINGFILE
-cp /home/<USER>/zookeeper/bin/../build/classes:/home/<USER>/zookeeper/bin/../build/lib/*.jar:/home/<USER>/zookeeper/bin/../lib/slf4j-log4j12-1.7.25.jar:/home/<USER>/zookeeper/bin/../lib/slf4j-api-1.7.25.jar:/home/<USER>/zookeeper/bin/../lib/netty-3.10.6.Final.jar:/home/<USER>/zookeeper/bin/../lib/log4j-1.2.17.jar:/home/<USER>/zookeeper/bin/../lib/jline-0.9.94.jar:/home/<USER>/zookeeper/bin/../lib/audience-annotations-0.5.0.jar:/home/<USER>/zookeeper/bin/../zookeeper-3.4.13.jar:/home/<USER>/zookeeper/bin/../src/java/lib/*.jar:/home/<USER>/zookeeper/bin/../conf:
org.apache.zookeeper.server.quorum.QuorumPeerMain
/home/<USER>/zookeeper/bin/../conf/zoo.cfg
140. 执行下面命令，停止所有zookeeper进程。
kill -9 pid
pid为8中执行命令返回信息中获取的进程id。
进程保护机制会将进程重新启动。
141. 等待5分钟，执行下面命令，检查是否只有一个zookeeper进程存在。
ps -ef |grep zookeeper
- 是，执行11。
- 否，请联系技术支持工程师协助解决。
回显如下所示时，表示只有一个zookeeper进程存在。
[vpc@PUB-SRV01 ~]$ ps  -ef | grep zookeeper
vpc       7228     1  015:04 ?        00:00:07
/opt/common/jre/bin/java -Dzookeeper.log.dir=logs -Dzookeeper.root.logger=INFO,ROLLINGFILE
-cp /home/<USER>/zookeeper/bin/../build/classes:/home/<USER>/zookeeper/bin/../build/lib/*.jar:/home/<USER>/zookeeper/bin/../lib/slf4j-log4j12-1.7.25.jar:/home/<USER>/zookeeper/bin/../lib/slf4j-api-1.7.25.jar:/home/<USER>/zookeeper/bin/../lib/netty-3.10.6.Final.jar:/home/<USER>/zookeeper/bin/../lib/log4j-1.2.17.jar:/home/<USER>/zookeeper/bin/../lib/jline-0.9.94.jar:/home/<USER>/zookeeper/bin/../lib/audience-annotations-0.5.0.jar:/home/<USER>/zookeeper/bin/../zookeeper-3.4.13.jar:/home/<USER>/zookeeper/bin/../src/java/lib/*.jar:/home/<USER>/zookeeper/bin/../conf:
org.apache.zookeeper.server.quorum.QuorumPeerMain
/home/<USER>/zookeeper/bin/../conf/zoo.cfg
142. 等待10分钟，检查告警是否已自动清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。
******* ALM-1200035 zookeeper进程无响应
##### 告警解释
系统每隔2分钟检查一次节点zookeeper进程是否无响应，如果无响应，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200035 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源系统 | 告警来源。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 此条告警信息最近发生时间。 |
| 定位信息 | 产生告警信息的定位信息。 |
| 附加信息 | 告警附加信息。 |
##### 对系统的影响
进程无响应的节点无法正常提供服务，需尽快处理。
##### 可能原因
系统异常。
##### 处理步骤
143. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
144. 单击“登录”。
145. 在页面上方的菜单栏，选择“集中告警”。
146. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
147. 在“告警详情”页的“定位信息”中，“主机IP”即告警节点IP地址。
148. 使用“PuTTY”，登录告警节点。
默认帐号： vpc，默认密码： *****
149. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
150. 执行下面命令，获取zookeeper进程pid。
ps -ef |grep zookeeper
回显如下所示。其中，可获取zookeeper进程pid为7228。
[vpc@PUB-SRV01 ~]$ ps  -ef | grep zookeeper
vpc       7228     1  015:04 ?        00:00:07
/opt/common/jre/bin/java -Dzookeeper.log.dir=logs -Dzookeeper.root.logger=INFO,ROLLINGFILE
-cp /home/<USER>/zookeeper/bin/../build/classes:/home/<USER>/zookeeper/bin/../build/lib/*.jar:/home/<USER>/zookeeper/bin/../lib/slf4j-log4j12-1.7.25.jar:/home/<USER>/zookeeper/bin/../lib/slf4j-api-1.7.25.jar:/home/<USER>/zookeeper/bin/../lib/netty-3.10.6.Final.jar:/home/<USER>/zookeeper/bin/../lib/log4j-1.2.17.jar:/home/<USER>/zookeeper/bin/../lib/jline-0.9.94.jar:/home/<USER>/zookeeper/bin/../lib/audience-annotations-0.5.0.jar:/home/<USER>/zookeeper/bin/../zookeeper-3.4.13.jar:/home/<USER>/zookeeper/bin/../src/java/lib/*.jar:/home/<USER>/zookeeper/bin/../conf:
org.apache.zookeeper.server.quorum.QuorumPeerMain
/home/<USER>/zookeeper/bin/../conf/zoo.cfg
151. 执行下面命令，停止zookeeper进程。
kill -9 pid
pid为8中执行命令返回信息中获取的进程id。
进程保护机制会将进程重新启动。
152. 等待5分钟，执行下面命令，检查zookeeper进程是否存在。
ps -ef |grep zookeeper
- 是，执行11。
- 否，请联系技术支持工程师协助解决。
回显如下所示时，表示zookeeper进程存在。
[vpc@PUB-SRV01 ~]$ ps  -ef | grep zookeeper
vpc       7228     1  015:04 ?        00:00:07
/opt/common/jre/bin/java -Dzookeeper.log.dir=logs -Dzookeeper.root.logger=INFO,ROLLINGFILE
-cp /home/<USER>/zookeeper/bin/../build/classes:/home/<USER>/zookeeper/bin/../build/lib/*.jar:/home/<USER>/zookeeper/bin/../lib/slf4j-log4j12-1.7.25.jar:/home/<USER>/zookeeper/bin/../lib/slf4j-api-1.7.25.jar:/home/<USER>/zookeeper/bin/../lib/netty-3.10.6.Final.jar:/home/<USER>/zookeeper/bin/../lib/log4j-1.2.17.jar:/home/<USER>/zookeeper/bin/../lib/jline-0.9.94.jar:/home/<USER>/zookeeper/bin/../lib/audience-annotations-0.5.0.jar:/home/<USER>/zookeeper/bin/../zookeeper-3.4.13.jar:/home/<USER>/zookeeper/bin/../src/java/lib/*.jar:/home/<USER>/zookeeper/bin/../conf:
org.apache.zookeeper.server.quorum.QuorumPeerMain
/home/<USER>/zookeeper/bin/../conf/zoo.cfg
153. 等待10分钟，检查告警是否已自动清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。
******* ALM-1200053 Neutron接口调用失败
##### 告警解释
系统每隔30秒检查一次VPC Service节点与FusionSphere OpenStack节点的通信状态，如果连续3次持续不通，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200053 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源系统 | 告警来源。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 此条告警信息最近发生时间。 |
| 定位信息 | 产生告警的定位信息。 |
| 附加信息 | 告警附加信息。 |
##### 对系统的影响
VPC Service节点无法与FSP节点通信，将导致网络配置无法处理，需尽快处理。
##### 可能原因
- VPC Service节点的网络不通。
- FusionSphere OpenStack Neutron服务异常。
##### 处理步骤
154. 参考ALM-73203 组件故障和ALM-73201 HAProxy代理服务不可用章节，排查是否为FusionSphere OpenStack Neutron服务异常导致的告警。
- 是，请参考对应章节进行处理。
##### - 否，可能原因为VPC Service节点的网络不通，执行2进行处理。
- 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
- 单击“登录”。
- 在页面上方的菜单栏，选择“集中告警”。
- 在告警列表中，找到待处理的告警记录，单击“名称”列，弹出“告警详情和处理建议”窗口。
- 在“告警详情”页的“定位信息”中，“主机IP”即告警节点IP地址。
当出现多条相同告警时，告警信息被汇聚。此时在“被汇聚告警”页签查看告警节点IP地址。
- 使用“PuTTY”，登录告警节点。
默认帐号： vpc，默认密码： *****
- 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
- 执行以下命令，获取neutron域名。
cat /home/<USER>/network/WEB-INF/classes/config/networkConfig.xml | grep neutron-endpoint
回显如下所示。
[vpc@PUB-SRV01 ~]$cat /home/<USER>/network/WEB-INF/classes/config/networkConfig.xml | grep
neutron-endpoint
<neutron-endpoint>https://network.az0.dc0.type2env.network.com:443&lt;/neutron-endpoint>
其中，neutron域名为network.az0.dc0.type2env.network.com。
- 执行以下命令，PING9中获取到的neutron域名，查看是否可以PING通。
ping neutron域名
举例：
ping network.az0.dc0.type2env.network.com
回显如下所示，表示可以PING通。
[vpc@PUB-SRV01 ~]$ ping network.az0.dc0.type2env.network.com
PING network.az0.dc0.type2env.network.com (**************) 56(84) bytes of data.
64 bytes from ************** (**************): icmp_seq=1 ttl=63 time=0.151 ms
64 bytes from ************** (**************): icmp_seq=2 ttl=63 time=0.195 ms
64 bytes from ************** (**************): icmp_seq=3 ttl=63 time=0.196 ms
^C
--- network.az0.dc0.type2env.network.com ping statistics ---
3 packets transmitted, 3 received, 0% packet loss, time 2001ms
rtt min/avg/max/mdev = 0.151/0.180/0.196/0.026 ms
- 如果neutron域名无法PING通，请执行11，继续排查本节点的DNS是否设置正确。
- 如果neutron域名可以PING通，请联系技术支持工程师协助解决。
- 排查本节点的DNS是否设置正确。
- 执行以下命令，查看DNS配置。
cat /etc/resolv.conf | grep nameserver
回显如下所示。
[vpc@PUB-SRV01 ~]$ cat /etc/resolv.conf | grep nameserver
nameserver 192.168.109.124
nameserver 192.168.109.125
- 与LLD中规划的DNS-Internal节点信息比对。
- 如果不一致，按照LLD中规划信息修改resolv.conf文件。
- 如果一致，说明DNS设置正确，请联系技术支持工程师协助解决。
- 执行以下命令，输入节点root密码，修改DNS配置。
sudo vim /etc/resolv.conf
- 按Insert键开始修改文件内容。
- 按Esc键，输入:wq并回车，保存文件的修改。
- 再次尝试PING neutron域名，如果仍然无法PING通，请联系技术支持工程师协助解决。
- 使用“PuTTY”，登录FusionSphere OpenStack节点。
默认帐号： fsp，默认密码： *****
- 导入环境变量。
具体操作请参见导入环境变量章节。
- 执行下面命令，其中域名使用9中获取的neutron域名。
curl -i --insecure -X GET 'https://neutron域名:443' -H "Accept: application/json"
举例：
curl -i --insecure -X GET 'https://network.az0.dc0.type2env.network.com:443' -H "Accept: application/json"
回显如下所示。
[vpc@PUB-SRV01 ~]$ curl -i --insecure -X GET 'https://network.az0.dc0.type2env.network.com:443' -H "Accept: application/json"
HTTP/1.1 200 OK
如果该接口返回码非200，请联系技术支持工程师协助解决。
- 等待3分钟后检查告警是否自动清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。
******** ALM-1200054 公网IP地址不足
##### 告警解释
弹性IP审计模块每天2:00校验IP地址池可用IP剩余量是否低于告警值（告警值默认为IP地址池内IP总数的10%），如果低于告警值，产生此审计告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200054 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 告警名称 | 告警名称 | 此条告警信息的名称。 |
| 来源系统 | 来源系统 | 告警来源。 |
| IP地址 | IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 最近发生时间 | 此条告警信息最近发生时间。 |
| 定位信息 | 网络ID | 产生告警信息的外部网络ID。 |
| 定位信息 | 子网ID | 产生告警信息的子网ID。 |
| 附加信息 | 附加信息 | 告警附加信息。 |
##### 对系统的影响
EIP地址不足，导致无法申请EIP资源。
##### 可能原因
弹性IP使用率较大，造成资源不足。
##### 处理步骤
新增弹性IP子网网段
155. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
156. 在页面上方的导航栏，选择“运维地图”。
157. 在“运维地图”页面右边的“快速访问”导航栏中，单击“ServiceOM”进入Service OM界面。
158. 选择“资源 > 网络资源”。
159. 选择“外部网络”页签。
160. 在外部网络列表中找到对应的EIP外部网络，在其所在行操作列，选择“更多 > 创建IPv4子网”。
161. 根据环境的网络规划分配子网网段，创建弹性IP外部网络的子网。
162. Region Type I场景下，还需切换至被级联层创建弹性IP外部网络的子网。
手动屏蔽该EIP外部网络子网网段的告警
163. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
164. 单击“登录”。
165. 在页面上方的菜单栏，选择“集中告警”。
166. 在告警列表中，找到待处理的告警记录，单击“”，选择“设置屏蔽规则”。
167. 在“设置屏蔽规则”窗口中设置如下屏蔽规则，屏蔽该EIP外部网络子网网段的告警。
- 屏蔽：“云服务”告警分组名称上的“公网IP地址不足”告警
- 条件：定位信息 包含 对应告警的网络ID；子网ID；服务名称
- 屏蔽后的告警：显示在“被屏蔽告警”中
当出现多条相同告警时，告警信息被汇聚。此时在“被汇聚告警”页签查看告警定位信息。
168. 单击“确定”，完成屏蔽规则设置。
告警清除
进入告警监控页面，在告警列表中选中该条告警，单击“清除”。
##### 参考信息
无。
******** ALM-1200074 VPC数据库访问失败
##### 告警解释
系统每隔60秒检查一次VPC Service访问VPC数据库的连接状态，如果持续不通，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200074 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源系统 | 告警来源。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 此条告警信息最近发生时间。 |
| 定位信息 | 产生告警的定位信息。 |
| 附加信息 | 告警附加信息。 |
##### 对系统的影响
VPC Service节点无法访问VPC DB，将导致虚拟私有云功能不可用，需尽快处理。
##### 可能原因
- VPC Service节点的网络不通。
- VPC DB数据库服务异常。
##### 处理步骤
169. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
170. 单击“登录”。
171. 在页面上方的菜单栏，选择“集中告警”。
172. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
173. 在“告警详情”页的“定位信息”中，“主机IP”即告警节点IP地址。
当出现多条相同告警时，告警信息被汇聚。此时在“被汇聚告警”页签查看告警节点IP地址。
174. 使用“PuTTY”，登录告警节点。
默认帐号： vpc，默认密码： *****
175. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
176. 执行下面命令，获取数据库地址。
cat /home/<USER>/network/WEB-INF/classes/config/extension.properties |grep network.db.url
回显如下所示，记录数据库地址 pub-db.regionid.network.com。
network.db.url=jdbc\:postgresql\://pub-db.regionid.network.com:5432/vpc?use_boolean\=true
177. 执行以下命令，获取数据库节点的浮动IP。
ping 数据库地址
其中，数据库地址为8中记录的地址。
例如：
ping pub-db.regionid.network.com
回显如下所示，记录数据库节点的浮动IP **************。
[vpc@PUB-SRV01 config]$ ping pub-db.regionid.network.com
PING pub-db.regionid.network.com (**************) 56(84) bytes of data.
64 bytes from ************** (**************): icmp_seq=1 ttl=64 time=0.193 ms
64 bytes from ************** (**************): icmp_seq=2 ttl=64 time=0.271 ms
178. 使用“PuTTY”，登录PUB-DB-01或PUB-DB-02（VPC DB节点）。
默认帐号： vpc，默认密码： *****
179. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
180. 执行ifconfig命令，查看浮动IP地址是否和获取到的地址一致。
回显如下所示：
eth0: flags=4163<UP,BROADCAST,RUNNING,MULTICAST>  mtu 1500
inet ***************  netmask *************  broadcast ***************
inet6 fe80::f816:3eff:feab:d1b9  prefixlen 64  scopeid 0x20<link>
ether fa:16:3e:ab:d1:b9  txqueuelen 1000  (Ethernet)
RX packets 61250645  bytes 20429432458 (19.0 GiB)
RX errors 0  dropped 0  overruns 0  frame 0
TX packets 53692510  bytes 27388569156 (25.5 GiB)
TX errors 0  dropped 0 overruns 0  carrier 0  collisions 0
eth0:1: flags=4163<UP,BROADCAST,RUNNING,MULTICAST>  mtu 1500
inet **************  netmask *************  broadcast ***************
ether fa:16:3e:ab:d1:b9  txqueuelen 1000  (Ethernet)
lo: flags=73<UP,LOOPBACK,RUNNING>  mtu 65536
inet 127.0.0.1  netmask *********
inet6 ::1  prefixlen 128  scopeid 0x10<host>
loop  txqueuelen 1000  (Local Loopback)
RX packets 8209772  bytes 4629798489 (4.3 GiB)
RX errors 0  dropped 0  overruns 0  frame 0
TX packets 8209772  bytes 4629798489 (4.3 GiB)
TX errors 0  dropped 0 overruns 0  carrier 0  collisions 0
lo:1: flags=73<UP,LOOPBACK,RUNNING>  mtu 65536
inet **************  netmask 0.0.0.0
loop  txqueuelen 1000  (Local Loopback)
- 如果IP地址一致，请执行13检查DB服务状态。
- 如果IP地址不一致，请联系技术支持工程师协助解决。
181. 执行以下命令，检查DB服务状态。
sudo service had query
根据提示输入root用户密码后，正常回显如下：
[root@PUB-DB01 vpc]# sudo service had query
NODE                     ROLE           PHASE         RESS            VER             START
PUB-DB01(PUB-DB01)      active         Actived       normal          V100R001C01     2019-06-13 19:36:31
PUB-DB02(PUB-DB02)      standby        Deactived     normal          V100R001C01     2019-06-13 19:37:36
--------------------------------------------------------------------------------------------------------
ID    RES                     STAT             RET             TYPE
PUB-DB01(PUB-DB01):       1     exfloatip               Normal           Normal          Single_active
2     gaussDB                 Normal           Active_normal   Active_standby
PUB-DB02(PUB-DB02):       1     exfloatip               Normal           Stopping        Single_active
2     gaussDB                 Normal           Standby_normal  Active_standby
- 如果RESS列为normal，表示服务状态正常。
- 如果RESS列不是normal，表示服务状态异常，请联系技术支持工程师协助解决。
182. 等待2分钟后检查告警是否自动清除。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。
******** 附录
< 上一节
********.1 导入环境变量
操作场景
系统在安装完成后，默认打开鉴权模式，在执行CPS及OpenStack命令前，需要先导入环境变量才能操作。
系统提供了导入环境变量的快捷脚本，但需要先确认默认的环境变量是否与系统匹配。
前提条件
183. 已登录AZ内任意一台主机。
184. 已完成所有主机的安装。
185. 已完成FusionSphere OpenStack的安装部署。
操作步骤
186. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：*****。
187. 执行以下命令，防止系统超时退出。
TMOUT=0
188. 确认是否直接执行导入当前系统保存的环境变量脚本。
默认环境变量如下：
export OS_USERNAME=dc1_admin
export OS_TENANT_NAME=dc_system_dc1
export OS_REGION_NAME=az1.dc1
export OS_AUTH_URL=https://identity.az1.dc1.domainname.com:443/identity/v2.0
export OS_PASSWORD=$password
export NOVA_ENDPOINT_TYPE=internalURL
export OS_ENDPOINT_TYPE=internalURL
export CINDER_ENDPOINT_TYPE=internalURL
export OS_VOLUME_API_VERSION=2
export BASE_BOND=brcps
环境变量中各参数含义可参考表1。
如果已修改默认环境变量，可通过cat /usr/bin/install_tool/set_env | grep export命令查看当前环境变量。
- 是，执行8。
- 否，执行4。
189. 执行以下命令，使用VI编辑器打开系统预置的环境变量脚本。
vi /usr/bin/install_tool/set_env
190. 按“i”进入编辑模式。
191. 修改环境变量中的以下斜体内容为实际配置的AZ名称和DC名称。
192. export OS_AUTH_URL=https://identity.az1.dc1.domainname.com:443/identity/v2.0
193. export OS_USERNAME=dc1_admin
194. export OS_TENANT_NAME=dc_system_dc1
export OS_REGION_NAME=az1.dc1
环境变量中的参数解释如表1所示。
| 表1 环境变量参数说明 | 表1 环境变量参数说明 |
| --- | --- |
| 参数 | 含义 |
| OS_AUTH_URL | 鉴权地址，对应Keystone服务的endpoint。<br>使用publicURL的endpoint。<br>需修改的部分包括AZ名、DC名、以及域名后缀。 |
| OS_USERNAME | 执行操作时使用的DC管理员的帐号。DC管理员在安装完成后自动创建，格式为dcname_admin。例如dcname为dc1，则DC管理员用户名为dc1_admin。 |
| OS_TENANT_NAME | 执行操作的DC管理员所属租户信息。租户信息在安装完成后自动创建，格式为dc_system_dcname。 |
| OS_REGION_NAME | AZ的域名，如az1.dc1。 |
| OS_ENDPOINT_TYPE | endpoint的类型，在使用OpenStack的命令时需要导入，请使用“internalURL”。 |
| NOVA_ENDPOINT_TYPE | endpoint的类型，在使用OpenStack的命令时需要导入，请使用“internalURL”。 |
| CINDER_ENDPOINT_TYPE | endpoint的类型，在使用OpenStack的命令时需要导入，请使用“internalURL”。 |
| OS_VOLUME_API_VERSION | 使用的VOLUME版本，需要输入2，表示使用V2版本。 |
195. 按“Esc”，输入“:wq”，并按“Enter”。
保存设置并退出VI编辑器。
196. 执行以下命令，并按提示输入“OS_USERNAME”的密码，导入环境变量。
source set_env
默认密码：“*****”。
回显如下类似信息：
...
Enter CPS_USERNAME=
197. 输入CPS鉴权用户“cps_admin”，并按“Enter”。
回显如下类似信息：
Enter CPS_PASSWORD=
198. 输入“cps_admin”的密码，并按“Enter”。
默认密码：“*****”。
输入完成后如果有cps host-list和nova list命令的自动回显信息，则表示环境变量导入成功。
5.2.10 弹性负载均衡
******** ALM-1223005 数据库连接异常
##### 告警解释
ELB API管理节点netcluster_elb_api_vm每10秒检测后端数据库服务，如果服务显示为down，上报数据库异常，生成此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1223005 | 次要 | 是 |
告警的级别不一定和告警定义时的级别一致，每次发送告警可根据需要动态调整告警级别。
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 此条告警信息的名称。 |
| 告警源 | 告警来源。 |
| 来源系统 | 对接系统的名称。 |
| 最近发生时间 | 此条告警信息最近一次发生时间。 |
| 定位信息 | 云服务：服务名称。 |
| 定位信息 | 节点类型：告警资源类型。 |
| 附加信息 | 云服务：服务名称。 |
| 附加信息 | 服务：设备节点类型，例如mgmt, lvs, nginx等。 |
| 附加信息 | 本端地址：产生告警信息的ELB API虚拟机的IP地址。 |
| 附加信息 | 对端地址：产生告警信息的ELB虚拟机的IP地址。 |
##### 对系统的影响
单节点数据库对业务无影响，双节点数据库异常会导致业务流量异常。
##### 可能原因
ELB API管理节点netcluster_elb_api_vm健康检查连接数据库出现异常。
##### 处理步骤
199. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
200. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
201. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
202. 查询以下告警信息。
附加信息：
- 对端地址
- 本端地址
203. 使用PuTTY，登录异常数据库节点。
登录地址：4中查出的对端地址。
默认帐户：elb
默认密码：*****。
如果数据库节点无法登录，请联系技术工程师协助解决。
204. 执行以下命令，切换到root用户。root帐户默认密码：*****。
sudo su root
205. 执行以下命令，查看gaussdb进程状态是否为正常。
service had query
命令回显如下，则gaussdb进程状态为normal。
- 是，请执行8。
- 否，请联系技术支持工程师协助解决。
206. 观察告警信息是否清除。
- 是，处理结束。
- 否，执行9。
207. 使用PuTTY，登录ELB管理节点。
登录地址：4中查询到本端地址。
默认帐户：elb
默认密码：*****。
208. 执行以下命令重新加载nginx进程。
cd /usr/local/NSP/etc/elb/bin
./elb_process.sh reload
209. 观察告警信息是否清除。
- 是，处理结束。
- 否，请联系技术工程师协助解决。
##### 参考信息
无。
5.2.10.2 ALM-1223006 ETCD集群健康检查告警
##### 告警解释
ELB管理节点、LVS节点或Nginx节点连接ETCD节点失败，生成此告警。
管理节点服务正常时，每隔15秒会对ETCD节点连接，如果连续三次一半以上的ETCD节点都连接失败，产生此告警。
LVS、Nginx节点服务正常时，每隔5秒会对ETCD节点连接，如果连续三次所有的ETCD节点都连接失败，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1223006 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 此条告警信息的名称。 |
| 告警源 | 告警来源。 |
| 来源系统 | 对接系统的名称。 |
| 最近发生时间 | 此条告警信息最近一次发生时间。 |
| 定位信息 | 云服务：服务名称。 |
| 定位信息 | 节点类型：告警资源类型。 |
| 附加信息 | 云服务：服务名称。 |
| 附加信息 | 服务：设备节点类型，例如mgmt, lvs, nginx等。 |
| 附加信息 | 本端地址：产生告警信息的ELB API虚拟机的IP地址。 |
| 附加信息 | 对端地址：产生告警信息的ELB虚拟机的IP地址。 |
##### 对系统的影响
210. 影响正常业务流程处理
211. 配置下发失败
212. 配置回滚失败
213. 配置不一致
214. 拉取配置文件失败
215. 管理节点推送配置文件失败
216. ELB TCP流量异常
217. ELB HTTP流量异常
##### 可能原因
多台ETCD服务器异常导致ETCD服务集群异常。
##### 处理步骤
218. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
219. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
220. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
221. 查询以下告警信息。
附加信息：对端地址
222. 使用PuTTY，登录异常ETCD节点。
登录地址：4中查出的对端地址。
默认帐户：elb
默认密码：*****。
223. 执行以下命令，切换到root用户。root帐户默认密码：*****。
sudo su root
若ETCD节点不能正常登录，请联系技术工程师协助解决。
224. 分别在所有ETCD节点上执行以下命令，查看ETCD进程是否正常启动。
ps -ef|grep etcd
命令回显如下，ETCD进程存在，表示ETCD进程正常启动。
- 是，请联系技术工程师协助解决。
- 否，请执行8
225. 执行以下命令，重新启动ETCD进程。
cd /usr/local/NSP/scripts
sh start_etcd.sh
重启ETCD进程不会对其他业务造成影响。
226. 观察告警是否清除。
- 是，处理结束。
- 否，请联系技术工程师协助解决。
##### 参考信息
无。
******** ALM-1223013 集群中存在主机连接异常
##### 告警解释
ELB每10秒检测后端LVS、 Nginx或API节点，如果后端服务端口检测异常，生成此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1223013 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 此条告警信息的名称。 |
| 告警源 | 告警来源。 |
| 来源系统 | 对接系统的名称。 |
| 最近发生时间 | 此条告警信息最近一次发生时间。 |
| 定位信息 | 云服务：服务名称。 |
| 定位信息 | 节点类型：告警资源类型。 |
| 附加信息 | 云服务：服务名称。 |
| 附加信息 | 服务：设备节点类型，例如mgmt, lvs, nginx等。 |
| 附加信息 | 本端地址：产生告警信息的ELB API虚拟机的IP地址。 |
| 附加信息 | 对端地址：产生告警信息的ELB虚拟机的IP地址。 |
##### 对系统的影响
影响现有业务性能或者业务无法处理。
##### 可能原因
管理节点健康检查时连接集群主机出现异常。
##### 处理步骤
227. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
228. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
229. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
230. 查询以下告警信息。
附加信息：对端地址
231. 使用PuTTY，登录连接异常的主机节点。
登录地址：LVS节点或Nginx节点的IP地址，节点IP地址为4中查出的对端地址。
默认账户：elb
帐户默认密码：*****。
232. 执行以下命令，查看nginx进程是否运行正常。
ps -ef|grep nginx|grep -v grep
5登录的是LVS节点，回显如下所示，表示nginx进程运行正常。
5登录的是Nginx节点，回显如下所示，表示nginx进程运行正常。
- 是，请执行9。
- 否，请执行7。
233. 执行以下命令，重新启动nginx进程。
- LVS节点
sh /usr/local/NSP/etc/lua/bin/restart_lvs.sh
- Nginx节点
sh /usr/local/NSP/etc/router/bin/restart_nginx.sh
234. 重启nginx进程不会对其他业务造成影响。
235. 执行以下命令，查看nginx进程是否恢复正常。
ps -ef|grep nginx|grep -v grep
- 是，执行9。
- 否，请联系技术支持工程师协助解决。
236. 观察告警是否清除。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
******** ALM-1223014 僵尸进程告警
##### 告警解释
ELB API每30分钟秒检测后端服务节点业务进程，如果进程显示为无响应状态，生成此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1223014 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 此条告警信息的名称。 |
| 告警源 | 告警来源。 |
| 来源系统 | 对接系统的名称。 |
| 最近发生时间 | 此条告警信息最近一次发生时间。 |
| 定位信息 | 云服务：服务名称。 |
| 定位信息 | 节点类型：告警资源类型。 |
| 附加信息 | 云服务：服务名称。 |
| 附加信息 | 服务：设备节点类型，例如mgmt, lvs, nginx等。 |
| 附加信息 | 本端地址：产生告警信息的ELB API虚拟机的IP地址。 |
| 附加信息 | 对端地址：产生告警信息的ELB虚拟机的IP地址。 |
##### 对系统的影响
影响业务性能或者业务完全无法处理。
##### 可能原因
管理节点进行僵尸进程检查时集群主机（LVS或Nginx）存在僵尸进程。
##### 处理步骤
237. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
238. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
239. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
240. 查询以下告警信息。
附加信息：对端地址
241. 使用PuTTY，登录出现僵尸进程的虚拟机节点，虚拟机节点可以是LVS节点或Nginx节点。
登录地址：LVS节点或Nginx节点的IP地址，节点IP地址为4中询到的对端地址。
默认帐户：elb
默认密码：*****。
242. 5中登录的节点为LVS节点，请执行7~10。
5中登录的节点为Nginx节点，请执行11~14。
243. 执行以下命令，查看keepalived进程和nginx进程是否运行正常。
ps -A -o stat,pid,ppid,cmd | grep -e '^[Zz]' | grep "keepalived" | awk 'END{print NR}'
ps -A -o stat,pid,ppid,cmd | grep -e '^[Zz]' | grep "nginx" | awk 'END{print NR}'
回显返回0，表示相关进程运行正常。
- 是，请执行15。
- 否，请执行8。
244. 执行以下命令，获取僵尸进程的父进程ID。
ps -A -o stat,ppid,cmd | grep keepalived | grep -e '^[Zz]' | awk '{print $2}'
ps -A -o stat,ppid,cmd | grep nginx | grep -e '^[Zz]' | awk '{print $2}'
245. 执行以下命令，清除僵尸进程的父进程。
kill -s 9 <pid>
其中<pid>为8查询出的进程ID。
清除僵尸进程不会对其他进程造成影响。
246. 执行以下命令，查看僵尸进程是否被清除。
ps -A -o stat,pid,ppid,cmd | grep -e '^[Zz]' | grep "keepalived" | awk 'END{print NR}'
ps -A -o stat,pid,ppid,cmd | grep -e '^[Zz]' | grep "nginx" | awk 'END{print NR}'
回显返回0，表示相关僵尸进程已被清除。
- 是，请执行15。
- 否，请联系技术支持工程师协助解决。
247. 执行以下命令，查看nginx进程是否运行正常。
ps -A -o stat,pid,ppid,cmd | grep -e '^[Zz]' | grep "nginx" | awk 'END{print NR}'
回显返回0，表示相关进程运行正常。
- 是，请执行15。
- 否，请执行12。
248. 执行以下命令，获取僵尸进程的父进程ID。
ps -A -o stat,ppid,cmd | grep nginx | grep -e '^[Zz]' | awk '{print $2}'
249. 执行以下命令，清理僵尸进程的父进程。
kill –s 9 <pid>
其中<pid>为12查询出的进程ID。
清除僵尸进程不会对其他业务造成影响。
250. 执行以下命令，查看僵尸进程是否清除。
ps -A -o stat,pid,ppid,cmd | grep -e '^[Zz]' | grep "nginx" | awk 'END{print NR}'
命令回显0，表示僵尸进程已被清除。
- 是，请执行15。
- 否，请联系技术支持工程师协助解决。
251. 观察告警是否清除。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
5.2.10.5 ALM-1223016 ELB管理节点脑裂告警
##### 告警解释
ELB API每10秒查询备节点状态，如果主备节点都处于主状态，上报ELB API脑裂异常，生成此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1223016 | 次要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 此条告警信息的名称。 |
| 告警源 | 告警来源。 |
| 来源系统 | 对接系统的名称。 |
| 最近发生时间 | 此条告警信息最近一次发生时间。 |
| 定位信息 | 云服务：服务名称。 |
| 定位信息 | 节点类型：告警资源类型。 |
| 附加信息 | 云服务：服务名称。 |
| 附加信息 | 服务：设备节点类型，例如mgmt, lvs, nginx等。 |
| 附加信息 | 本端地址：产生告警信息的ELB API虚拟机的IP地址。 |
| 附加信息 | 对端地址：产生告警信息的ELB虚拟机的IP地址。 |
##### 对系统的影响
对现有业务无影响，新下发的ELB业务会出现配置异常。
##### 可能原因
两个ELB管理节点之间keepalived心跳中断，仲裁无法对其感知，导致ELB管理节点出现双主情况。
##### 处理步骤
252. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
253. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
254. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
255. 查询以下告警信息。
附加信息：
- 对端地址
- 本端地址
256. 使用PuTTY，登录ELB管理节点。
登录地址：4中查出的本端地址和对端地址。
默认帐户：elb
默认密码：*****。
257. 执行以下命令，切换到root用户。root帐户默认密码：*****。
sudo su root
258. 分别在这两个节点执行以下命令，查看两个节点是否都为master。
cat /etc/keepalived/status
命令执行后回显如下信息：
[root@localhost elb]# cat /etc/keepalived/status
master
- 若一个节点为master，另一个节点为backup，则管理节点进程正常，请执行9。
- 若两个节点都为master，则进程不正常，继续执行8。
259. 使用PING命令查看两个管理节点之间是否连通。
- 是，请联系技术支持工程师协助解决。
- 否，请解决网络问题后执行9。
260. 观察告警是否清除。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
< 上一节
5.2.11 灾备服务
******** eBackup
********.1 0x1000F40000 License文件无效
##### 告警解释
License文件无效。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1000F40000 | 紧急 | 否 |
##### 对系统的影响
用户不能执行备份、跨AZ/被级联OpenStack恢复、复制、远程复制、HA相关操作。
##### 可能原因
License文件无效。
##### 处理步骤
##### - 可能原因1：License文件无效。
- 请参考《OceanStor BCManager 8.0.1 eBackup License申请指导书》 中的“License功能特性”、“获取ESN”和“申请License”章节申请新的License文件。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > License”界面。
- 单击“导入”，在弹出的对话框中，选择License文件，然后单击“打开”。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.2 0x1000F40001 License未配置
##### 告警解释
在激活License时，检测到License文件不存在。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1000F40001 | 紧急 | 否 |
##### 对系统的影响
用户不能执行备份、跨AZ/被级联OpenStack恢复、复制、远程复制、HA相关操作。
##### 可能原因
用户未导入License文件。
##### 处理步骤
##### - 可能原因1：用户未导入License文件。
- 请参考《OceanStor BCManager 8.0.1 eBackup License申请指导书》中的“License功能特性”、“获取ESN”和“申请License”章节申请新的License文件。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > License”界面。
- 单击“导入”，在弹出的对话框中，选择License文件，然后单击“打开”。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.3 0x1000F40002 License进入宽限期
##### 告警解释
当前所用License已超过有效期（到期时间：[ExpirationTime]），但在60天的宽限期内还可使用。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1000F40002 | 紧急 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| ExpirationTime | 试用期过期时间。 |
##### 对系统的影响
不涉及。
##### 可能原因
License文件已过期。
##### 处理步骤
##### - 可能原因1：License文件已过期。
- 请参考《OceanStor BCManager 8.0.1 eBackup License申请指导书》中的“License功能特性”、“获取ESN”和“申请License”章节申请新的License文件。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > License”界面。
- 单击“导入”，在弹出的对话框中，选择License文件，然后单击“打开”。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.4 0x1000F40003 License已经过期
##### 告警解释
当前所用License已超过宽限的有效期。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1000F40003 | 紧急 | 否 |
##### 对系统的影响
用户不能执行备份、跨AZ/被级联OpenStack恢复、复制、远程复制、HA相关操作。
##### 可能原因
License已超过宽限期。
##### 处理步骤
##### - 可能原因1：License已超过宽限期。
- 请参考《OceanStor BCManager 8.0.1 eBackup License申请指导书》中的“License功能特性”、“获取ESN”和“申请License”章节申请新的License文件。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > License”界面。
- 单击“导入”，在弹出的对话框中，选择License文件，然后单击“打开”。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.5 0x101000F40000 License ESN不匹配
##### 告警解释
保存在License文件中的ESN与备份服务器的ESN都不匹配，License进入宽限期。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x101000F40000 | 紧急 | 否 |
##### 对系统的影响
不涉及。
##### 可能原因
- 没有使用该服务器的ESN申请License。
- 申请License时所使用的ESN对应的网卡故障。
##### 处理步骤
##### - 可能原因1：没有使用该服务器的ESN申请License。
- 请参考《OceanStor BCManager 8.0.1 eBackup License申请指导书》中的“License功能特性”、“获取ESN”和“申请License”章节申请新的License文件。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > License”界面。
- 单击“导入”，在弹出的对话框中，选择License文件，然后单击“打开”。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，执行2。
##### - 可能原因2：申请License时所使用的ESN对应的网卡故障。
- 检查网卡是否能修复。
- 是，修复网卡，确保网卡能正常工作。，处理结束。
- 否，执行2.b。
- 参考1.a获取ESN并申请新的License文件。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > License”界面，单击“导入”，在弹出的对话框中，选择License文件，然后单击“打开”。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.6 0x101000F40001 License文件版本不匹配
##### 告警解释
License文件中的版本与当前系统版本的不匹配。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x101000F40001 | 紧急 | 否 |
##### 对系统的影响
用户不能执行备份、跨AZ/被级联OpenStack、复制、远程复制、HA相关操作。
##### 可能原因
用户使用了非当前版本的License文件。
##### 处理步骤
##### - 可能原因1：用户使用了非当前版本的License文件。
- 请参考《OceanStor BCManager 8.0.1 eBackup License申请指导书》中的“License功能特性”、“获取ESN”和“申请License”章节申请新的License文件。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > License”界面。
- 单击“导入”，在弹出的对话框中，选择License文件，然后单击“打开”。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.7 0x201000F40004 试用即将到期
##### 告警解释
软件的试用时间即将到期（到期时间：[ExpirationTime]）。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000F40004 | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| ExpirationTime | 试用期过期时间。 |
##### 对系统的影响
不涉及。
##### 可能原因
软件的试用时间即将到期。
##### 处理步骤
##### - 可能原因1：软件的试用时间即将到期。
- 请参考《OceanStor BCManager 8.0.1 eBackup License申请指导书》中的“License功能特性”、“获取ESN”和“申请License”章节申请新的License文件。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > License”界面。
- 单击“导入”，在弹出的对话框中，选择License文件，然后单击“打开”。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.8 0x201000F40005 试用期已过
##### 告警解释
软件的试用期已过。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000F40005 | 重要 | 否 |
##### 对系统的影响
用户不能执行备份、跨AZ/被级联OpenStack恢复、复制、远程复制、HA相关操作。
##### 可能原因
软件的试用期已过。
##### 处理步骤
##### - 可能原因1：软件的试用期已过。
- 请参考《OceanStor BCManager 8.0.1 eBackup License申请指导书》中的“License功能特性”、“获取ESN”和“申请License”章节申请新的License文件。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > License”界面。
- 单击“导入”，在弹出的对话框中，选择License文件，然后单击“打开”。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.9 0x201000F40007 License授权容量耗尽
##### 告警解释
License中资源项（[res_name]）的授权容量（[res_capacity]）已经耗尽。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000F40007 | 紧急 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| res_name | 资源项名称。 |
| res_capacity | 资源项的授权容量。 |
##### 对系统的影响
用户无法执行备份操作。
##### 可能原因
License中资源项的授权容量已经耗尽。
##### 处理步骤
##### - 可能原因1：License中资源项的授权容量已经耗尽。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > License”界面。
- 获取资源项描述中授权容量。
- 请参考《OceanStor BCManager 8.0.1 eBackup License申请指导书》中的“License功能特性”、“获取ESN”和“申请License”章节申请新的License文件，并保证新License文件中的授权容量上限大于原有License文件的授权容量。
- 选择“设置 > License”界面，单击“导入”，在弹出的对话框中，选择License文件，然后单击“打开”。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.10 0x201000F40008 License授权容量即将耗尽
##### 告警解释
License中资源项（[res_name]）的授权容量（[res_capacity]）使用达到了告警阈值（[threshold_value]），即将耗尽。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000F40008 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| res_name | 资源项名称。 |
| res_capacity | 资源项的授权容量。 |
| threshold_value | 告警阈值。 |
##### 对系统的影响
不涉及。
##### 可能原因
License授权容量即将耗尽。
##### 处理步骤
##### - 可能原因1：License中资源项的授权容量即将耗尽。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > License”界面。
- 获取资源项描述中授权容量。
- 请参考《OceanStor BCManager 8.0.1 eBackup License申请指导书》中的“License功能特性”、“获取ESN”和“申请License”章节申请新的License文件，并保证新License文件中的授权容量上限大于原有License文件的授权容量。
- 选择“设置 > License”界面，单击“导入”，在弹出的对话框中，选择License文件，然后单击“打开”。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.11 0x201000F4000C 存在License不支持的特性
##### 告警解释
当前不支持VMware类型的虚拟机，备份VMware虚拟机的任务将不会被执行。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000F4000C | 次要 | 否 |
##### 对系统的影响
备份VMware虚拟机的任务将不会被执行。
##### 可能原因
当前License不支持备份VMware类型的虚拟机。
##### 处理步骤
##### - 可能原因1：当前License不支持备份VMware类型的虚拟机。
- 请参考《OceanStor BCManager 8.0.1 eBackup License申请指导书》中的“License功能特性”、“获取ESN”和“申请License”章节申请支持备份VMware类型的虚拟机的License文件。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > License”界面。
- 单击“导入”，在弹出的对话框中，选择License文件，然后单击“打开”。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.12 0x201000F40013 存在License不支持的特性
##### 告警解释
当前不支持HA相关功能，此类型的任务将不会被执行。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000F40013 | 次要 | 否 |
##### 对系统的影响
HA相关任务无法被执行。
##### 可能原因
当前License不支持HA相关功能。
##### 处理步骤
##### - 可能原因1：当前License不支持HA相关功能。
- 请参考《OceanStor BCManager 8.0.1 eBackup License申请指导书》中的“License功能特性”、“获取ESN”和“申请License”章节申请支持HA的License文件。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > License”界面。
- 单击“导入”，在弹出的对话框中，选择License文件，然后单击“打开”。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.13 0x201000F40014 存在License不支持的特性
##### 告警解释
当前License不支持重复数据删除特性，此类型的备份任务将不会被执行。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000F40014 | 次要 | 否 |
##### 对系统的影响
具有重复数据删除特性的备份任务无法被执行。
##### 可能原因
当前License不支持重复数据删除特性。
##### 处理步骤
##### - 可能原因1：当前License不支持重复数据删除特性。
- 请参考《OceanStor BCManager 8.0.1 eBackup License申请指导书》中的“License功能特性”、“获取ESN”和“申请License”章节申请支持重复数据删除特性的License文件。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > License”界面。
- 单击“导入”，在弹出的对话框中，选择License文件，然后单击“打开”。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.14 0x201000F40016 存在License不支持的特性
##### 告警解释
当前不支持两级备份特性，此类型的复制任务将不会被执行。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000F40016 | 次要 | 否 |
##### 对系统的影响
两级备份任务无法被执行。
##### 可能原因
当前License不支持两级备份特性。
##### 处理步骤
##### - 可能原因1：当前License不支持两级备份特性。
- 请参考《OceanStor BCManager 8.0.1 eBackup License申请指导书》中的“License功能特性”、“获取ESN”和“申请License”章节申请支持两级备份特性的License文件。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > License”界面。
- 单击“导入”，在弹出的对话框中，选择License文件，然后单击“打开”。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.15 0x201000F40017 存在License不支持的特性
##### 告警解释
当前不支持跨AZ/被级联OpenStack恢复特性，此类型的恢复任务将不会被执行。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000F40017 | 次要 | 否 |
##### 对系统的影响
跨AZ/被级联OpenStack恢复任务无法被执行。
##### 可能原因
当前License不支持跨AZ/被级联OpenStack恢复特性。
##### 处理步骤
##### - 可能原因1：当前License不支持跨AZ/被级联OpenStack恢复特性。
- 请参考《OceanStor BCManager 8.0.1 eBackup License申请指导书》中的“License功能特性”、“获取ESN”和“申请License”章节申请支持跨AZ/被级联OpenStack恢复特性的License文件。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > License”界面。
- 单击“导入”，在弹出的对话框中，选择License文件，然后单击“打开”。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.16 0x201000F40018 存在License不支持的特性
##### 告警解释
当前License不支持VPP协议加速特性。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000F40018 | 次要 | 否 |
##### 对系统的影响
系统无法使用VPP协议加速功能。
##### 可能原因
当前License不支持VPP协议加速特性。
##### 处理步骤
##### - 可能原因1：当前License不支持VPP协议加速特性。
- 请参考《OceanStor BCManager 8.0.1 eBackup License申请指导书》中的“License功能特性”、“获取ESN”和“申请License”章节申请支持VPP协议加速特性的License文件。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > License”界面。
- 单击“导入”，在弹出的对话框中，选择License文件，然后单击“打开”。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.17 0x1000310000 事件转储目录所用空间已超出阈值
##### 告警解释
事件转储目录（[Dump_dir]）所用空间已超过预设的阈值（[Threshold_size]MB），当目录大小超过最大值（[Max_size]MB）后，程序将自动删除最旧的转储文件。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1000310000 | 紧急 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Threshold_size | 允许转储目录使用空间的阈值。 |
| Max_size | 允许转储目录使用的最大空间。 |
| Dump_dir | 事件转储路径。 |
##### 对系统的影响
系统事件记录可能丢失。
##### 可能原因
事件转储目录下所保存的文件太多。
##### 处理步骤
##### - 可能原因1：事件转储目录下所保存的文件太多。
- 参考登录eBackup服务器登录eBackup-Manager或Server。
- 执行“su root”命令，输入root用户密码，切换至root用户登录。
root帐号的默认密码为*****。
- 执行“cat /opt/huawei-data-protection/ebackup/microservice/ebk_alarm/conf/hcpconf.ini | grep 'EventDumpDirPath'”命令，获取事件转储目录。
- 执行cd EventDumpDirPath命令进入事件转储目录，其中EventDumpDirPath为1.c获取的事件转储目录。
- 请备份事件转储文件。
- 手动删除已经备份的事件转储文件，使“EventDumpDirPath”目录的使用空间小于阈值。
##### 参考信息
无
********.18 0x20100031000A 证书校验失败
##### 告警解释
与告警服务器（IP：[IP_Address]）之间的连接没有可匹配的CA证书。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x20100031000A | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| IP_Address | 告警服务器的IP地址。 |
##### 对系统的影响
与告警服务器之间的连接存在安全风险。
##### 可能原因
- 系统中不存在连接该告警服务器的CA证书。
- 连接该告警服务器的CA证书已过期。
##### 处理步骤
##### - 可能原因1：系统中不存在连接该告警服务器的CA证书。
- 使用浏览器，登录Manager或Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 进入“设置 > 证书”界面，查看告警服务器的CA证书是否存在。
- 是，执行2。
- 否，执行1.c。
- 请联系管理员获取告警服务器的CA证书。
- 单击“导入”，上传告警服务器的证书。
##### - 可能原因2：连接该告警服务器的CA证书已过期。
- 使用浏览器，登录Manager或Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 进入“设置 > 证书”界面，查看告警服务器的CA证书是否过期。
- 是，执行2.c。
- 否，执行3。
- 请联系管理员获取告警服务器的CA证书。
- 单击“导入”，上传告警服务器的证书。
- 检查该告警是否为升级前产生的。如果是，则手动清除该告警；如果不是，则联系技术支持工程师协助解决。
可参考以下步骤检查该告警是否为升级前产生的：
- 使用浏览器，登录Manager或Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 在事件界面查看是否有升级事件0x201000C9001D（备份服务器升级成功）存在。
- 存在，如果该告警的产生时间在升级事件的产生时间之前，则该告警为升级前产生的。
- 不存在，则该告警不是升级前产生的。
##### 参考信息
无
********.19 0x20100031000C 证书校验失败
##### 告警解释
在服务器（IP：[NodeIP]）上的微服务（[MicroService_Name]）与数据库（IP：[IP_Address]）之间的连接没有可匹配的CA证书。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x20100031000C | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| NodeIP | 服务器的IP地址。 |
| MicroService_Name | 微服务的名称。 |
| IP_Address | 数据库的IP地址。 |
##### 对系统的影响
与数据库之间的连接存在安全风险。
##### 可能原因
系统中不存在连接该数据库的CA证书或CA证书已过期。
##### 处理步骤
##### - 可能原因1：系统中不存在连接该数据库的CA证书或CA证书已过期。
- 请联管理员获取未过期的数据库证书文件，将其重命名为“cacert.pem”。
- 通过WinSCP工具将“cacert.pem”文件拷贝到上报告该警的服务器的“/home/<USER>
默认帐户：hcp，默认密码：*****
- 使用PuTTY，通过告警上报的IP地址登录eBackup服务器。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户登录。
root帐号的默认密码为*****。
- 执行mv /home/<USER>/cacert.pem /opt/huawei-data-protection/ebackup/microservice/ebk_xxx/conf命令，将证书文件移动到对应微服务的配置目录中。其中ebk_xxx为微服务名称。
- 执行cd /opt/huawei-data-protection/ebackup/microservice/ebk_xxx/conf命令，进入证书保存目录。
- 执行chmod 600 cacert.pem命令，将“cacert.pem”文件的权限设置为600。
- 执行chown hcpprocess:hcpmgr cacert.pem命令，将“cacert.pem”文件的所有者修改为hcpprocess。
- 执行service hcp restart命令，重启服务。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.20 0x201000310010 访问告警服务器失败
##### 告警解释
访问告警服务器（IP：[Server_ip]）失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000310010 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Server_ip | 告警服务器管理IP地址。 |
##### 对系统的影响
不能正常上报告警。
##### 可能原因
与告警服务器之间的连接中断。
##### 处理步骤
##### - 可能原因1：与告警服务器连接中断。
- 参考登录eBackup服务器登录eBackup-Manager或Server。
- 执行“su root”命令，输入root用户密码，切换至root用户登录。
root帐号的默认密码为*****。
- 如果是IPv4，执行“ping 告警服务器的管理IP”命令，如果是IPv6，执行“ping6 告警服务器的管理IP”命令，检查网络连通性是否正常。
- 是，执行1.d。
- 否，请联系技术支持工程师协助解决。
- 使用浏览器，登录Manager或Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 进入“设置 > 配置 > 告警上报”界面。
请确认“服务类型”是否为“OC”，“URL”是否为ManageOne告警服务器的域名和端口号。
- 是，执行2。
- 否，重新配置告警上报参数。
- 检查该告警是否为升级前产生的。如果是，则手动清除该告警；如果不是，则联系技术支持工程师协助解决。
可参考以下步骤检查该告警是否为升级前产生的：
- 使用浏览器，登录Manager或Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 在事件界面查看是否有升级事件0x201000C9001D（备份服务器升级成功）存在。
- 存在，如果该告警的产生时间在升级事件的产生时间之前，则该告警为升级前产生的。
- 不存在，则该告警不是升级前产生的。
##### 参考信息
无
********.21 0x201000310015 数据库连接失败
##### 告警解释
在服务器（IP：[NodeIP]）上的微服务（[MicroService_Name]）连接数据库（IP：[IP_Address]）失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000310015 | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| NodeIP | 服务器的IP地址。 |
| MicroService_Name | 微服务的名称。 |
| IP_Address | 数据库的IP地址。 |
##### 对系统的影响
该微服务无法正常运行，可能导致系统整体性能下降。
##### 可能原因
- 数据库服务未启动。
- 网络异常。
##### 处理步骤
##### - 可能原因1：数据库服务未启动。
- 参考登录eBackup服务器登录eBackup-Manager或Server。
- 执行“su root”命令，输入root用户密码，切换至root用户登录。
root帐号的默认密码为*****。
- 执行“ps -ef | grep gaussdb”命令，查看数据库进程是否存在。
- 是，执行2。
- 否，执行1.d。
- 执行“sh /opt/huawei-data-protection/ebackup/bin/gaussdb_sandbox.sh restart”命令，重启数据库服务。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，执行2。
##### - 可能原因2：网络异常。
- 使用PuTTY，以hcp用户通过告警上报的服务器的IP地址登录eBackup服务器。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户登录。
root帐号的默认密码为*****。
- 执行“cat /opt/huawei-data-protection/ebackup/microservice/ebk_xxx/conf/hcpconf.ini | grep Loadbalance”，获取浮动IP地址。其中“ebk_xxx”为微服务名称。
- 如果是IPv4，执行“ping 浮动IP地址”命令，如果是IPv6，执行“ping6 浮动IP地址”，检查网络通信是否正常。
- 是，请联系技术支持工程师协助解决。
- 否，执行2.e。
- 排查网络相关问题后，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.22 0x210000000101 微服务注册失败
##### 告警解释
微服务（名称：[Name]，IP：[IP_Address]，端口：[Port]）注册失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x210000000101 | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Name | 微服务的名称。 |
| IP_Address | 微服务的IP地址。 |
| Port | 微服务的端口。 |
##### 对系统的影响
该微服务无法正常接收和处理请求，可能导致系统整体性能下降。
##### 可能原因
微服务注册失败。
##### 处理步骤
##### - 可能原因1：微服务注册失败。
- 使用PuTTY，通过告警上报的IP地址登录微服务注册失败所在服务器。
默认帐户：hcp，默认密码：*****
- 执行su root命令，输入root用户密码，切换至root用户登录。
root帐号的默认密码为*****。
- 执行ps -ef | grep 微服务名称命令，检查微服务进程是否存在。
- 是，执行1.f。
- 否，执行1.d。
- 执行netstat -lp | grep 微服务端口号命令，检查该微服务相关端口是否处于“LISTEN”状态。
- 是，执行1.e。
- 否，执行1.f。
- 找到“LISTEN”状态后面的进程ID，执行kill -9 进程ID命令，释放该端口。
- 执行cd /opt/huawei-data-protection/ebackup/microservice/ebk_xxx/script命令，进入微服务脚本目录，其中，“ebk_xxx”是微服务名称。
- 执行source ebackup_env.sh命令，导入环境变量。
- 执行stop命令，停止该微服务。等待1分钟左右，微服务会自动启动。
- 是，执行1.i。
- 否，请联系技术支持工程师协助解决。
- 等待3分钟左右，在当前告警界面查看该告警是否恢复。
- 是，处理结束。
- 否，执行1.j。
- 执行service hcp status命令，检查ebk_governance和ebk_iam微服务是否在运行。
- 是，执行1.k。
- 否，参考1.f~1.h重新启动未运行的ebk_governance或ebk_iam，之后转到1.i。
- 执行ps -ef | grep ebk_lb命令，检查ebk_lb的nginx进程是否正常运行。
- 是，执行1.l。
- 否，参考1.f~1.h重新启动ebk_lb。之后转到1.i。
- 执行cat /opt/huawei-data-protection/ebackup/microservice/ebk_xxx/conf/hcpconf.ini | grep Loadbalance命令，获取浮动IP地址。
- 如果是IPv4，执行ping 浮动IP地址命令，如果是IPv6，执行ping6 浮动IP地址，检查微服务所在服务器的网络通信是否正常。
- 是，请联系技术支持工程师协助解决。
- 否，修复网络。之后转到1.i。
##### 参考信息
无
********.23 0x210000000100 微服务已停止
##### 告警解释
微服务（名称：[Name]，IP：[IP_Address]，端口：[Port]）已停止。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x210000000100 | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Name | 微服务的名称。 |
| IP_Address | 微服务的IP地址。 |
| Port | 微服务的端口。 |
##### 对系统的影响
该微服务无法正常运行，可能导致系统整体性能下降。
##### 可能原因
微服务已停止运行。
##### 处理步骤
##### - 可能原因1：微服务已停止运行。
- 使用PuTTY，通过告警上报的IP地址登录已停止运行的微服务所在服务器。
默认帐户：hcp，默认密码：*****
- 执行su root命令，输入root用户密码，切换至root用户登录。
root帐号的默认密码为*****。
- 执行ps -ef | grep 微服务名称命令，检查微服务进程是否存在。
- 是，执行1.f。
- 否，执行1.d。
- 执行netstat -lp | grep 微服务端口号命令，检查该微服务相关端口是处于“LISTEN”状态。
- 是，执行1.e。
- 否，执行1.f。
- 找到“LISTEN”状态后面的进程ID，执行kill -9 进程ID命令，释放该端口。
- 执行cd /opt/huawei-data-protection/ebackup/microservice/ebk_xxx/script命令，进入微服务脚本目录其中，“ebk_xxx”是微服务名称。
- 执行source ebackup_env.sh命令，导入环境变量。
- 执行stop命令，停止该微服务。等待1分钟左右，微服务会自动启动。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.24 0x6000840001 证书已经过期
##### 告警解释
证书（区域：[Region]，部件：[ServiceName]，证书名：[CertName]）已经过期。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x6000840001 | 紧急 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Region | 来源部件所在区域。 |
| ServiceName | 证书来源部件。 |
| CertName | 证书的名称。 |
##### 对系统的影响
外部客户端部件连接eBackup可能失败。
##### 可能原因
证书已经过期。
##### 处理步骤
##### - 可能原因1：证书已经过期。
- 如果告警中的证书名是“eBackup-Portal”，请参考更换灾备服务eBackup证书（eBackup-Portal）章节替换证书。
- 如果告警中的证书名是“eBackup-Cert”或“eBackup-IAMCert”，请参考通过ManageOne界面方式单个或批量更换证书章节替换证书。
- 等待3分钟左右，在当前告警界面查看该告警是否恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.25 0x6000840002 证书即将过期
##### 告警解释
证书（区域：[Region]，部件：[ServiceName]，证书名：[CertName]）即将在[Date]过期。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x6000840002 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Region | 来源部件所在区域。 |
| ServiceName | 证书来源部件。 |
| CertName | 证书的名称。 |
| Date | 证书过期日期。 |
##### 对系统的影响
无。
##### 可能原因
证书即将过期。
##### 处理步骤
##### - 可能原因1：证书即将过期。
- 如果告警中的证书名是“eBackup-Portal”，请参考中的“证书管理 > 更换A类证书 > 更换灾备服务eBackup证书（eBackup-Portal）”更换灾备服务eBackup证书（eBackup-Portal） 章节替换证书。
- 如果告警中的证书名是“eBackup-Cert”或“eBackup-IAMCert”，请参考通过ManageOne界面方式单个或批量更换证书章节替换证书。
- 等待3分钟左右，在当前告警界面查看该告警是否恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.26 0x210000000200 证书校验失败
##### 告警解释
与LDAP服务器（IP：[IP_Address]）之间的连接没有可匹配的CA证书。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x210000000200 | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| IP_Address | LDAP服务器的IP地址。 |
##### 对系统的影响
与LDAP服务器之间的连接存在安全风险。
##### 可能原因
系统中不存在连接该LDAP服务器的CA证书或CA证书已过期。
##### 处理步骤
##### - 可能原因1：系统中不存在连接该LDAP服务器的CA证书或CA证书已过期。
- 请联系管理员获取未过期的LDAP服务器的CA证书文件。
- 将获取到的LDAP服务器的CA证书文件重命名为“LDAP_CACert.crt”。
- 通过WinSCP工具将“LDAP_CACert.crt”文件拷贝到Manager或Server的“/home/<USER>
登录方式请参考登录eBackup服务器。
默认帐户：hcp，默认密码：*****
- 参考登录eBackup服务器登录eBackup-Manager或Server。
- 执行su root命令，输入root用户密码，切换至root用户登录。
root帐号的默认密码为*****。
- 执行mv /home/<USER>/LDAP_CACert.crt /opt/huawei-data-protection/ebackup/microservice/ebk_iam/conf命令，将证书文件移动到IAM微服务的配置目录中。
- 执行cd /opt/huawei-data-protection/ebackup/microservice/ebk_iam/conf命令，进入证书保存目录。
- 执行chmod 600 LDAP_CACert.crt命令，将“LDAP_CACert.crt”文件的权限设置为600。
- 执行chown hcpprocess:hcpmgr LDAP_CACert.crt命令，将“LDAP_CACert.crt”文件的所有者修改为hcpprocess:hcpmgr。
- 执行cd /opt/huawei-data-protection/ebackup/microservice/ebk_iam/script命令，进入微服务脚本目录。
- 执行source ebackup_env.sh命令，导入环境变量。
- 执行stop命令，停止IAM微服务。等待1分钟左右，IAM微服务会自动启动。
- 以LDAP类型的帐户登录Manager或Server的GUI，以此触发证书校验。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.27 0x105800860001 清理备份记录失败
##### 告警解释
微服务（[MicroService_Name]）清理备份记录（备份ID：[backupId]）失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x105800860001 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| MicroService_Name | 微服务名 |
| backupId | 备份ID |
##### 对系统的影响
不涉及。
##### 可能原因
网络中断。
##### 处理步骤
##### - 可能原因1：网络中断。
- 参考登录eBackup服务器登录Manager。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 如果是IPv4，执行ping OpenStack控制节点IP或域名命令，如果是IPv6，执行ping6 OpenStack控制节点IP或域名命令，测试Manager与OpenStack控制节点的网络是否连通。
- 是，执行1.d。
- 否，执行1.h。
- 使用PuTTY，登录任意OpenStack控制节点。
默认帐号：fsp，默认密码：*****
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行source set_env命令导入环境变量。
- 执行cinder backup-delete 备份id命令删除快照，检查执行结果是否成功。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
- 请联系机房管理员修复网络连通性，确保网络正常连接后查看告警是否恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.28 0x21000000090E 清理leftover删除快照失败
##### 告警解释
微服务（[MicroService_Name]）删除卷快照（快照ID：[snapshotId]）失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x21000000090E | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| MicroService_Name | 微服务名 |
| snapshotId | 快照ID |
##### 对系统的影响
不涉及。
##### 可能原因
网络中断。
##### 处理步骤
##### - 可能原因1：网络中断。
- 参考登录eBackup服务器登录Manager。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 如果是IPv4，执行“ping OpenStack控制节点IP或域名”命令，如果是IPv6，执行“ping6 OpenStack控制节点IP或域名”命令，测试Manager与OpenStack控制节点网络是否连通。
- 是，1.d。
- 否，执行1.h。
- 使用PuTTY，登录任意OpenStack控制节点。
默认帐号：fsp，默认密码：*****
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行source set_env命令导入环境变量。
- 执行cinder snapshot-delete 快照id命令删除快照，检查执行结果是否成功。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
- 请联系机房管理员修复网络连通性，确保网络正常连接后查看告警是否恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.29 0x21000000090F 组件连接异常
##### 告警解释
备份节点（[Node_Ip]）与组件（[Module_Type]）地址（[IP_Address]）连接异常。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x21000000090F | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Node_Ip | eBackup节点IP |
| Module_Type | 对接组件类型 |
| IP_Address | 组件地址 |
##### 对系统的影响
不能进行正常业务操作。
##### 可能原因
组件间网络问题。
##### 处理步骤
##### - 可能原因1：网络中断。
- 使用PuTTY，以hcp帐号登录告警所述备份节点。
默认帐户：hcp，默认密码：*****
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 如果是IPv4，执行“ping 组件地址”命令，如果是IPv6，执行“ping6 组件地址”命令，测试告警所述备份节点与组件的网络是否连通。
- 是，执行2。
- 否，执行1.d。
- 执行echo "nameserver DNS server ip" >> /etc/resolv.conf命令，配置DNS服务器地址。DNS配置完成后，查看告警是否恢复。
- 是，处理结束。
- 否，执行2。
- 检查该告警是否为升级前产生的。如果是，则手动清除该告警；如果不是，则联系技术支持工程师协助解决。
可参考以下步骤检查该告警是否为升级前产生的：
- 使用浏览器，登录Manager的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 在事件界面查看是否有升级事件0x201000C9001D（备份服务器升级成功）存在。
- 存在，如果该告警的产生时间在升级事件的产生时间之前，则该告警为升级前产生的。
- 不存在，则该告警不是升级前产生的。
##### 参考信息
无
********.30 0x210000000901 监控进程启动失败
##### 告警解释
在备份节点（[Node_Ip]）上微服务（[MicroService_Name]）的监控进程启动失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x210000000901 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Node_Ip | eBackup备份节点IP |
| MicroService_Name | eBackup微服务名称 |
##### 对系统的影响
不涉及。
##### 可能原因
初始化配置失败。
##### 处理步骤
##### - 可能原因1：初始化配置失败。
- 使用PuTTY，登录告警上报的备份节点。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，切换到root帐户。
- 执行“cd /opt/huawei-data-protection/ebackup/microservice/ebk_XXX/conf”命令，进入微服务配置目录。“ebk_XXX”为微服务名称。
- 执行“ls -l srv_checked_processes.xml”命令，查看配置文件权限是否为600，所有者是否为hcpprocess:hcpmgr。
- 是，执行1.e。
- 否，执行“chmod 600 srv_checked_processes.xml”命令，设置文件权限为600；执行“chown hcpprocess:hcpmgr srv_checked_processes.xml”命令，设置文件所有者为hcpprocess:hcpmgr，处理结束。
- 执行“cat srv_checked_processes.xml”命令，查看配置项autoRestart的值是否为“yes”或“no”、配置项failedThreshold和failedPeriod的值是否在“0-1440”范围内、配置项onlyRunOnLeaderNode的值是否为“yes”或“no”。
- 是，执行1.f。
- 否，执行“vim srv_checked_processes.xml”命令，修改1.e中配置项的值为合法值，处理结束。
- 执行“cd /opt/huawei-data-protection/ebackup/microservice/ebk_XXX/script”命令，进入微服务脚本目录。“ebk_XXX”为微服务名称。
- 执行“ls -l srv_processes_monitor.sh”命令，查看脚本文件权限是否为550，所有者是否为root:hcpmgr。
- 是，请联系技术支持工程师协助解决。
- 否，执行“chmod 550 srv_processes_monitor.sh”命令，设置文件权限为550；执行“chown root:hcpmgr srv_processes_monitor.sh”命令，设置文件所有者为root:hcpmgr。处理结束。
##### 参考信息
无
********.31 0x2010E01D0005 证书校验失败
##### 告警解释
OpenStack（组件：[Module]，IP：[IP_Address]）没有可匹配的证书。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x2010E01D0005 | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Module | OpenStack组件名 |
| IP_Address | OpenStack组件的IP |
##### 对系统的影响
与Openstack组件连接存在安全风险。
##### 可能原因
- 系统中不存在该OpenStack组件的证书。
- 该OpenStack组件的证书已过期。
##### 处理步骤
##### - 可能原因1：系统中不存在该OpenStack组件的证书。
- 使用浏览器，登录Manager的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 进入“设置 > 证书”界面，查看OpenStack类型的证书是否存在。
- 是，2。
- 否，执行1.c。
- 请参考导入eBackup周边组件证书章节，获取Openstack组件证书并导入。
##### - 可能原因2：该OpenStack组件的证书已过期。
- 使用浏览器，登录Manager的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 进入“设置 > 证书”界面，查看OpenStack类型的证书是否过期。
- 是，执行2.c。
- 否，执行3。
- 请参考导入eBackup周边组件证书章节，获取Openstack组件证书并导入。
- 检查该告警是否为升级前产生的。如果是，则手动清除该告警；如果不是，则联系技术支持工程师协助解决。
可参考以下步骤检查该告警是否为升级前产生的：
- 使用浏览器，登录Manager的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 在事件界面查看是否有升级事件0x201000C9001D（备份服务器升级成功）存在。
- 存在，如果该告警的产生时间在升级事件的产生时间之前，则该告警为升级前产生的。
- 不存在，则该告警不是升级前产生的。
##### 参考信息
无
********.32 0x1010E01A0018 执行备份时CBT机制未生效
##### 告警解释
对受保护对象（[Machine_name]）中磁盘（ID：[Disk_id]）执行了磁盘预置大小的完全备份而非基于CBT机制的备份。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1010E01A0018 | 次要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Machine_name | 受保护对象名称。 |
| Disk_id | 磁盘ID。 |
##### 对系统的影响
- 目标磁盘如果为精简盘则可能会多占用备份存储空间。
- 如果用该备份副本进行恢复则会占用该磁盘预置大小的生产存储空间。
##### 可能原因
生产端主机掉电重启后或者受保护对象进行过恢复操作后生产端CBT机制未生效。
##### 处理步骤
##### - 可能原因1：生产端主机掉电重启后或者受保护对象进行过恢复操作后生产端CBT机制未生效。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 查看任务详情中是否包含获取CBT失败的信息。
- 是，执行1.c。
- 否，请联系技术支持工程师协助解决。
- 单击“受保护环境 > VMware”，根据受保护对象的名称找到受保护环境vCenter的IP。
- 登录vCenter Client，根据受保护对象的名称（Machine_name）找到目标受保护对象。
- 单击“受保护对象”，然后选择“快照 > 快照管理器”。
- 删除受保护对象存在的所有快照。
- 手动对该受保护对象执行全量备份并查看任务详情观察问题是否解决。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
********.33 0x101000C90003 系统配置数据所占空间已超出最大阈值
##### 告警解释
在Manager或Server（IP：[Server_ip]）上，系统配置数据所占空间已超出最大阈值（95%）。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x101000C90003 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Server_ip | eBackup服务器IP地址。 |
##### 对系统的影响
不涉及。
##### 可能原因
在Manager或Server上，系统配置数据所占空间已超出最大阈值（95%）。
##### 处理步骤
##### - 可能原因1：Manager或Server上，系统配置数据所占空间已超出最大阈值（95%）。
- 参考登录eBackup服务器登录eBackup-Manager或Server。
- 执行“su root”命令，输入root用户密码，切换至“root”用户登录。
root帐号的默认密码为*****。
- 执行“df -h” 命令查看/opt分区空间使用率是否超过95%。
- 是，执行1.d。
- 否，请联系技术支持工程师协助解决。
- 执行“cd /opt”命令进入“/opt”目录。
- 保留“/opt”目录下的huawei-data-protection，logstash，omm，root，dsware，lost+found，software，export_alarms，moopsagent，UltraPath，workspace目录，删除用户确认无用的文件或者目录，确保“/opt”分区空间使用率不超过90%。
##### 参考信息
无
********.34 0x101000C90004 系统配置数据所占空间已超出阈值
##### 告警解释
在Manager或Server（IP：[Server_id]）上，系统配置数据所占空间已超出阈值（80%）。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x101000C90004 | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Server_id | eBackup服务器IP地址。 |
##### 对系统的影响
不涉及。
##### 可能原因
在Manager或Server上，系统配置数据所占空间已超出阈值（80%）。
##### 处理步骤
##### - 可能原因1：在Manager或Server上，系统配置数据所占空间已超出阈值（80%）。
- 参考登录eBackup服务器登录eBackup-Manager或Server。
- 执行“su root”命令，输入root用户密码，切换至“root”用户登录。
root帐号的默认密码为*****。
- 执行“df -h” 命令查看/opt分区空间使用率是否超过80%。
- 是，执行1.d。
- 否，请联系技术支持工程师协助解决。
- 执行“cd /opt”命令进入“/opt”目录。
- 保留“/opt”目录下的huawei-data-protection，logstash，omm，root，dsware，lost+found，software，export_alarms目录，删除用户确认无用的文件或者目录，确保“/opt”分区空间使用率不超过75%。
##### 参考信息
无
********.35 0x1000C90002 访问系统数据库备份共享存储失败
##### 告警解释
访问系统数据库备份共享存储（路径：[DBBackup_path]）失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1000C90002 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| DBBackup_path | 系统数据库备份共享存储路径。 |
##### 对系统的影响
系统数据库文件不能再进行备份。
##### 可能原因
- Manager或Server与存储设备之间的网络连接中断。
- 管理数据备份存储不可访问。
- S3存储的桶不存在，AK/SK错误。
- Manager或Server没有权限访问共享存储。
##### 处理步骤
##### - 可能原因1：Manager或Server与存储设备之间的网络连接中断。
- 使用浏览器，依次登录Manager和Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > 配置 > 管理数据备份”，进入“管理数据备份”界面。
- 单击“设置备份存储”，查看管理数据备份存储服务器IP地址。
- 参考登录eBackup服务器登录eBackup-Manager和Server。
- 执行su root命令，输入root用户密码，切换至root用户登录。
root帐号的默认密码为*****。
- 如果是IPv4，执行ping 管理数据备份存储服务器IP命令，如果是IPv6，执行ping6 管理数据备份存储服务器IP，查看Manager和Server与管理数据备份存储服务器之间网络是否联通。
- 是，执行2。
- 否，请联系机房管理员修复网络连通性，确保网络正常连接。
##### - 可能原因2：管理数据备份存储不可访问。
- 根据1的执行结果，查看是否存成在丢包的现象。
- 是，联系管理员提高网络性能质量，保证备Manager和Server与存储设备间通信稳定。
- 否，执行2.b。
- 使用浏览器，依次登录Manager和Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > 配置 > 管理数据备份”，进入“管理数据备份”界面。
- 单击“设置备份存储”，查看管理数据备份存储类型。
- S3，执行3。
- NFS，执行4。
##### - 可能原因3：S3存储的桶不存在，AK/SK错误。
- 参考登录eBackup服务器登录eBackup-Manager和Server。
- 执行su root命令，输入root用户密码，切换至root用户登录。
root帐号的默认密码为*****。
- 执行cd /opt/huawei-data-protection/ebackup/sbin命令，进入“sbin”目录；
- 执行export LD_LIBRARY_PATH=/opt/huawei-data-protection/ebackup/libs命令，导入LD_LIBRARY_PATH环境变量。
- 执行./uds_plug-in TestBucket S3存储IP地址 桶名 AK SK命令，查看AK、SK以及桶名是否正确。
- 是，执行4。
- 否，执行3.f。
- 使用浏览器，依次登录Manager和Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > 配置 > 管理数据备份”，进入“管理数据备份”界面。
- 单击“设置备份存储”，重新配置管理数据的备份存储，详细请参考S3章节。
##### - 可能原因4：Manager或Server没有权限访问共享存储。
- 参考登录eBackup服务器登录eBackup-Manager和Server。
- 执行su root命令，输入root用户密码，切换至root用户登录。
root帐号的默认密码为*****。
- 执行chown hcpprocess:hcpmgr /opt/huawei-data-protection/ebackup/db_bak命令和ls /opt/huawei-data-protection/ebackup/db_bak命令，检查该目录能否更改目录权限和正常访问。
- 是，联系技术支持工程师处理协助解决。
- 否，请联系存储设备管理员，开通Manager或Server对存储设备的访问权限。
当为NFS存储时，请确保存储侧不限制eBackup服务器root用户的权限，使该用户对NFS共享目录具有完全控制权限。
当为S3存储时，请确保eBackup对S3存储的桶有完全控制权限。
##### 参考信息
无
********.36 0x1000C90035 证书校验失败
##### 告警解释
管理数据的备份存储没有可匹配的FTP证书。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1000C90035 | 次要 | 否 |
##### 对系统的影响
与FTP服务器之间的连接存在安全风险。
##### 可能原因
- 系统中不存在管理数据备份存储的FTP证书。
- 管理数据备份存储的FTP证书已过期。
##### 处理步骤
##### - 可能原因1：系统中不存在管理数据备份存储的FTP证书。
- 使用浏览器，依次登录Manager和Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > 证书”，进入“证书”界面，查看管理数据备份存储（类型：FTP存储）的证书是否存在。
- 是，执行2。
- 否，执行1.c。
- 请联系管理员获取FTP证书。
- 单击“导入”，上传管理数据备份存储的FTP证书。
##### - 可能原因2：管理数据备份存储的FTP证书已过期。
- 使用浏览器，依次登录Manager和Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > 证书”，进入“证书”界面，查看管理数据备份存储（类型：FTP存储）的证书是否过期。
- 是，执行2.c。
- 否，请联系技术支持工程师协助解决。
- 请联系管理员获取FTP证书。
- 单击“导入”，上传管理数据备份存储的有效证书。
##### 参考信息
无
********.37 0x1000C90003 系统数据库备份共享存储空间不足
##### 告警解释
系统数据库备份共享存储（路径：[DBBackup_path]）空间不足。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1000C90003 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| DBBackup_path | 系统数据库备份共享存储路径。 |
##### 对系统的影响
系统数据库文件有可能不能再进行备份。
##### 可能原因
存储设备容量不足。
##### 处理步骤
##### - 可能原因1： 存储设备容量不足。
- 参考登录eBackup服务器登录eBackup-Manager和Server。
- 执行“su root”命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 执行“ll /opt/huawei-data-protection/ebackup/db_bak/标示符目录”命令，检查系统存储路径下是否存在无用的数据。
- 是，执行1.d。
- 否，请联系管理员对共享存储执行扩容或数据迁移。
- 执行“rm -rf 无用的文件或目录”命令，删除无用的数据。
##### 参考信息
无
********.38 0x1000C90005 未配置备份服务器
##### 告警解释
当前系统没有配置管理数据的备份存储。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1000C90005 | 重要 | 否 |
##### 对系统的影响
系统的管理数据不能进行备份，且管理数据的恢复功能不可用。
##### 可能原因
系统中没有配置管理数据的备份存储。
##### 处理步骤
##### - 可能原因1：没有配置管理数据的备份存储。
- 使用浏览器，依次登录Manager和Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > 配置 > 管理数据备份”，进入“管理数据备份”界面。
- 单击“设置备份存储”，配置管理数据的备份存储，详细请参考配置备份策略章节。
##### 参考信息
无
********.39 0x1000C90006 证书校验失败
##### 告警解释
管理数据的备份存储没有可匹配的S3证书。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1000C90006 | 次要 | 否 |
##### 对系统的影响
与S3存储服务器之间的连接存在安全风险。
##### 可能原因
- 系统中不存在管理数据备份存储的S3证书。
- 管理数据备份存储的S3证书已过期。
##### 处理步骤
##### - 可能原因1：系统中不存在管理数据备份存储的S3证书。
- 使用浏览器，依次登录Manager和Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > 证书”，进入“证书”界面，查看管理数据备份存储（类型：S3存储）的证书是否存在。
- 是，执行2。
- 否，执行1.c。
- 请联系管理员获取S3存储的证书。
- 单击“导入”，上传管理数据备份存储的S3证书。
##### - 可能原因2：管理数据备份存储的S3证书已过期。
- 使用浏览器，依次登录Manager和Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > 证书”，进入“证书”界面，查看管理数据备份存储（类型：S3存储）的证书是否过期。
- 是，执行2.c。
- 否，请联系技术支持工程师协助解决。
- 请联系管理员获取S3存储的证书。
- 单击“导入”，上传管理数据备份存储的有效证书。
##### 参考信息
无
********.40 0x1000C90032 FTP服务器空间不足
##### 告警解释
当前管理数据备份设置的FTP服务器（路径：[Ftp_path]）空间不足。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1000C90032 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Ftp_path | FTP服务器路径。 |
##### 对系统的影响
管理数据备份到FTP服务器失败。
##### 可能原因
FTP服务器空间不足。
##### 处理步骤
##### - 可能原因1： FTP服务器空间不足。
- 使用FTP客户端工具，通过用户和密码登录FTP服务器。
- 检查FTP服务器共享目录下是否存在无用的数据。
- 是，执行1.c。
- 否，请联系管理员对FTP服务器共享存储进行扩容。
- 执行“rm -rf 无用的文件或目录”命令，删除无用的数据。
##### 参考信息
无
********.41 0x1000C90033 登录FTP服务器被拒绝
##### 告警解释
用户（[User]）登录FTP服务器（路径：[Ftp_path]）被拒绝。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1000C90033 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| User | FTP用户。 |
| Ftp_path | FTP服务器路径。 |
##### 对系统的影响
管理数据备份到FTP服务器失败。
##### 可能原因
- 登录FTP服务器的用户名或密码不正确。
- FTP服务器要求客户端必须采用FTPS协议进行通信。
##### 处理步骤
##### - 可能原因1：登录FTP服务器的用户名或密码不正确。
- 联系管理员获取正确的FTP服务器的用户名和密码。
- 使用浏览器，依次登录Manager和Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > 配置 > 管理数据备份”，进入“管理数据备份”界面。
- 单击“设置备份存储”，进入“设置备份存储”界面，检查配置的FTP服务器用户和密码是否正确。
- 是，执行2。
- 否，执行1.e。
- 重新配置FTP备份存储。详细请参考FTP章节。
##### - 可能原因2：FTP服务器要求客户端必须采用FTPS协议进行通信。
- 选择“设置 > 配置 > 管理数据备份”，进入“管理数据备份”界面。
- 单击“设置备份存储”，进入“设置备份存储”界面，检查配置的协议类型是否为FTPS。
- 是，请联系技术支持工程师协助解决。
- 否，执行2.c。
- 修改FTP协议配置为FTPS，重新配置可用的FTP备份存储，详细请参考FTP章节。
##### 参考信息
无
********.42 0x1000C90034 上传管理数据到FTP服务器失败
##### 告警解释
用户（[User]）上传管理数据到FTP服务器（路径：[Ftp_path]）失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1000C90034 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| User | FTP用户。 |
| Ftp_path | FTP服务器路径。 |
##### 对系统的影响
管理数据备份到FTP服务器失败。
##### 可能原因
用户没有权限上传文件到FTP服务器。
##### 处理步骤
##### - 可能原因1：用户没有权限上传文件到FTP服务器。
- 使用FTP客户端工具，通过用户和密码登录FTP服务器，检查能否上传文件。
- 是，请联系技术支持工程师协助解决。
- 否，执行1.b。
- 联系管理员获取正确的FTP服务器的用户名和密码，并为Manager和Server开通允许上传文件的权限。
- 使用浏览器，依次登录Manager和Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > 配置 > 管理数据备份”，进入“管理数据备份”页面。
- 重新配置FTP备份存储。详细请参考FTP章节。
##### 参考信息
无
********.43 0x1000C90004 当前挂载的系统数据库备份共享存储类型与预置类型不匹配
##### 告警解释
当前挂载的系统数据库备份共享存储类型与预置类型不匹配。（挂载路径:[DBBackup_path]）。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1000C90004 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| DBBackup_path | 系统数据库备份共享存储路径。 |
##### 对系统的影响
系统数据库文件有可能不能再进行备份。
##### 可能原因
当前挂载的系统数据库备份共享存储类型与预置类型不匹配。
##### 处理步骤
##### - 可能原因1：当前挂载的系统数据库备份共享存储类型与预置类型不匹配。
- 参考登录eBackup服务器登录eBackup-Manager和Server。
- 执行“su root”命令，输入root用户密码，切换至“root”用户登录。
root帐号的默认密码为*****。
- 执行“cat /opt/huawei-data-protection/ebackup/conf/hcpconf.ini | grep "DBBakRepositoryType="”命令，获取数据库备份共享存储预置类型。
- 执行“mount”命令，查看系统数据库备份共享存储类型是否与1.c的结果一致。
- 是，请联系技术支持工程师协助解决。
- 否，执行1.e。
- 执行“umount /opt/huawei-data-protection/ebackup/db_bak”命令手动卸载系统数据库的共享存储。
- 使用浏览器，依次登录Manager和Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > 配置 > 管理数据备份”，进入“管理数据备份”界面。
- 单击“设置备份存储”，配置管理数据的备份存储，详细请参考配置备份策略章节。
##### 参考信息
无
********.44 0x2010E00E0007 eBackup没有存储单元的写权限
##### 告警解释
eBackup没有存储单元（名称：[Brick_name]，路径：[Brick_path]）的写权限。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x2010E00E0007 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Brick_name | 存储单元名称。 |
| Brick_path | 存储单元的路径。 |
##### 对系统的影响
备份、复制任务将执行失败。
##### 可能原因
存储单元没有可写权限。
##### 处理步骤
##### - 可能原因1：存储单元没有可写权限。
请参考《华为云Stack 6.5.1 硬件checklist》“备份服务”页签中备份存储所在行的“创建NFS共享”、“创建CIFS共享”或“添加桶”的操作设置存储单元的写权限。根据存储单元使用的是NFS存储、CIFS或S3存储确认选择对应的操作。
##### 参考信息
无
********.45 0x2010E00E000A 证书校验失败
##### 告警解释
存储单元（路径：[Path]）没有可匹配的证书。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x2010E00E000A | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Path | 存储单元的路径。 |
##### 对系统的影响
与S3存储服务器之间的连接存在安全风险。
##### 可能原因
- eBackup备份管理系统中不存在该存储单元的证书。
- 该存储单元的证书已过期。
##### 处理步骤
##### - 可能原因1：eBackup备份管理系统中不存在该存储单元的证书。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > 证书”，进入“证书”界面，查看该存储单元（类型：S3存储）的证书是否存在。
- 是，执行2。
- 否，执行1.c。
- 请联系管理员获取S3存储的证书。
- 单击“导入”，上传该S3类型存储单元的证书。
##### - 可能原因2：该存储单元的证书已过期。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > 证书”，进入证书界面，查看该存储单元（类型：S3存储）的证书是否过期。
- 是，执行2.c。
- 否，请联系技术支持工程师协助解决。
- 请联系管理员获取S3存储的证书。
- 单击“导入”，上传该S3类型存储单元的有效证书。
##### 参考信息
无
********.46 0x2010E00E0006 存储单元没有可用容量
##### 告警解释
存储单元（名称：[Brick_name]，路径：[Brick_path]）的可用容量已为空。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x2010E00E0006 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Brick_name | 存储单元名称。 |
| Brick_path | 存储单元的路径。 |
##### 对系统的影响
不涉及。
##### 可能原因
存储单元的空间被占满。
##### 处理步骤
##### - 可能原因1：存储单元的空间被占满。
- 请参考《华为云Stack 6.5.1 扩容指南》中的“新增备份存储的容量”章节对存储单元进行扩容，或联系技术支持工程师进行数据离线迁移。
- 等待8分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.47 0x2010E01D0006 证书校验失败
##### 告警解释
没有匹配的eBackup（IP：[IP_Address]）证书。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x2010E01D0006 | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| IP_Address | eBackup管理平面的IP。 |
##### 对系统的影响
可能影响信息安全。
##### 可能原因
- 系统中不存在该eBackup的证书。
- 该eBackup的证书已过期。
##### 处理步骤
##### - 可能原因1：系统中不存在该eBackup的证书。
- 使用浏览器，登录发送告警的节点的GUI。
登录地址：https://发送告警的节点IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > 证书”，进入“证书”界面。如果告警上报的IP地址为Manager的IP地址，则查看是否存在备份管理服务器证书；如果告警上报的IP地址为Server的IP地址，则查看是否存在备份服务器证书。
- 是，执行2。
- 否，执行1.c。
- 请联系管理员获取证书。如果告警上报的IP地址为Manager的IP地址，则获取备份管理服务器证书；如果告警上报的IP地址为Server的IP地址，则获取备份服务器证书。
- 单击“导入”，上传该eBackup的证书。
##### - 可能原因2：该eBackup的证书已过期。
- 使用浏览器，登录发送告警的节点的GUI。
登录地址：https://发送告警的节点IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > 证书”，进入“证书”界面。如果告警上报的IP地址为Manager的IP地址，则查看是否存在备份管理服务器证书；如果告警上报的IP地址为Server的IP地址，则查看是否存在备份服务器证书。
- 是，执行2.c。
- 否，请联系技术支持工程师协助解决。
- 请联系管理员获取证书。如果告警上报的IP地址为Manager的IP地址，则获取备份管理服务器证书；如果告警上报的IP地址为Server的IP地址，则获取备份服务器证书。
- 单击“导入”，上传该eBackup的有效证书。
##### 参考信息
无
********.48 0x1000C90000 eBackup服务器之间失去连接
##### 告警解释
eBackup服务器（IP：[Server_ip1]）与（IP：[Server_ip2]）失去连接。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1000C90000 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Server_ip1 | eBackup服务器IP地址。 |
| Server_ip2 | eBackup服务器IP地址。 |
##### 对系统的影响
系统性能将受影响，备份和恢复任务将失败。
##### 可能原因
- eBackup服务器之间的网络连接中断。
- 网络性能差。
##### 处理步骤
##### - 可能原因1：eBackup服务器之间的网络连接中断。
- 登录IP地址为Server_ip1的eBackup服务器。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 检查eBackup服务器之间的网路连通性。如果是IPv4，执行“ping Server_ip2”，如果是IPv6，执行“ping6 Server_ip2”，检查是否可ping通。
- 是，执行2。
- 否，请联系机房管理员修复网络连通性，确保网络正常连接。
##### - 可能原因2：网络性能差。
- 根据1的执行结果，查看是否存成在丢包的现象。
- 是，请管理员提高网络性能质量，保证eBackup服务器间通信稳定，转到2.b。
- 否，请联系技术支持工程师协助解决。
- 使用浏览器，登录Manager或Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“监控 > 服务器”，进入“服务器”界面，查看该服务器是否处于可访问状态。
- 是，执行2.d。
- 否，请联系技术支持工程师协助解决。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.49 0x10E00C0000 存储库容量不足
##### 告警解释
存储库（名称：[Namespace_name]）的容量不足。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E00C0000 | 次要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Namespace_name | 存储库的名称。 |
##### 对系统的影响
需访问此存储库的备份及恢复任务均将失败。
##### 可能原因
存储库的使用容量已经超出设置容量。
##### 处理步骤
##### - 可能原因1：存储库的使用容量已经超出设置容量。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“备份存储 > 存储库”，进入“存储库”界面，查看该存储库的“利用率”是否大于“告警阈值”。
- 是，执行1.c。
- 否，请联系技术支持工程师协助解决。
- 在“存储库”界面，选择该存储库，获取该存储库的存储单元信息。
- 请参考《华为云Stack 6.5.1 扩容指南》中的“新增备份存储的容量”对存储单元进行扩容，或联系技术支持工程师进行数据离线迁移。
- 在当前告警界面手动清除该告警。
##### 参考信息
无
********.50 0x10E01C0000 添加Workflow或Proxy失败
##### 告警解释
添加Workflow或Proxy（IP：[IP_addr]）失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C0000 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| IP_addr | IP地址。 |
##### 对系统的影响
不涉及。
##### 可能原因
- Workflow（Proxy）与Manager（Server）之间的网络连接中断。
- 网络性能差。
##### 处理步骤
##### - 可能原因1：Workflow（Proxy）与Manager（Server）之间的网络连接中断。
- 参考登录eBackup服务器登录eBackup-Manager或Server。
- 执行“su root”命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 如果是IPv4，执行“ping Workflow或Proxy的IP地址”，如果是IPv6，执行“ping6 Workflow或Proxy的IP地址”，检查是否可以ping通。
- 是，执行2。
- 否，请联系机房管理员修复网络，确保网络连接正常。
##### - 可能原因2：网络性能差。
- 根据1的执行结果，查看是否存成在丢包的现象。
- 是，联系管理员提高网络性能质量，保证Workflow（Proxy）和Manager（Server）间通信稳定。
- 否，执行2.b。
- 使用浏览器，登录Manager或Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“监控 > 服务器”，进入“服务器”界面，查看该服务器是否处于可访问状态。
- 是，执行2.d。
- 否，请联系技术支持工程师协助解决。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.51 0x6000760001 HA的证书已过期
##### 告警解释
在eBackup服务器（IP：[NodeIP]）上的HA功能的证书已过期。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x6000760001 | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| NodeIP | eBackup服务器的IP地址。 |
##### 对系统的影响
HA功能存在安全风险。
##### 可能原因
- HA功能的证书已过期。
- 系统时间不在HA功能的证书的有效期内。
##### 处理步骤
##### - 可能原因1：HA的证书已过期。
- 请联系管理员获取有效的HA证书文件。
- 使用浏览器，登录Manager或Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“监控 > 服务器”，进入“服务器”界面，查看HA的主节点（角色是“备份服务器（主）”或“备份管理服务器（主）”）和备节点（角色是“备份服务器（备）”或“备份管理服务器（备）”）的备份管理平面的IP地址。
- 分别登录HA的主备节点。
默认帐户：hcp，默认密码：*****
- 执行su root命令，输入root用户密码，切换至root用户。
- 执行mv /opt/huawei-data-protection/ebackup/ha/local/cert/cacert.pem /opt/huawei-data-protection/ebackup/ha/local/cert/cacert.pem.bak和mv /opt/huawei-data-protection/ebackup/ha/local/cert/server.pem /opt/huawei-data-protection/ebackup/ha/local/cert/server.pem.bak命令，备份原有HA证书文件。
- 通过WinSCP工具将cacert.pem和server.pem文件拷贝到“/home/<USER>
默认帐户：hcp，默认密码：*****
- 执行mv /home/<USER>/cacert.pem /opt/huawei-data-protection/ebackup/ha/local/cert/和mv /home/<USER>/server.pem /opt/huawei-data-protection/ebackup/ha/local/cert/命令，将证书文件移动HA的证书路径下。
- 执行chmod 600 /opt/huawei-data-protection/ebackup/ha/local/cert/cacert.pem和chmod 600 /opt/huawei-data-protection/ebackup/ha/local/cert/server.pem命令，将“cacert.pem”和“server.pem”文件的权限设置为600。
- 执行chown hcpprocess /opt/huawei-data-protection/ebackup/ha/local/cert/cacert.pem和chown hcpprocess /opt/huawei-data-protection/ebackup/ha/local/cert/server.pem命令，将“cacert.pem”和“server.pem”文件的所有者修改为hcpprocess。
- 执行/opt/huawei-data-protection/ebackup/ha/module/hacom/script/stop_ha_process.sh命令，重启HA服务。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### - 可能原因2：系统时间不在HA功能的证书的有效期内。
- 使用PuTTY，以管理IP地址登录eBackup-Manager节点（Manager角色所在节点）或Server角色所在节点。
登录地址：
- eBackup-Manager节点的管理IP地址为“Workflow-PublicService-IP0”字段对应的IP地址。
- Server角色所在节点的管理IP地址为“datamover_externalom_iplist”字段对应的IP地址。
默认帐户：hcp，默认密码：*****
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行date命令检查系统时间是否与当前时间一致。
- 是，请联系技术支持工程师协助解决。
- 否，执行2.d。
- 执行date -s "年-月-日 时:分:秒"命令，手动修改系统时间。
- 等待3分钟左右，在当前告警界面查看该告警是否恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.52 0x10E01C0027 服务进程异常
##### 告警解释
eBackup服务器（IP address：[IP_Address]）上服务（服务名称：[Service_Name]，进程名称：[Process_Name]）异常。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C0027 | 次要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| IP_Address | ebackup服务器IP地址。 |
| Process_Name | 异常进程列表。 |
| Service_Name | 服务名称。 |
##### 对系统的影响
可能造成业务中断。
##### 可能原因
- eBackup升级、配置NTP、同步时间、替换证书，人为重启eBackup服务或eBackup虚拟机。
- 进程异常退出。
##### 处理步骤
##### - 可能原因1：eBackup升级，移除HA成员，配置NTP，同步时间，替换证书，人为重启eBackup服务或eBackup虚拟机。
- 是否同时满足以下两个条件：
- 异常进程为AdminNode或iBase。
- eBackup已配置HA且HA当前主节点IP地址和发送本条告警的节点IP地址不相同。
- 是，执行1.e。
- 否，执行1.b。
- 使用PuTTY，通过告警上报的服务器的IP地址登录eBackup服务器。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 执行“ps -ef | grep 异常进程名 | grep -v grep”查看该进程是否存在。
- 是，执行1.e。
- 否，请联系技术支持工程师协助解决。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
##### - 可能原因2：进程异常退出。
请联系技术支持工程师协助解决。
##### 参考信息
无。
********.53 0x10E00E0000 连接存储单元失败
##### 告警解释
Proxy（IP：[Node_IP]）与存储单元（路径：[Brick_path]）已失去连接。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E00E0000 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Node_IP | 备份代理的IP地址。 |
| Brick_path | 存储单元的路径。 |
##### 对系统的影响
Proxy将不再运行需访问此存储单元的任务，除非连接恢复正常。
##### 可能原因
- Proxy与存储设备之间的网络连接中断。
- 网络性能差。
- 如果存储单元类型为S3，检查S3的AK和SK是否正确。
- Manager或Server没有权限访问共享存储。
##### 处理步骤
##### - 可能原因1：Proxy与存储设备之间的网络连接中断。
- 使用PuTTY，通过告警上报的IP地址登录Proxy节点。
默认帐户：hcp，默认密码：*****
- 执行su root命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 如果是IPv4，执行ping 存储单元的IP地址，如果是IPv6，执行ping6 存储单元的IP地址，检查是否可以ping通。
- 是，执行2。
- 否，请联系机房管理员修复网络，确保网络连接正常。
##### - 可能原因2：网络性能差。
- 根据1的执行结果，查看是否存成在丢包的现象。
- 是，联系管理员提高网络性能质量，保证备Proxy和存储设备间通信稳定，处理结束。
- 否，执行3。
##### - 可能原因3：如果存储单元类型为S3，检查S3的AK和SK是否正确。
- 使用PuTTY，通过告警上报的IP地址登录Proxy节点。
默认帐户：hcp，默认密码：*****
- 执行su root命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 执行cd /opt/huawei-data-protection/ebackup/sbin命令，进入“sbin”目录；
- 执行export LD_LIBRARY_PATH=/opt/huawei-data-protection/ebackup/libs命令，导入LD_LIBRARY_PATH环境变量。
- 执行./uds_plug-in TestBucket S3存储IP地址 桶名 AK SK命令，查看AK、SK以及桶名是否正确。
- 是，4。
- 否，请联系管理员获取正确的AK、SK以及桶名，重新配置存储单元。
##### - 可能原因4：Manager或Server没有权限访问共享存储。
- 参考登录eBackup服务器登录eBackup-Manager和Server。
- 执行su root命令，输入root用户密码，切换至root用户登录。
- 执行chown hcpprocess:hcpmgr /opt/huawei-data-protection/ebackup/db_bak命令和ls /opt/huawei-data-protection/ebackup/db_bak命令，检查该目录能否更改目录权限和正常访问。
- 是，联系技术支持工程师协助解决。
- 否，请联系存储设备管理员，开通Manager或Server对存储设备的访问权限。
当为NFS存储时，确保存储侧不限制eBackup服务器root用户的权限，使该用户对NFS共享目录具有完全控制权限。
当为CIFS存储时，确保该用户对CIFS共享目录具有完全控制权限。
当为S3存储时，确保eBackup对S3存储的桶有完全控制权限。
##### 参考信息
无
********.54 0x10E01C0029 浮动IP连接异常
##### 告警解释
HA节点（IP：[Node_Name]）与浮动IP（IP：[IP]）连接失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C0029 | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Node_Name | HA节点IP地址。 |
| IP | 浮动IP地址。 |
##### 对系统的影响
可能导致外部对接业务中断。
##### 可能原因
- HA主节点和备节点之间的网络连接中断。
- 网络性能差。
##### 处理步骤
##### - 可能原因1：HA主节点和备节点之间的网络连接中断。
- 使用浏览器，登录Manager或Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“监控 > 服务器”，进入“服务器”界面，查看HA的主节点和备节点的IP地址。
- 登录HA的主节点。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 如果是IPv4，执行“ping HA的备节点IP地址”，如果是IPv6，执行“ping6 HA的备节点IP地址”，检查是否可以ping通。
- 是，执行2。
- 否，请联系机房管理员修复网络连通性，确保网络正常连接。
##### - 可能原因2：网络性能差。
- 根据1的执行结果，查看是否存成在丢包的现象。
- 是，联系管理员提高网络性能质量，保证HA主节点和备节点之间通信稳定。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.55 0x10E01C0001 Workflow（Proxy）和Manager（Server）的版本不兼容
##### 告警解释
Workflow（Proxy）（IP：[IP_addr]）与Manager（Server）的版本不兼容。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C0001 | 次要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| IP_addr | Workflow（Proxy）IP地址。 |
##### 对系统的影响
Workflow（Proxy）在Manager（Server）上进行注册时将失败。
##### 可能原因
Workflow（Proxy）与Manager（Server）的版本不同。
##### 处理步骤
##### - 可能原因：Workflow（Proxy）与Manager（Server）的版本不同。
- 使用PuTTY，依次登录告警上报的节点以及该节点的主节点。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 在Workflow（Proxy）和Manager（Server）上执行“showsys | grep "Product Version"”命令，检查Workflow（Proxy）与Manager（Server）版本是否一致，如果版本不一致，则升级Workflow（Proxy）版本与Manager（Server）一致。
具体操作方法请参考《OceanStor BCManager xxx eBackup 升级指导书》。
- 检查所有Workflow（Proxy）的版本是否和Manager（Server）一致，针对版本号和Manager（Server）版本不一致的Workflow（Proxy）则进行升级操作，确保和Manager（Server）版本一致。
##### 参考信息
无
********.56 0x10E01C0002 主备参数不一致导致HA功能异常
##### 告警解释
主备参数不一致，HA功能异常。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C0002 | 紧急 | 是 |
##### 对系统的影响
HA功能不可用。
##### 可能原因
主备参数不一致。
##### 处理步骤
##### - 可能原因1：主备参数不一致。
- 使用浏览器，登录Manager或Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“监控 > 服务器”，进入“服务器”界面，查看HA的主节点和备节点的IP地址。
- 使用PuTTY，登录HA的主节点。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 执行cd /opt/huawei-data-protection/ebackup/ha/module/hacom/script命令，进入script目录。
- 执行sh config_ha.sh -a命令，查看HA主备节点参数是否一致。
- 是，执行1.g。
- 否，请联系技术支持工程师协助解决。
回显类似如下表示HA主备节点参数是一致的。
HA主节点回显类似如下：
[root@eBackup script]# sh config_ha.sh -a
HaMode:       double
HaLocalName:  ha1(active)
HaPeerName:   ha2(standby)
HaProtocol:   ssl
HaArbLk:      **************:5555  --   **************:5555
HaSyncLk:     **************:6666  --   **************:6666
HaRpcLk:      127.0.0.1:61806
HaArpLk:      **************
HaGwLk:       ***********
HA备节点回显类似如下：
linux-MAVRil:/opt/huawei-data-protection/ebackup/ha/module/hacom/script # sh config_ha.sh -a
HaMode:       double
HaLocalName:  ha2(standby)
HaPeerName:   ha1(active)
HaProtocol:   ssl
HaArbLk:      **************:5555  --  **************:5555
HaSyncLk:     **************:6666  --  **************:6666
HaRpcLk:      127.0.0.1:61806
HaArpLk:      **************
HaGwLk:       ***********
- 在当前告警界面手动清除该告警。
##### 参考信息
无
********.57 0x10E01C0003 恢复主备倒换功能失败
##### 告警解释
取消禁止主备倒换失败，HA功能异常。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C0003 | 紧急 | 是 |
##### 对系统的影响
HA无法进行主备倒换。
##### 可能原因
- HA主节点和备节点之间的网络连接中断。
- 网络性能差。
- 内部处理错误。
##### 处理步骤
##### - 可能原因1：HA主节点和备节点之间的网络连接中断。
- 使用浏览器，登录Manager或Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“监控 > 服务器”，进入“服务器”界面，查看HA的主节点和备节点的IP地址。
- 使用PuTTY，登录HA的主节点。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 如果是IPv4，执行“ping HA的备节点IP地址”，如果是IPv6，执行“ping6 HA的备节点IP地址”，检查是否可以ping通。
- 是，执行2。
- 否，请联系机房管理员修复网络连通性，确保网络正常连接。
##### - 可能原因2：网络性能差。
- 根据1的执行结果，查看是否存成在丢包的现象。
- 是，联系管理员提高网络性能质量，保证HA主节点和备节点之间通信稳定。
- 否，执行3。
##### - 可能原因3：内部处理错误。
- 使用PuTTY，登录HA的主节点。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 执行“cd /opt/huawei-data-protection/ebackup/ha/module/hacom/tools”命令，进入tools目录。
- 执行“./ha_client_tool --cancelforbidswitch”命令，查看命令是否可以执行成功。
- 是，执行3.e。
- 否，请联系技术支持工程师协助解决。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
##### 参考信息
无
********.58 0x10E01C0004 HA主备节点心跳中断
##### 告警解释
节点（IP：[Node_Name]）连续[Time]秒（keepalive配置的心跳中断时间）未收到对端的心跳消息。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C0004 | 紧急 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Node_Name | HA节点IP地址。 |
| Time | keepalive配置的心跳中断时间。 |
##### 对系统的影响
HA无法进行主备倒换。
##### 可能原因
- HA主节点和备节点之间的网络连接中断。
- 网络性能差。
- 节点复位。
##### 处理步骤
##### - 可能原因1：HA主节点和备节点之间的网络连接中断。
- 使用浏览器，登录Manager或Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“监控 > 服务器”，进入“服务器”界面，查看HA的主节点和备节点的IP地址。
- 使用PuTTY，登录HA主节点。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 如果是IPv4，执行“ping HA备节点IP地址”，如果是IPv6，执行“ping6 HA的备节点IP地址”，检查是否可以ping通。
- 是，执行2。
- 否，请联系机房管理员修复网络连通性，确保网络正常连接。
##### - 可能原因2：网络性能差。
- 根据1的执行结果，查看是否存成在丢包的现象。
- 是，联系管理员提高网络性能质，保证HA主节点和备节点之间通信稳定。
- 否，执行3。
##### - 可能原因3：节点复位。
- 使用PuTTY，登录HA的主节点和备节点。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 执行“service hcp status”命令，查看主节点和备节点上的服务状态，是否有服务处于未运行状态。
- 是，等待HA主节点和备节点服务启动完成，转到3.d。
- 否，请联系技术支持工程师协助解决。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.59 0x10E01C0005 HA主节点向备节点同步文件失败
##### 告警解释
HA主节点（IP：[Node_Name_P]）向备节点（IP：[Node_Name_S]）同步文件失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C0005 | 紧急 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Node_Name_P | HA主节点IP地址。 |
| Node_Name_S | HA备节点IP地址。 |
##### 对系统的影响
HA主节点与备节点配置文件不一致。
##### 可能原因
- 文件同步链路故障。
- 节点复位。
- 备节点磁盘空间不足。
##### 处理步骤
##### - 可能原因1：文件同步链路故障。
- 使用浏览器，登录Manager或Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“监控 > 服务器”，进入“服务器”界面，查看HA的主节点和备节点的IP地址。
- 使用PuTTY，登录HA主节点。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 如果是IPv4，执行“ping HA备节点IP地址”，如果是IPv6，执行“ping6 HA的备节点IP地址”，检查是否可以ping通。
- 是，执行2。
- 否，请联系机房管理员修复网络连通性，确保网络正常连接，转到4。
##### - 可能原因2：节点复位。
- 使用PuTTY，依次登录HA的主节点和备节点。
默认帐户：hcp，默认密码：*****
- 执行su root命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 执行service hcp status命令，查看主节点和备节点上的服务状态，是否有服务处于未运行状态。
- 是，等待HA主节点和备节点服务启动完成，转到4。
- 否，执行3。
##### - 可能原因3：备节点磁盘空间不足。
- 以hcp帐户登录HA备节点。
默认帐户：hcp，默认密码：*****
- 执行su root命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 执行df -h命令查看磁盘空间是否足够。
回显类似如下，找到回显中Mounted on列值为/opt的行，查看该行Avail的值是否大于等于500MB。
- 是，请联系技术支持工程师协助解决。
- 否，清理无用文件，保证磁盘空间充足，转到4。
[root@eBackup ~]# df -h
Filesystem                        Size  Used Avail Use% Mounted on
/dev/mapper/euleros-root          9.4G  2.1G  6.8G  24% /
devtmpfs                          7.8G     0  7.8G   0% /dev
tmpfs                             7.8G     0  7.8G   0% /dev/shm
tmpfs                             7.8G  752M  7.1G  10% /run
tmpfs                             7.8G     0  7.8G   0% /sys/fs/cgroup
/dev/vda1                         465M   84M  353M  20% /boot
/dev/mapper/VolGroup2-varvolume    30G  646M   28G   3% /var
tmpfs                             1.6G     0  1.6G   0% /run/user/0
/dev/mapper/VolGroup2-optvolume    59G  6.3G   50G  12% /opt
/dev/mapper/VolGroup2-tmpvolume   4.9G   21M  4.6G   1% /tmp
/dev/mapper/VolGroup2-homevolume  4.9G   21M  4.6G   1% /home
- 手动进行文件同步。
- 执行以下命令设置环境变量。
export LD_LIBRARY_PATH=/opt/huawei-data-protection/ebackup/libs/
- 执行以下命令完成文件同步。
/opt/huawei-data-protection/ebackup/ha/module/hacom/tools/ha_client_tool --syncallfile
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.60 0x10E01C0009 HA仲裁网关不可达
##### 告警解释
在HA节点（IP：[Node_Name]）上仲裁网关(IP：[Gatway_IP])不可达。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C0009 | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Node_Name | HA节点IP地址。 |
| Gatway_IP | 网关IP地址。 |
##### 对系统的影响
备节点无法切换成主节点。
##### 可能原因
- HA主节点和备节点之间的网络连接中断。
- 网络性能差。
- 主备端口没有放通。
##### 处理步骤
##### - 可能原因1：HA主节点和备节点之间的网络连接中断。
- 使用浏览器，登录Manager或Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“监控 > 服务器”，进入“服务器”界面，查看HA的主节点和备节点的IP地址。
- 使用PuTTY，登录HA主节点。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 如果是IPv4，执行“ping HA备节点IP地址”，如果是IPv6，执行“ping6 HA的备节点IP地址”，检查是否可以ping通。
- 是，执行2。
- 否，请联系机房管理员修复网络连通性，确保网络正常连接。
##### - 可能原因2：网络性能差。
- 根据1的执行结果，查看是否存成在丢包的现象。
- 是，联系管理员提高网络性能质量，保证HA主节点和备节点之间通信稳定。
- 否，执行3。
##### - 可能原因3：主备端口没有放通。
- 执行iptables -n -L|grep ACCEPT |grep 6666命令，检查端口是否放通。
- 是，请联系技术支持工程师协助解决。
- 否，执行3.b。
回显类似如下表示已放通主备端口；如果执行命令后没有回显，则说明没有放通主备端口。
回显示例中**************，**************分别为eBackup备节点和主节点IP地址。
[root@eBackup ~]# iptables -n -L|grep ACCEPT |grep 6666
ACCEPT  tcp  --  **************  **************     multiport dports 5555,6666
- 执行以下命令放通主备端口。
- 如果是IPv4，执行以下命令：
iptables -I INPUT -p tcp -s 主节点内部通信平面IP地址 -d 备节点内部通信平面IP地址 -m multiport --dport 5555,6666 -j ACCEPT
- 如果是IPv6，执行以下命令：
ip6tables -I INPUT -p tcp -s 主节点内部通信平面IP地址 -d 备节点内部通信平面IP地址 -m multiport --dport 5555,6666 -j ACCEPT
##### 参考信息
无
********.61 0x10E01C000B 数据库升主失败
##### 告警解释
数据库在HA节点（IP：[Node_Name]）上升主失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C000B | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Node_Name | HA节点IP地址。 |
##### 对系统的影响
可能导致数据库服务异常。
##### 可能原因
- 数据库同步进度小于规定进度（默认:90%）。
- 数据库同步成功时间小于规定值（默认:10分钟）。
##### 处理步骤
##### - 可能原因1：数据库同步进度小于规定进度（默认：90%）。
- 使用浏览器，登录Manager或Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“监控 > 服务器”，进入“服务器”界面，获取HA主节点和备节点的IP地址。
- 使用PuTTY，登录HA主节点。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 执行cat /opt/huawei-data-protection/ebackup/conf/db_sync.conf命令打开db_sync.conf文件，查看sync_progress值是否小于allow_sync_progress的值。
- 是，请联系技术支持工程师协助解决。
- 否，执行2。
##### - 可能原因2：数据库同步成功时间小于规定值（默认：10分钟）。
- 使用PuTTY，登录HA主节点。
默认帐户：hcp，默认密码：*****
- 执行su root命令，输入ROOT用户密码，切换至root用户。
root帐号的默认密码为*****。
- 执行cat /opt/huawei-data-protection/ebackup/conf/db_sync.conf命令打开db_sync.conf文件，查看sync_time的值并执行date -d "@sync_time" +"%F %H:%M:%S"将其转换为标准时间，计算与当前时间的差值，是否小于10分钟。
- 是，执行3。
- 否，请联系技术支持工程师协助解决。
- 重启HA主备节点服务。
- 使用PuTTY，依次登录HA主节点和备节点。
默认帐户：hcp，默认密码：*****
- 执行su root命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 在主备节点上分别执行/opt/huawei-data-protection/ebackup/bin/db_sync_monitor.sh get_status命令查看节点状态。
- 如果两个节点的角色为Primary和Standby，执行3.e。如果两个节点的角色都为Primary，执行3.h。
- 在Standby节点上执行service hcp stop命令，停止Standby节点服务。
- 在Primary节点上执行service hcp restart force命令强制重启Primary节点服务。
- 等待Primary节点上的服务重启完成后，再在Standby节点上执行service hcp start命令，启动Standby节点上的服务，转到4。
- 在主节点上执行cd /opt/huawei-data-protection/ebackup/ha/module/hacom/script命令，进入script目录。
- 在主节点上执行sh status_ha.sh命令，从回显信息中获取“StartTime”字段信息。
- 选择StartTime显示时间距当前时刻较远的节点，在该节点上执行service hcp stop命令停止该节点的服务。
- 选择StartTime显示时间距当前时刻较近的节点，在该节点上执行service hcp restart force命令强制重启该节点的上的服务。
- 在StartTime较近的节点上，执行service hcp restart force命令强制重启该节点的上的服务。
- 等待StartTime较近的节点上的服务重启完成后，再在StartTime较远的节点上执行service hcp start命令，启动该节点上的服务，转到4。
- 查看告警是否消除。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.62 0x10E01C000E AdminNode服务异常
##### 告警解释
在HA节点（IP：[Node_Name]）上AdminNode服务异常。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C000E | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Node_Name | HA节点IP地址。 |
##### 对系统的影响
可能造成业务中断。
##### 可能原因
- 系统发生HA主备倒换。
- AdminNode进程异常。
##### 处理步骤
##### - 可能原因1：系统发生HA主备倒换。
- 使用PuTTY，通过告警上报的IP地址登录eBackup服务器。
默认帐户：hcp，默认密码：*****
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行service hcp status命令，查看回显信息中是否存在“AdminNode”字段，且命令执行结果为“running”。
- 是，执行2。
- 否，执行1.d。
- 等待3分钟左右，在当前告警界面查看该告警是否恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### - 可能原因2：AdminNode进程异常。
- 使用PuTTY，通过告警上报的IP地址登录eBackup服务器。
默认帐户：hcp，默认密码：*****
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行service hcp start命令，启动AdminNode服务。
- 执行service hcp status命令，查看回显信息中是否存在“AdminNode is running”。
- 是，执行2.e。
- 否，请联系技术支持工程师协助解决。
- 等待3分钟左右，在当前告警界面查看该告警是否恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.63 0x10E01C000F 浮动IP服务异常
##### 告警解释
在HA节点（IP：[Node_Name]）上浮动IP服务异常。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C000F | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Node_Name | HA节点IP地址。 |
##### 对系统的影响
可能引起外部对接业务中断。
##### 可能原因
- 管理平面网卡异常。
- 浮动IP冲突。
##### 处理步骤
##### - 可能原因1：管理平面网卡异常。
- 使用浏览器，登录Manager或Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“监控 > 服务器 > HA管理 > 修改HA参数”，进入“修改HA参数”界面，查看配置的浮动IP地址。
- 使用PuTTY，通过告警上报的服务器的IP地址登录eBackup服务器。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 如果是IPv4，执行1.f；如果是IPv6，执行1.i。
- 执行“ifconfig”命令，查看是否存在“mgr”网卡。
- 是，执行2。
- 否，执行1.g。
- 执行ifconfig 管理平面网卡名称 up命令，查看是否可以将该网卡启动。
如：执行ifconfig mgr up命令，其中mgr为管理平面的网卡名称。
- 是，执行1.h。
- 否，请联系技术工程师协助解决。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，2。
- 执行“ifconfig”命令，查看浮动IP地址，是否在管理平面网卡展示的信息内。
- 是，执行2。
- 否，请联系技术支持工程师协助解决。
##### - 可能原因2：浮动IP冲突。
- 使用PuTTY，通过告警上报的服务器的IP地址登录eBackup服务器。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 如果是IPv4，执行“ping 浮动IP地址”，如果是IPv6，执行“ping6 浮动IP地址”，检查是否可以ping通。
- 是，执行2.d。
- 否，请联系技术支持工程师协助解决。
- 使用浏览器，登录Manager或Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“监控 > 服务器 > HA管理 > 修改HA参数”，进入“修改HA参数”界面，更换浮动IP。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.64 0x10E01C0010 GaussDB服务异常
##### 告警解释
在HA节点（IP：[Node_Name]）上GaussDB服务异常。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C0010 | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Node_Name | HA节点IP地址。 |
##### 对系统的影响
可能造成业务中断。
##### 可能原因
- 数据库同步状态异常。
- 有残留的gaussdb进程。
##### 处理步骤
##### - 可能原因1：数据库同步状态异常。
- 在当前告警界面查看ID为0x10E01C000F（浮动IP服务异常）的告警是否存在。
- 是，执行1.b。
- 否，执行1.d。
##### - 请参考0x10E01C000F（浮动IP服务异常）这条告警的处理步骤，先清除0x10E01C000F这条告警。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，执行1.d。
- 在当前告警界面查看ID为0x10E01C0011（ibase服务异常）的告警是否存在。
- 是，执行1.e。
- 否，执行2。
##### - 请参考0x10E01C0011（ibase服务异常）这条告警的处理步骤，先清除0x10E01C0011这条告警。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，执行2。
##### - 可能原因2：有残留的gaussdb进程。
- 使用PuTTY，通过告警上报的服务器的IP地址登录eBackup服务器。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 执行“ps -ef|grep gaussdb”命令查看是否存在gaussdb进程。
- 是，执行2.d。
- 否，请联系技术支持工程师协助解决。
- 执行“killall -9 gaussdb”命令停掉残留进程。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.65 0x10E01C0011 ibase服务异常
##### 告警解释
在HA节点（IP：[Node_Name]）上ibase服务异常。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C0011 | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Node_Name | HA节点IP地址。 |
##### 对系统的影响
可能造成业务中断。
##### 可能原因
- ibase端口被占用。
- httpd启动失败。
##### 处理步骤
##### - 可能原因1：ibase端口被占用。
- 使用PuTTY，通过告警上报的服务器的IP地址登录eBackup服务器。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户登录。
root帐号的默认密码为*****。
- 执行“netstat -anp | grep 8989”命令，查看执行结果中8989端口是否处于“LISTEN”状态。
- 是，执行1.d。
- 否，执行2。
- 找到“LISTEN”状态后面的进程ID，执行“kill -9 进程ID”，释放8989端口。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，执行2。
##### - 可能原因2：httpd端口被占用。
- 使用PuTTY，通过告警上报的服务器的IP地址登录eBackup服务器。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户登录。
root帐号的默认密码为*****。
- 执行“netstat -anp | grep 8080”和“netstat -anp | grep 8087”命令，查看执行结果中8080和8087端口是否处于“LISTEN”状态
- 是，执行2.d。
- 否，请联系技术支持工程师协助解决。
- 找到“LISTEN”状态后面的进程ID，执行“kill -9 进程ID”，释放8080和8087端口。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.66 0x10E01C0028 证书校验失败
##### 告警解释
云平台仲裁没有可匹配的证书。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C0028 | 次要 | 否 |
##### 对系统的影响
与云平台仲裁服务器之间的连接存在安全风险。
##### 可能原因
- 系统中不存在云平台仲裁的证书。
- 云平台仲裁的证书已过期。
##### 处理步骤
##### - 可能原因1：系统中不存在云平台仲裁服务器的证书。
- 使用浏览器，登录Manager的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > 证书”，进入“证书”界面，查看云平台仲裁服务器的证书是否存在。
- 是，执行2。
- 否，执行1.c。
- 请联系管理员获取云平台仲裁服务器证书。
- 单击“导入”，上传云平台仲裁服务器的证书。
##### - 可能原因2：云平台仲裁服务器的证书已过期。
- 使用浏览器，登录Manager的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > 证书”，进入“证书”界面，查看云平台仲裁服务器的证书是否过期。
- 是，执行2.c。
- 否，请联系技术支持工程师协助解决。
- 请联系管理员获取云平台仲裁服务器证书。
- 单击“导入”，上传云平台仲裁服务器的有效证书。
##### 参考信息
无。
********.67 0x10E00E0001 访问存储单元失败
##### 告警解释
Proxy（IP：[Node_IP]）访问存储单元（路径：[Brick_path]）失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E00E0001 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Node_IP | 备份代理的IP地址。 |
| Brick_path | 存储单元的路径。 |
##### 对系统的影响
Proxy将不再运行访问此存储单元的任务，除非运行恢复正常。
##### 可能原因
- Proxy与存储单元之间的网络连接中断。
- 网络性能差。
##### 处理步骤
##### - 可能原因：Proxy与存储单元之间的网络连接中断。
- 通过告警上报的IP地址登录Proxy。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 如果是IPv4，执行“ping 存储单元IP地址”，如果是IPv6，执行“ping6 存储单元IP地址”，检查是否可以ping通。
- 是，执行2。
- 否，请联系机房管理员修复网络，确保网络连接正常。
##### - 可能原因2：网络性能差。
- 根据1的执行结果，查看是否存成在丢包的现象。
- 是，联系管理员提高网络性能质量，保证Proxy与存储单元之间通信稳定。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.68 0x10E0140001 扫描受保护环境失败
##### 告警解释
扫描受保护环境（名称：[Protected_Env_name]）失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E0140001 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Protected_Env_name | 受保护环境名称。 |
##### 对系统的影响
对该受保护环境下的受保护对象的备份和恢复操作将失败。
##### 可能原因
- Proxy与受保护环境间网络连接中断。
- 受保护环境的登录配置无效。
##### 处理步骤
##### - 可能原因1：Proxy与受保护环境间网络连接中断。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 在导航栏上单击“受保护环境 > VMware”或者“受保护环境 > 存储设备”界面，找到对应的受保护环境并且获取IP。
- 参考登录eBackup服务器登录Proxy节点。
- 执行“su root”命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 检查Proxy与受保护环境之间的网路连通性。如果是IPv4，执行“ping 受保护环境IP”，如果是IPv6，执行“ping6 受保护环境IP”，检查是否可ping通。
- 是，执行1.f。
- 否，请联系机房管理员修复网络连通性，确保网络正常连接。
- 在Server的左侧导航栏中单击对应受保护环境后的扫描符号，重新扫描受保护环境，查看扫描结果是否成功。
- 是，处理结束。
- 否，执行2。
##### - 可能原因2：受保护环境的登录配置无效。
- 联系受保护环境管理员获取用户名和密码。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 如果受保护环境是“VMware”或者“存储设备”，执行2.d。
- 在导航栏中单击“受保护环境 > VMware”或者“受保护环境 > 存储设备”，单击左侧导航栏的修改符号，编辑该受保护环境，并输入最新的用户名和密码，重新扫描该受保护环境，查看结果是否成功。
- 是，处理结束。
- 否，联系技术支持工程师协助解决。
##### 参考信息
无
********.69 0x10E0140000 连接受保护环境失败
##### 告警解释
Server与受保护环境（IP：[Protected_Env_IP]）连接失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E0140000 | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Protected_Env_IP | 受保护环境IP地址。 |
##### 对系统的影响
在受保护环境下的受保护对象的备份以及恢复操作将失败。
##### 可能原因
- Server与受保护环境间网络连接中断。
- 受保护环境的登录配置无效。
##### 处理步骤
##### - 可能原因1：Server与受保护环境间网络连接中断。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 在导航栏上单击“受保护环境 > VMware”或者“受保护环境 > 存储设备”界面，找到对应的受保护环境并且获取IP地址。
- 参考登录eBackup服务器登录Server。
- 执行“su root”命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 检查Server与受保护环境之间的网路连通性。如果是IPv4，执行“ping 受保护环境IP”，如果是IPv6，执行“ping6 受保护环境IP”，检查是否可ping通。
- 是，执行1.f。
- 否，请联系机房管理员修复网络连通性，确保网络正常连接。
- 在Server的左侧导航栏中单击对应受保护环境后的扫描符号，重新扫描受保护环境，查看扫描结果是否成功。
- 是，处理结束。
- 否，执行2。
##### - 可能原因2：受保护环境的登录配置无效。
- 联系受保护环境管理员获取用户名和密码。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 如果受保护环境是“VMware”或者“存储设备”，执行2.d。
- 在导航栏中单击“受保护环境 > VMware”或者“受保护环境 > 存储设备”，单击左侧导航栏的修改符号，编辑该受保护环境，并输入最新的用户名和密码，重新扫描该受保护环境，查看结果是否成功。
- 是，处理结束。
- 否，联系技术支持工程师协助解决。
##### 参考信息
无。
********.70 0x2010E014000C 证书校验失败
##### 告警解释
受保护环境（IP：[IP_Address]）没有可匹配的证书。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x2010E014000C | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| IP_Address | 受保护环境的IP地址。 |
##### 对系统的影响
与受保护环境之间的连接存在安全风险。
##### 可能原因
- 系统中不存在该受保护环境的证书。
- 该受保护环境的证书已过期。
##### 处理步骤
##### - 可能原因1：系统中不存在该受保护环境的证书。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > 证书”，进入“证书”界面，查看该受保护环境的证书是否存在。
- 是，执行2。
- 否，执行1.c。
- 请联系管理员获取受保护环境的证书。
- 单击“导入”，上传该受保护环境的证书。
##### - 可能原因2：该受保护环境的证书已过期。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > 证书”，进入“证书”界面，查看该受保护环境的证书是否过期。
- 是，执行2.c。
- 否，请联系技术支持工程师协助解决。
- 请联系管理员获取受保护环境的证书。
- 单击“导入”，上传该受保护环境的有效证书。
##### 参考信息
无
********.71 0x10E00D0000 存储池空间使用率超出临界值
##### 告警解释
当前存储池（名称：[Storagepool_name]）的空间使用率已超出临界值。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E00D0000 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Storagepool_name | 存储池名称。 |
##### 对系统的影响
不涉及。
##### 可能原因
- 存储池的空间使用率已经超出临界值。
- 存储池的空间被占满。
##### 处理步骤
##### - 可能原因1：存储池的空间使用率已经超出临界值。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“备份存储 > 存储池”，进入“存储池”界面，查看该存储池的“利用率”是否大于“告警阈值”。
- 是，执行1.c。
- 否，请联系技术支持工程师协助解决。
- 在“存储池”界面，选择该存储池，获取该存储池的存储单元信息。
- 请联系管理员对该存储池里的存储单元进行扩容或数据离线迁移。
- 等待3分钟左右，在当前告警界面查看该告警是否恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.72 0x201000C90002 不支持当前时区信息
##### 告警解释
系统不支持当前设置的时区。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000C90002 | 次要 | 否 |
##### 对系统的影响
eBackup备份管理页面显示时区信息不正确。
##### 可能原因
当前设置时区信息无法被软件识别。
##### 处理步骤
##### - 可能原因1: 当前设置时区信息无法被软件识别。
- 使用浏览器，依次登录Manager和Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > 系统时间&时区”，进入“系统时间&时区”界面。
- 选择系统时区，并单击“确定”。
##### 参考信息
无
********.73 0x201000C90025 NTP时间差异过大
##### 告警解释
NTP服务器（IP/域名:[IP_addr1]）与eBackup服务器（IP:[IP_addr2]）时间差异过大，相差（[Time_diff]分钟）。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000C90025 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| IP_addr1 | NTP服务器IP地址。 |
| IP_addr2 | eBackup服务器IP地址。 |
| Time_diff | NTP服务器与eBackup服务器之间的时间差值。 |
##### 对系统的影响
不涉及。
##### 可能原因
NTP服务器与eBackup服务器时间差异过大。
##### 处理步骤
##### - 可能原因1: NTP服务器与eBackup服务器时间差异过大。
请参考NTP服务器与Manager或Server时间差异过大章节。
##### 参考信息
无。
********.74 0x201000C90024 eBackup服务器到NTP服务器连接异常
##### 告警解释
eBackup服务器（IP:[IP_addr1]）到NTP服务器（IP/域名:[IP_addr2]）连接异常。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000C90024 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| IP_addr1 | eBackup服务器IP地址。 |
| IP_addr2 | NTP服务器IP地址。 |
##### 对系统的影响
不涉及。
##### 可能原因
eBackup服务器与NTP服务器之间的网络连接断开。
##### 处理步骤
##### - 可能原因1：eBackup服务器与NTP服务器之间的网络连接断开。
- 使用PuTTY，通过告警上报的IP地址登录eBackup服务器。
默认帐户：hcp，默认密码：*****
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 如果是IPv4，执行ping ntp_server_ip -c 3 > /dev/null; echo $?命令，如果是IPv6，执行ping6 ntp_server_ip -c 3 > /dev/null; echo $?命令，查看显示结果是否为0。
- 是，执行2。
- 否，网络不通，请联系机房管理员修复网络连通性。
##### - 可能原因2：节点NTP服务级数大于15级。
- 使用PuTTY，通过告警上报的IP地址登录eBackup服务器。
默认帐户：hcp，默认密码：*****
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行ntpq -p命令，查看显示结果中“st”字段是否小于等于15。
- 是，eBackup服务器会自动恢复与NTP服务器对时。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
********.75 0x201000C90009 eBackup服务器未设置NTP时钟源
##### 告警解释
eBackup服务器未设置NTP时钟源。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000C90009 | 重要 | 否 |
##### 对系统的影响
不涉及。
##### 可能原因
eBackup服务器未设置NTP时钟源。
##### 处理步骤
##### - 可能原因1: eBackup服务器未设置NTP时钟源。
- 使用浏览器，依次登录Manager和Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > 系统时间&时区”，进入“系统时间&时区”界面。
- 打开NTP服务状态开关。
- 在NTP服务器地址框中输入NTP服务器的IP地址，并单击“确定”。
##### 参考信息
无
********.76 0x5800790001 SFTP服务器空间不足
##### 告警解释
当前管理数据备份设置的SFTP服务器（路径：[SFtp_path]）空间不足。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x5800790001 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| SFtp_path | SFTP服务器路径。 |
##### 对系统的影响
管理数据备份到SFTP服务器失败。
##### 可能原因
SFTP服务器空间不足。
##### 处理步骤
##### - 可能原因1： SFTP服务器空间不足。
- 使用SFTP客户端工具，通过用户和密码登录SFTP服务器。
- 检查SFTP服务器共享目录下是否存在无用的数据。
- 是，执行1.c。
- 否，请联系管理员对SFTP服务器共享存储进行扩容。
- 执行rm -rf 无用的文件或目录命令，删除无用的数据。
##### 参考信息
无
********.77 0x5800790002 登录SFTP服务器被拒绝
##### 告警解释
用户（[User]）登录SFTP服务器（路径：[SFtp_path]）被拒绝。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x5800790002 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| User | SFTP用户。 |
| SFtp_path | SFTP服务器路径。 |
##### 对系统的影响
管理数据备份到SFTP服务器失败。
##### 可能原因
登录SFTP服务器的用户名或密码不正确。
##### 处理步骤
##### - 可能原因1：登录SFTP服务器的用户名或密码不正确。
- 联系管理员获取正确的SFTP服务器的用户名和密码。
- 使用浏览器，依次登录Manager和Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > 配置 > 管理数据备份”，进入“管理数据备份”界面。
- 单击“设置备份存储”，进入“设置备份存储”界面，检查配置的SFTP服务器用户和密码是否正确。
- 是，联系技术支持工程师协助解决。
- 否，执行1.e。
- 重新配置SFTP备份存储，详细请参考SFTP章节。
##### 参考信息
无
********.78 0x5800790003 上传管理数据到SFTP服务器失败
##### 告警解释
用户（[User]）上传管理数据到SFTP服务器（路径：[SFtp_path]）失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x5800790003 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| User | SFTP用户。 |
| SFtp_path | SFTP服务器路径。 |
##### 对系统的影响
管理数据备份到SFTP服务器失败。
##### 可能原因
用户没有权限上传文件到SFTP服务器。
##### 处理步骤
##### - 可能原因1：用户没有权限上传文件到SFTP服务器。
- 使用SFTP客户端工具，通过用户和密码登录SFTP服务器，检查能否上传文件。
- 是，请联系技术支持工程师协助解决。
- 否，执行1.b。
- 联系管理员获取正确的SFTP服务器的用户名和密码，并为Manager和Server开通允许上传文件的权限。
- 使用浏览器，依次登录Manager和Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > 配置 > 管理数据备份”，进入“管理数据备份”页面。
- 重新配置SFTP备份存储，详细请参考SFTP章节。
##### 参考信息
无
********.79 0x10E01A0010 备份副本中缺失受保护对象元数据
##### 告警解释
在执行校验受保护对象（名称：[Machine_name]，UUID/GUID：[Machine_UUID]，备份副本ID：[Snap_id]，创建时间：[Create_time]）的任务时，检测到受保护对象的元数据缺失（或部分缺失），或元数据损坏。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01A0010 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Snap_id | 备份副本ID。 |
| Create_time | 备份副本创建时间。 |
| Machine_name | 备份对象名称。 |
| Machine_UUID | 备份对象UUID。 |
##### 对系统的影响
基于此备份副本的下一次备份任务将提升为全量备份。
##### 可能原因
- 存储单元不可访问。
- 备份副本的受保护对象元数据缺失。
##### 处理步骤
##### - 可能原因1：存储单元不可访问。
- 在当前告警界面查看ID为 0x10E00E0000（连接存储单元失败） 或者0x10E00E0001（访问存储单元失败）的告警是否上报。
- 是，处理1.a所述的告警，保证存储单元可访问。
- 否，执行2。
##### - 可能原因2：备份副本的受保护对象元数据缺失。
请联系技术支持工程师协助解决。
##### 参考信息
无。
********.80 0x10E01A0011 备份副本的元数据损坏
##### 告警解释
在执行校验受保护对象（名称：[Machine_name]，UUID：[Machine_UUID]，备份副本ID： [Snap_id]，创建时间：[Create_time]）的任务时，检测到备份副本的元数据缺失或已损坏。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01A0011 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Snap_id | 备份副本ID。 |
| Create_time | 备份副本创建时间。 |
| Machine_name | 虚拟机名称。 |
| Machine_UUID | 虚拟机UUID。 |
##### 对系统的影响
基于此备份副本的下一次备份任务将提升为全量备份。
##### 可能原因
- 存储单元不可访问。
- 备份副本的元数据损坏。
##### 处理步骤
##### - 可能原因1：存储单元不可访问。
- 在当前告警界面查看ID为0x10E00E0000（连接存储单元失败）或者0x10E00E0001（访问存储单元失败）的告警是否上报。
- 是，处理1.a所述的告警，保证存储单元可访问。
- 否，执行2。
##### - 可能原因2：备份副本的受保护对象元数据损坏。
请联系技术支持工程师协助解决。
##### 参考信息
无
********.81 0x10E01A0019 删除备份副本失败
##### 告警解释
删除备份副本（任务ID：[Task_id]，备份副本ID：[Snap_id]）失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01A0019 | 次要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Task_id | 任务ID。 |
| Snap_id | 备份副本ID。 |
##### 对系统的影响
备份副本会占用备份存储空间。
##### 可能原因
- 存储单元不可访问。
- 备份副本被删除或损坏。
##### 处理步骤
##### - 可能原因1：存储单元不可访问。
- 在当前告警界面查看ID为 0x10E00E0000（连接存储单元失败） 或者0x10E00E0001（访问存储单元失败）的告警是否上报。
- 是，处理1.a所述的告警，保证存储单元可访问。
- 否，执行2。
##### - 可能原因2：备份副本被删除或损坏。
请联系技术支持工程师协助解决。
##### 参考信息
无。
********.82 0x2010E01A0008 检测到备份副本的数据块有损坏
##### 告警解释
在执行校验受保护对象（名称：[Machine_name]，UUID：[Machine_UUID]，备份副本ID：[Snap_id]，创建时间：[Create_time]）的任务（[Task_id]）时，检测到备份副本的数据块有损坏。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x2010E01A0008 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Snap_id | 备份副本ID。 |
| Create_time | 备份副本创建时间。 |
| Machine_name | 受保护对象名称。 |
| Machine_UUID | 受保护对象UUID。 |
| Task_id | 任务ID。 |
##### 对系统的影响
基于此备份副本的下一次备份任务将提升为全量备份。
##### 可能原因
- 存储单元不可访问。
- 备份副本的部分数据块有损坏。
##### 处理步骤
##### - 可能原因1：存储单元不可访问。
- 在当前告警界面查看ID为 0x10E00E0000（连接存储单元失败） 或者0x10E00E0001（访问存储单元失败）的告警是否上报。
- 是，处理1.a所述的告警，保证存储单元可访问。
- 否，执行2。
##### - 可能原因2：备份副本的部分数据块已损坏。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择导航栏上的“恢复 > 全部备份副本”，按照备份副本ID及其产生时间进行搜索，找到当前操作所用备份副本及备份计划名称。
- 选择该备份副本，单击“快速校验”，查看备份副本状态是否为可用。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
********.83 0x2010E01A000E 验证备份副本的任务失败
##### 告警解释
验证备份副本（ID：[Snap_id]）的任务失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x2010E01A000E | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Snap_id | 备份副本ID。 |
##### 对系统的影响
不涉及。
##### 可能原因
- 存储单元不可访问。
- 备份副本的部分数据块已损坏。
##### 处理步骤
##### - 可能原因1：存储单元不可访问。
- 在当前告警界面查看ID为0x10E00E0000（连接存储单元失败） 或者0x10E00E0001（访问存储单元失败）的告警是否上报。
- 是，处理1.a所述的告警，保证存储单元可访问。
- 否，执行2。
##### - 可能原因2：备份副本的部分数据块已损坏。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择导航栏上的“恢复 > 全部备份副本”，按照备份副本ID及其产生时间进行搜索，找到当前操作所用备份副本及备份计划名称。
- 勾选该备份副本，单击“快速校验”。
- 如果校验任务成功，则可能之前校验失败是网络问题导致；如果校验失败，请联系技术支持工程师协助解决。
##### 参考信息
无。
********.84 0x201000C9000A Proxy上的NTP服务异常
##### 告警解释
Proxy（IP：[IP_addr]）上的NTP服务异常。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000C9000A | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| IP_addr | 备份代理IP。 |
##### 对系统的影响
不涉及。
##### 可能原因
- Server上的NTP服务器与Proxy上NTP客户端之间的网络异常或者Proxy与Server的时间间隔超过60s。
- Server上的NTP服务器异常。
##### 处理步骤
261. Server上的NTP服务器与Proxy上NTP客户端之间的网络异常或者Proxy与Server的时间间隔超过60s。
- 参考登录eBackup服务器登录Server。
- 执行“su root”命令，输入root用户密码，切换至“root”用户。
root帐号的默认密码为*****。
- 检查Server和proxy之间的网络连通性。如果是IPv4，执行“ping Proxy的IP”，如果是IPv6，执行“ping6 Proxy的IP”，查看是否可ping通。
- 是，执行1.d。
- 否，请联系机房管理员修复网络连通性，确保网络正常连接。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 进入“监控 > 服务器”界面。
- 单击“NTP时间同步状态”为“未同步”的服务器，在右侧详情面板中单击“同步时间”，查看告警是否可清除。
- 是，处理结束。
- 否，执行2。
##### - 可能原因2: 备份服务器上的NTP服务器异常。
- 使用PuTTY，通过告警上报的IP地址登录Proxy。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至“root”用户。
root帐号的默认密码为*****。
- 执行“cd /opt/huawei-data-protection/ebackup/cli;sh hcpcli.sh admin”命令，登录CLI界面。
- 在setting视图下，执行“change ntp server Proxy的IP” 命令重新设置NTP服务器。
- 在setting视图下，执行“synchronize time all”命令重新同步时间。
##### 参考信息
无。
********.85 0x210000000D00 连接远端vpp服务器失败
##### 告警解释
Proxy（[ipAddr]）连接远端vpp服务器（[vppServerIP]）失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x210000000D00 | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| ipAddr | 本端备份节点IP。 |
| vppServerIP | 远端vpp服务器IP。 |
##### 对系统的影响
不涉及。
##### 可能原因
- Proxy与远端vpp服务器之间的物理连接中断。
- 远端vpp服务器上hcp_vppd进程异常。
##### 处理步骤
##### - 可能原因1：备份代理与远端vpp服务器之间的物理连接中断。
- 使用PuTTY，通过告警上报的IP地址登录Proxy节点。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 检查Proxy节点与远端vpp服务器之间的连通性。如果是IPv4，执行“ping 远端vpp服务器”，如果是IPv6，执行“ping6 远端vpp服务器”，查看是否可ping通。
- 是，执行2。
- 否，请联系机房管理员修复网络连通性，确保网络正常连接。
##### - 可能原因2：远端vpp服务器上hcp_vppd进程异常。
- 登录到远端vpp服务器。
- 执行“service hcp_vppd status”命令，查看界面回显是否为“ebackup_vpp_Proxy is running”。
- 是，请联系技术支持工程师协助解决。
- 否，执行2.b。
- 执行“service hcp_vppd restart”启动ebackup_vpp_Proxy进程，当回显显示“ebk_vpp_proxy start successfully”时，则表示重启成功。
##### 参考信息
无
********.86 0x201000C90021 清理复制任务的残留资源失败
##### 告警解释
清理复制任务（ID：[Task_id]）的残留资源失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000C90021 | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Task_id | 任务ID。 |
##### 对系统的影响
复制副本会占用复制存储空间。
##### 可能原因
- 目标端复制存储单元不可访问。
- 目标端复制存储的元数据损坏。
##### 处理步骤
##### - 可能原因1：存储单元不可访问。
- 在当前告警界面查看ID为 0x10E00E0000（连接存储单元失败） 或者0x10E00E0001（访问存储单元失败）的告警是否上报。
- 是，处理1.a所述的告警，保证存储单元可访问。
- 否，执行2。
##### - 可能原因2：目标端复制存储的元数据损坏。
请联系技术支持工程师协助解决。
##### 参考信息
无。
********.87 0x2010E01A001D 复制任务失败
##### 告警解释
复制任务（[TaskID]）失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x2010E01A001D | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| TaskID | 复制任务ID。 |
##### 对系统的影响
复制任务失败。
##### 可能原因
- 存储单元不可访问。
- 出现内部错误。
##### 处理步骤
##### - 可能原因1：存储单元不可访问。
- 在当前告警界面查看ID为0x10E00E0000（连接存储单元失败）或者0x10E00E0001（访问存储单元失败）的告警是否上报。
- 是，处理1.a所述的告警，保证存储单元可访问。
- 否，执行2。
##### - 可能原因2：出现内部错误。
请联系技术支持工程师协助解决。
##### 参考信息
无。
********.88 0x10E01A0014 卸载FusionStorage卷失败
##### 告警解释
Proxy（管理平面IP address：[IP_addr]）中，卸载卷(卷名：[Vol_name]，FusionStorageManager：[DSWare_IP])失败，显示错误信息（[errMsg]）。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01A0014 | 次要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| IP_addr | 备份代理管理平面地址。 |
| DSWare_IP | FusionStorageManager地址。 |
| Vol_name | 卷名称。 |
| errMsg | 错误信息。 |
##### 对系统的影响
造成备份或恢复任务失败。
##### 可能原因
Proxy与受保护环境所在服务器间物理连接中断。
##### 处理步骤
##### - 可能原因1：Proxy与受保护环境所在服务器间连接中断。
- 在当前告警界面查看ID为0x10E0140000（连接受保护环境失败）或0x10E0140001（扫描受保护环境失败）的告警是否上报。
- 是，请先处理1.a所述告警，保证Proxy与生产端网络连接正常。
- 否，执行1.b。
- 根据告警中的卷信息执行命令“/usr/bin/vbs_cli -c detachwithip -v VolumeName -i DSWareIP -p 0”手动卸载卷，其中VolumeName为告警详细信息中的卷名称，DSWareIP为FusionStorageManager的IP。查看回显是否为“ret_code=0”。
- 是，在告警界面手动清除告警。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
********.89 0x10E01A0015 删除FusionStorage卷失败
##### 告警解释
在Proxy（管理平面IP address：[IP_addr]）中，删除卷(卷名：[Vol_name]，FusionStorageManager：[DSWare_IP])失败，显示错误信息([errMsg])。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01A0015 | 次要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| IP_addr | 备份代理管理平面地址。 |
| DSWare_IP | FusionStorageManager地址。 |
| Vol_name | 卷名称。 |
| errMsg | 错误信息。 |
##### 对系统的影响
造成备份或恢复任务失败。
##### 可能原因
- Proxy与受保护环境所在服务器间物理连接中断。
- 要删除的卷没有进行卸载。
##### 处理步骤
##### - 可能原因1：备份代理与受保护环境所在服务器间连接中断。
- 在当前告警界面查看ID为0x10E0140000（连接受保护环境失败）或0x10E0140001（扫描受保护环境失败）的告警是否上报。
- 是，请先处理1.a所述告警，保证Proxy与受保护环境网络连接正常。
- 否，执行1.b。
- 根据告警中的卷信息执行命令“/opt/huawei-data-protection/ebackup/vbstool/vrmVBSTool.sh --op deleteVolume --dsaIp 127.0.0.1 --dswareFloatIP DSWareIP --volName VolumeName”手动删除卷，其中VolumeName为告警详细信息中的卷名称，DSWareIP为FusionStorageManager的IP。查看返回码是否为0。
- 是，处理结束。
- 否，执行2。
##### - 可能原因2：要删除的卷没有进行卸载。
- 根据告警中的卷信息执行命令“/usr/bin/vbs_cli -c detachwithip -v VolumeName -i DSWareIP -p 0”手动卸载卷，其中VolumeName为告警详细信息中的卷名称，DSWareIP为FusionStorageManager的IP。查看返回码是否为0。
- 是，执行2.b。
- 否，请联系技术支持工程师协助解决。
- 执行命令“/opt/huawei-data-protection/ebackup/vbstool/vrmVBSTool.sh --op deleteVolume --dsaIp 127.0.0.1 --dswareFloatIP DSWareIP --volName VolumeName”手动删除卷，其中VolumeName为告警详细信息中的卷名称，DSWareIP为FusionStorageManager的IP。查看返回码是否为0。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
********.90 0x10E01A0017 卸载OceanStor V3/V5卷或者Dorado V3卷失败
##### 告警解释
在Proxy（管理平面IP[IP_addr]）中，卸载卷(卷ID：[Vol_ID]，DeviceManager：[Device_IP]，LUN组名称：[Lungroup_name] )失败，显示错误（[errorDes]）。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01A0017 | 次要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| IP_addr | 备份代理管理平面地址。 |
| Vol_ID | 卷ID。 |
| Device_IP | DeviceManager管理IP。 |
| Lungroup_name | LUN组名称。 |
| errorDes | 错误信息。 |
##### 对系统的影响
造成备份或恢复任务失败。
##### 可能原因
备份代理与受保护环境所在服务器间物理连接中断。
##### 处理步骤
##### - 可能原因1：备份代理与受保护环境所在服务器间物理连接中断。
- 通过DeviceManager管理IP地址登录DeviceManager。
默认帐号：admin，默认密码：*****。
- 进入“资源分配 > LUN > LUN组”，根据LUN组名称，单击对应LUN组。
- 根据卷ID，搜索出对应的LUN，将它从LUN组内移除。
- 是，在告警界面手动清除告警。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
********.91 0x6300740001 重删数据有冗余
##### 告警解释
虽然系统已检测到前一次备份副本，但本次重删备份任务（ID：[Task_id]）仍对磁盘（ID：[Disk_id]）执行了全量备份而非增量备份，并产生了备份副本（ID：[Current_Snap_id]）。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x6300740001 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Task_id | 备份任务ID |
| Current_Snap_id | 当前备份副本ID |
| Disk_id | 磁盘ID |
##### 对系统的影响
不涉及。
##### 可能原因
磁盘所属受保护对象在生产端的前一个快照已被删除。
##### 处理步骤
##### - 可能原因1：磁盘所属受保护对象在生产端的前一个快照已被删除。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 在导航栏上选择“恢复”，进入恢复界面。
- 进入“恢复”界面，根据实际情况在导航中选择VMware、FusionSphere或存储设备，查看该受保护对象所属的受保护环境是否为FusionSphere。
- 是，1.d。
- 否，请联系技术支持工程师处理。
- 使用管理员用户登录FusionSphere受保护环境。
- 查看磁盘所属数据存储类型是否为FusionStorage。
- 是，1.f。
- 否，请联系技术支持工程师处理。
- 查看磁盘所属受保护对象的前一个快照是否存在。
- 是，收集并保存当前运行的任务及其状态，并联系技术支持工程师处理。
- 否，处理结束。
##### 参考信息
无
********.92 0x6000840003 访问ManageOne失败
##### 告警解释
访问ManageOne（IP：[IPAddress]）失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x6000840003 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| IPAddress | ManageOne的IP地址 |
##### 对系统的影响
无法使用ManageOne替换证书。
##### 可能原因
- 网络异常。
- 连接ManageOne的用户名或密码错误。
##### 处理步骤
##### - 可能原因1：网络异常。
- 使用PuTTY，以管理IP地址登录eBackup-Manager节点（Manager角色所在节点）或Server角色所在节点。
登录地址：
- eBackup-Manager节点的管理IP地址为“Workflow-PublicService-IP0”字段对应的IP地址。
- Server角色所在节点的管理IP地址为“datamover_externalom_iplist”字段对应的IP地址。
默认帐户：hcp，默认密码：*****
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行cat /opt/huawei-data-protection/ebackup/microservice/ebk_governance/conf/hcpconf.ini | grep ManageOneAddr命令，获取ManageOne的IP地址。
- 如果是IPv4，执行ping ManageOne的IP地址命令，如果是IPv6，执行ping6 ManageOne的IP地址命令，检查网络通信是否正常。
- 是，执行2。
- 否，执行1.e。
- 联系管理员修复网络问题。
- 等待15分钟左右，在当前告警界面查看该告警是否恢复。
- 是，处理结束。
- 否，执行2。
##### - 可能原因2：连接ManageOne的用户名或密码错误。
- 使用PuTTY，以管理IP地址登录eBackup-Manager节点（Manager角色所在节点）或Server角色所在节点。
登录地址：
- eBackup-Manager节点的管理IP地址为“Workflow-PublicService-IP0”字段对应的IP地址。
- Server角色所在节点的管理IP地址为“datamover_externalom_iplist”字段对应的IP地址。
默认帐户：hcp，默认密码：*****
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行cd /opt/huawei-data-protection/ebackup/microservice/ebk_governance/script命令，进入微服务脚本目录。
- 执行sh change_manage_one_info.sh命令，根据提示修改连接ManageOne的信息。
- 等待3分钟左右，在当前告警界面查看该告警是否恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
********.93 0x105800740001 备份代理存在进度长时间未更新任务
##### 告警解释
在备份代理（[Node_Ip]）上微服务（[Service_Name]）存在进度长时间未更新任务。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x105800740001 | 次要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Node_Ip | 备份代理IP地址。 |
| Service_Name | 微服务名称。 |
##### 对系统的影响
任务不能结束。
##### 可能原因
访问备份存储的I/O长时间悬挂。
##### 处理步骤
##### - 可能原因1：访问备份存储的I/O长时间悬挂。
- 获取告警中的备份代理IP地址和微服务名称信息。
- 微服务名称是否为Unknown。
- 是，联系技术支持工程师协助解决。
- 否，执行1.c。
- 根据备份代理IP地址登录后台操作系统。
- 执行ps -ef |grep "微服务名称" 命令获取微服务进程ID。
- 执行kill -9 微服务进程ID命令终止该微服务进程。
终止后，该微服务会被重新启动。
********.94 附录
< 上一节
********.94.1 登录eBackup服务器
登录eBackup-Manager或eBackup-Workflow节点
- 使用浏览器，登录Manager的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 在导航栏单击“监控 > 服务器”。
在“服务器”页面可看到eBackup-Manager和eBackup-Workflow的IP地址。
- eBackup-Manager的管理平面IP地址为：角色为“备份管理服务器（主）”对应的“备份管理平面IP地址”。
- eBackup-Workflow（eBackup-Manager的备节点）的管理平面IP地址为：角色为“备份管理服务器（备）”对应的“备份管理平面IP地址”。
- 其他eBackup-Workflow的管理平面IP地址为：角色为“备份流程服务器”对应的“备份管理平面IP地址”。
- 使用PuTTY，通过2中获取的管理平面IP地址登录eBackup-Manager或eBackup-Workflow。
默认帐户：hcp，默认密码：*****
登录Server或Proxy节点
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 在导航栏单击“监控 > 服务器”。
在“服务器”页面可看到Server和Proxy的IP地址。
- Server的管理平面IP地址为：角色为“备份服务器（主）”对应的“备份管理平面IP地址”。
- Proxy（Server的备节点）的管理平面IP地址为：角色为“备份服务器（备）”对应的“备份管理平面IP地址”。
- 其他Proxy的管理平面IP地址为：角色为“备份代理”对应的“备份管理平面IP地址”。
- 使用PuTTY，通过2中获取的管理平面IP地址登录Server或Proxy。
默认帐户：hcp，默认密码：*****
******** Karbor
********.1 1020799 创建云服务器复制副本失败
##### 告警解释
创建云服务器复制副本失败。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020799 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 租户ID | 产生告警的租户ID。 |
| 项目ID | 产生告警的项目ID。 |
| 云服务器ID | 复制失败的源副本对应的云服务器ID。 |
| 源副本ID | 复制失败的源副本ID。 |
| 租户名称 | 产生告警的租户名称。 |
| 项目名称 | 产生告警的项目名称。 |
| 节点IP | 产生告警的节点IP。 |
| 策略名称 | 绑定此云服务器的策略名称。 |
| 请求ID | 请求ID，可用于日志搜索。 |
| 错误码 | 故障对应的错误码。 |
##### 对系统的影响
创建复制副本失败，影响后续的恢复操作。
##### 可能原因
- 与eBackupWorkFlow连接不通。
- eBackup故障。
##### 处理步骤
##### - 可能原因：与eBackupWorkFlow连接不通。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行check_karbor_connect命令，查看“eBackupWorkFlow”对应行的“Check_Result”值是否为“OK”。
- 是，请执行2。
- 否，请执行1.d。
- 执行docker exec -ti karborapi bash -c "cat /etc/karbor/karbor.conf" | grep ebackup_lb_ip_address命令获取配置的eBackupWorkFlow的IP地址，在部署或扩容云服务HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索“workflow_management_float_ip”，查看二者是否一致。
- 是，请联系技术支持工程师协助解决。
- 否，执行set_ebackup_plugin --ebackup_url eBackupWorkFlow的IP地址命令重新配置eBackupWorkFlow的IP地址，默认密码为“*****”。再次执行check_karbor_connect，若“eBackupWorkFlow”对应行的“Check_Result”值不为“OK”，请联系技术支持工程师协助解决。
- 联系告警附加信息中“租户名称”所对应租户，通知其使用VDC管理员帐号登录ManageOne运营面，选择告警上报区域，切换到告警附加信息中“项目名称”对应的项目下，进入“云服务器备份”页面，对告警附加信息中“策略名称”对应策略立即重新执行复制。
##### - 可能原因：eBackup故障。
- 使用告警附加信息中“租户名称”所对应租户下的VDC管理员帐号登录ManageOne运营面，选择告警上报区域，切换到告警附加信息中“项目名称”对应的项目下，进入“云服务器备份”页面，获取对应任务或副本的失败原因。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 查看日志文件/var/log/huawei/dj/services/system/karbor/karbor-protection/karbor-protection.log，收集失败产生时刻的错误日志详情。
- 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.2 1020796 云服务器备份配额消耗到配额总量的阈值
##### 告警解释
云服务器备份配额消耗到配额总量的80%。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020796 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 租户ID | 产生告警的租户ID。 |
| 项目ID | 产生告警的项目ID。 |
| 配额类型 | 消耗到阈值的配额类型。backup_capacity代表备份配额，copy_capacity代表复制配额。 |
| 租户名称 | 产生告警的租户名称。 |
| 项目名称 | 产生告警的项目名称。 |
| 节点IP | 产生告警的节点IP。 |
| 总量 | 配额的总量。 |
| 已使用 | 配额的已使用量。 |
| 阈值 | 产生告警需要超过配额已使用/总量的百分比。 |
| 清除阈值 | 清除告警需要低于配额已使用/总量的百分比。 |
##### 对系统的影响
不及时扩容租户配额，可能导致手动备份、自动备份失败。
##### 可能原因
配额消耗到配额总量的阈值。
##### 处理步骤
##### - 可能原因：配额消耗到配额总量的阈值。
- 获取该条告警附加信息中“租户名称”，“项目名称”。
- 联系“租户名称”对应的租户，通知其使用VDC管理员帐号登录ManageOne运营面，选择告警上报区域，切换到“项目名称”对应的项目下。
- 进入“云服务器备份”页面，手动申请空间或者删除不必要的副本。申请空间的详细操作请参见申请备份空间。
##### 参考信息
无。
********.3 1020791 云服务器备份策略自动调度失败
##### 告警解释
云服务器备份策略自动调度失败。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020791 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 租户ID | 产生告警的租户ID。 |
| 项目ID | 产生告警的项目ID。 |
| 策略ID | 调度失败的策略ID。 |
| 租户名称 | 产生告警的租户名称。 |
| 项目名称 | 产生告警的项目名称。 |
| 节点IP | 产生告警的节点IP。 |
| 请求ID | 请求ID，可用于日志搜索。 |
| 错误码 | 故障对应的错误码。 |
| 策略名称 | 调度失败的策略名称。 |
##### 对系统的影响
备份未按照设定好的策略自动生成，影响后续的恢复操作。
##### 可能原因
- 组件状态异常。
- 对接nova的连接异常，无法获取虚拟机信息。
- 对接cinder的连接异常，无法获取卷信息。
- 备份配额不足。
##### 处理步骤
##### - 可能原因：组件状态异常。
- 检查是否存在告警“组件状态异常”。
- 是，根据告警修复建议修复组件异常。
- 否，请执行2。
- 联系告警附加信息中“租户名称”所对应租户，通知其使用VDC管理员帐号登录ManageOne运营面，选择告警上报区域，切换到告警附加信息中“项目名称”对应的项目下，进入“云服务器备份”页面，对告警附加信息中“策略名称”对应的策略手动执行备份。
##### - 可能原因：对接nova的连接异常，无法获取虚拟机信息。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行check_karbor_connect命令，查看“Nova”行对应的“Check_Result”值。
- 如果为“OK”，执行3。
- 如果为“SSLError”，请参见通过ManageOne界面方式单个或批量更换证书更新FusionSphere部件的“FusionSphere-API”证书。再次执行2.c。
- 如果为“Error”，执行set_karbor_endpoints --nova_endpoint nova的url地址命令设置nova的endpoint，可在HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索DMK_g_regions:fsp_Cascading.nova获取nova的url地址。再次执行2.c。
- 如果仍未修复与nova的连接异常，请联系技术支持工程师协助解决。
##### - 可能原因：对接cinder的连接异常，无法获取卷信息。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行check_karbor_connect命令，查看“Cinder”行对应的“Check_Result”值。
- 如果为“OK”，执行4。
- 如果为“SSLError”，请参见通过ManageOne界面方式单个或批量更换证书更新FusionSphere部件的“FusionSphere-API”证书。再次执行3.c。
- 如果提示endpoint错误，执行set_karbor_endpoints --cinder_endpoint cinder的url地址命令设置cinder的endpoint，可在HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索DMK_g_regions:fsp_Cascading.cinder获取cinder的url地址。再次执行3.c。
- 如果仍未修复与cinder的连接异常，请联系技术支持工程师协助解决。
##### - 可能原因：备份配额不足。
- 查看附加信息中“错误码”值是否为CSBS.9006。
- 是，联系附加信息中“租户名称”所对应租户，通知其使用VDC管理员帐号登录ManageOne运营面，选择告警上报区域，切换到附加信息中“项目名称”对应的项目下，进入“云服务器备份”页面，申请备份空间。申请空间的详细操作请参见申请备份空间。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
********.4 1020790 云服务器备份复制策略自动调度失败
##### 告警解释
云服务器备份复制策略自动调度失败。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020790 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 租户ID | 产生告警的租户ID。 |
| 项目ID | 产生告警的项目ID。 |
| 策略ID | 调度失败的策略ID。 |
| 租户名称 | 产生告警的租户名称。 |
| 项目名称 | 产生告警的项目名称。 |
| 节点IP | 产生告警的节点IP。 |
| 请求ID | 请求ID，可用于日志搜索。 |
| 错误码 | 故障对应的错误码。 |
| 策略名称 | 调度失败的策略名称。 |
##### 对系统的影响
复制副本未按照设定好的策略自动生成，影响后续的恢复操作。
##### 可能原因
- 组件状态异常。
- 对接nova的连接异常，无法获取虚拟机信息。
- 对接cinder的连接异常，无法获取卷信息。
- 复制配额不足。
##### 处理步骤
##### - 可能原因：组件状态异常。
- 检查是否存在告警“组件状态异常”。
- 是，根据告警修复建议修复组件异常。
- 否，请执行2。
- 联系告警附加信息中“租户名称”所对应租户，通知其使用VDC管理员帐号登录ManageOne运营面，选择告警上报区域，切换到告警附加信息中“项目名称”对应的项目下，进入“云服务器备份”页面，对告警附加信息中“策略名称”对应的策略手动执行复制。
##### - 可能原因：对接nova的连接异常，无法获取虚拟机信息。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行check_karbor_connect命令，查看“Nova”行对应的“Check_Result”值。
- 如果为“OK”，执行3。
- 如果为“SSLError”，请参见通过ManageOne界面方式单个或批量更换证书更新FusionSphere部件的“FusionSphere-API”证书。再次执行2.c。
- 如果为“Error”，执行set_karbor_endpoints --nova_endpoint nova的url地址命令设置nova的endpoint，可在HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索“DMK_g_regions:fsp_Cascading.nova”获取nova的url地址。再次执行2.c。
- 如果仍未修复与nova的连接异常，请联系技术支持工程师协助解决。
##### - 可能原因：对接cinder的连接异常，无法获取卷信息。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行check_karbor_connect命令，查看“Cinder”行对应的“Check_Result”值。
- 如果为“OK”，执行4。
- 如果为“SSLError”，请参见通过ManageOne界面方式单个或批量更换证书更新FusionSphere部件的“FusionSphere-API”证书。再次执行3.c。
- 如果提示endpoint错误，执行set_karbor_endpoints –cinder_endpoint cinder的url地址命令设置cinder的endpoint，可在HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索“DMK_g_regions:fsp_Cascading.cinder”获取cinder的url地址。再次执行3.c。
- 如果仍未修复与cinder的连接异常，请联系技术支持工程师协助解决。
##### - 可能原因：复制配额不足。
- 查看告警附加信息中“错误码”值是否为CSBS.9006。
- 是，联系告警“附加信息”中“租户名称”所对应租户，通知其使用VDC管理员帐号登录ManageOne运营面，选择告警上报区域，切换到附加信息中“项目名称”对应的项目下，进入“云服务器备份”页面，申请复制空间。申请空间的详细操作请参见申请备份空间。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
********.5 1020788 云服务器备份失败
##### 告警解释
云服务器备份失败。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020788 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 租户ID | 产生告警的租户ID。 |
| 项目ID | 产生告警的项目ID。 |
| 云服务器ID | 备份失败的云服务器ID。 |
| 租户名称 | 产生告警的租户名称。 |
| 项目名称 | 产生告警的项目名称。 |
| 节点IP | 产生告警的节点IP。 |
| 策略名称 | 绑定此云服务器的策略名称。 |
| 副本ID | 本次生成状态为“错误”的副本ID。 |
| 请求ID | 请求ID，可用于日志搜索。 |
| 错误码 | 故障对应的错误码。 |
##### 对系统的影响
备份未成功生成，影响后续的恢复操作。
##### 可能原因
- 与eBackupWorkFlow连接不通。
- eBackup故障。
##### 处理步骤
##### - 可能原因：与eBackupWorkFlow连接不通。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行check_karbor_connect命令，查看“eBackupWorkFlow”对应行的“Check_Result”值是否为“OK”。
- 是，请执行2。
- 否，请执行1.d。
- 执行docker exec -ti karborapi bash -c "cat /etc/karbor/karbor.conf" | grep ebackup_lb_ip_address命令获取配置的eBackupWorkFlow的IP地址，在部署或扩容云服务HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索“workflow_management_float_ip”，查看二者是否一致。
- 是，请联系技术支持工程师协助解决。
- 否，执行set_ebackup_plugin --ebackup_url eBackupWorkFlow的IP地址命令重新配置eBackupWorkFlow的IP地址，默认密码为“*****”。再次执行check_karbor_connect，若“eBackupWorkFlow”对应行的“Check_Result”值不为“OK”，请联系技术支持工程师协助解决。
- 联系告警附加信息中“租户名称”所对应租户，通知其使用VDC管理员帐号登录ManageOne运营面，切换到告警附加信息中“项目名称”对应的项目下，进入“云服务器备份”页面，对告警附加信息中“策略名称”对应策略立即重新执行备份。
##### - 可能原因：eBackup故障。
- 使用告警附加信息中“租户名称”所对应租户下的VDC管理员帐号登录ManageOne运营面，选择告警上报区域，切换到告警附加信息中“项目名称”对应的项目下，进入“云服务器备份”页面，获取对应任务或副本的失败原因。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 查看日志文件/var/log/huawei/dj/services/system/karbor/karbor-protection/karbor-protection.log，收集失败产生时刻的错误日志详情。
- 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.6 1020783 启动消息队列服务失败
##### 告警解释
启动消息队列服务失败。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020783 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 节点IP | 产生告警的节点IP。 |
##### 对系统的影响
消息队列服务启动失败，可能无法正常提供服务。
##### 可能原因
网络故障。
##### 处理步骤
##### - 可能原因：网络故障。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行如下操作查询rabbitmq组件状态。
show_service --service rabbitmq
查看回显信息中rabbitmq组件状态是否为“fault”。
- 是，请执行1.d。
- 否，请手工清除该告警，处理结束。
- 执行如下操作停止rabbitmq组件。
stop_service --service rabbitmq
查看回显信息中，执行结果是否为“Successfully”。
- 是，请执行1.e。
- 否，请执行1.g。
- 执行如下操作重新启动rabbitmq组件。
start_service --service rabbitmq
查看回显信息中，执行结果是否为“Successfully”。
- 是，请执行1.f。
- 否，请执行1.g。
- 执行如下操作重新查询rabbitmq组件状态。
show_service --node rabbitmq
查看回显信息中rabbitmq组件状态是否为“fault”。
- 是，请执行1.g。
- 否，请手工清除该告警，处理结束。
- 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.7 1020782 消息队列存在消息响应超时
##### 告警解释
消息队列存在消息响应超时。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020782 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 节点IP | 产生告警的节点IP。 |
##### 对系统的影响
消息响应超时，可能导致备份等业务无法正常执行。
##### 可能原因
消息队列服务异常。
##### 处理步骤
##### - 可能原因：消息队列服务异常。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行如下操作查询rabbitmq组件状态。
show_service --service rabbitmq
查看回显信息中rabbitmq组件状态是否为“fault”。
- 是，请执行1.d。
- 否，请执行1.g。
- 执行如下操作停止rabbitmq组件。
stop_service --service rabbitmq
查看回显信息中，执行结果是否为“Successfully”。
- 是，请执行1.e。
- 否，请执行1.g。
- 执行如下操作重新启动rabbitmq组件。
start_service --service rabbitmq
查看回显信息中，执行结果是否为“Successfully”。
- 是，请执行1.f。
- 否，请执行1.g。
- 执行如下操作重新查询rabbitmq组件状态。
show_service --node rabbitmq
查看回显信息中rabbitmq组件状态是否为“fault”。
- 是，请执行1.g。
- 否，请手工清除该告警，处理结束。
- 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.8 1020779 创建云硬盘复制副本失败
##### 告警解释
创建云硬盘复制副本失败。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020779 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 租户ID | 产生告警的租户ID。 |
| 项目ID | 产生告警的项目ID。 |
| 云硬盘ID | 备份失败的云硬盘ID。 |
| 源副本ID | 复制失败的源副本ID。 |
| 租户名称 | 产生告警的租户名称。 |
| 项目名称 | 产生告警的项目名称。 |
| 节点IP | 产生告警的节点IP。 |
| 策略名称 | 绑定此云硬盘的策略名称。 |
| 请求ID | 请求ID，可用于日志搜索。 |
| 错误码 | 故障对应的错误码。 |
##### 对系统的影响
创建复制副本失败，影响后续的恢复操作。
##### 可能原因
- 与cinder连接不通。
- eBackup故障。
##### 处理步骤
##### - 可能原因：与cinder连接不通。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行check_karbor_connect命令，查看“Cinder”对应行的“Check_Result”值是否为“OK”。
- 是，请执行2。
- 否，请执行1.d。
- 执行命令docker exec –ti karborapi bash –c "cat /etc/karbor/karbor.conf" | grep cinder_endpoint获取cinder的url地址，在HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索“DMK_g_regions:fsp_Cascading.cinder”查看其参数值，对比二者是否一致。
- 否，执行set_karbor_endpoints --cinder_endpoint cinder的url地址命令重新设置cinder的endpoint。再次执行check_karbor_connect，若“Cinder”对应行的“Check_Result”值不为“OK”，请联系技术支持工程师协助解决。
- 是，请联系技术支持工程师协助解决。
- 联系附加信息中“租户名称”所对应租户，通知其使用VDC管理员帐号登录ManageOne运营面，选择告警上报区域，切换到附加信息中“项目名称”对应的项目下，进入“云硬盘备份”页面，对附加信息中的 “策略名称”对应的策略立即重新执行复制。
##### - 可能原因：eBackup故障。
- 使用附加信息中“租户名称”所对应租户下的VDC管理员帐号登录ManageOne运营面，选择告警上报区域，切换到附加信息中“项目名称”对应的项目下，进入“云硬盘备份”页面，获取对应任务或副本的失败原因。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 查看日志文件/var/log/huawei/dj/services/system/karbor/karbor-protection/karbor-protection.log，收集失败产生时刻的错误日志详情。
- 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.9 1020776 云硬盘备份配额消耗到配额总量的阈值
##### 告警解释
云硬盘备份配额消耗到配额总量的阈值。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020776 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 租户ID | 产生告警的租户ID。 |
| 项目ID | 产生告警的项目ID。 |
| 配额类型 | 消耗到阈值的配额类型。volume_backup_capacity代表备份配额，volume_copy_capacity代表复制配额。 |
| 租户名称 | 产生告警的租户名称。 |
| 项目名称 | 产生告警的项目名称。 |
| 节点IP | 产生告警的节点IP。 |
| 总量 | 配额的总量。 |
| 已使用 | 配额的已使用量。 |
| 阈值 | 产生告警需要超过配额已使用/总量的百分比。 |
| 清除阈值 | 清除告警需要低于配额已使用/总量的百分比。 |
##### 对系统的影响
不及时扩容租户配额，可能导致手动备份、自动备份失败。
##### 可能原因
配额消耗到配额总量的阈值。
##### 处理步骤
##### - 可能原因：配额消耗到配额总量的阈值。
- 获取该条告警附加信息中“租户名称”，“项目名称”。
- 联系“租户名称”对应的租户，通知其使用VDC管理员帐号登录ManageOne运营面，选择告警上报区域，切换到“项目名称”对应的项目下。
- 进入“云硬盘备份”页面，手动申请空间或者删除不必要的副本。申请空间的详细操作请参见申请备份空间。
##### 参考信息
无。
********.10 1020771 云硬盘备份策略自动调度失败
##### 告警解释
云硬盘备份策略自动调度失败。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020771 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 租户ID | 产生告警的租户ID。 |
| 项目ID | 产生告警的项目ID。 |
| 策略ID | 调度失败的策略ID。 |
| 租户名称 | 产生告警的租户名称。 |
| 项目名称 | 产生告警的项目名称。 |
| 节点IP | 产生告警的节点IP。 |
| 请求ID | 请求ID，可用于日志搜索。 |
| 错误码 | 故障对应的错误码。 |
| 策略名称 | 调度失败的策略名称。 |
##### 对系统的影响
备份未按照设定好的策略自动生成，影响后续的恢复操作。
##### 可能原因
- 组件状态异常。
- 对接cinder的连接异常，无法获取卷信息。
- 备份配额不足。
##### 处理步骤
##### - 可能原因：组件状态异常。
- 检查是否存在告警“组件状态异常”。
- 是，根据告警修复建议修复组件异常。
- 否，请执行2。
- 联系告警附加信息中“租户名称”所对应租户，通知其使用VDC管理员帐号登录ManageOne运营面，选择告警上报区域，切换到告警附加信息中“项目名称”对应的项目下，进入“云硬盘备份”页面，对告警附加信息中“策略名称”对应的策略手动执行备份。
##### - 可能原因：对接cinder的连接异常，无法获取卷信息。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行check_karbor_connect命令，查看“Cinder”行对应的“Check_Result”值。
- 如果为“OK”，执行3。
- 如果为“SSLError”，请参见通过ManageOne界面方式单个或批量更换证书更新FusionSphere部件的“FusionSphere-API”证书。再次执行2.c。
- 如果提示“Error”，执行set_karbor_endpoints –cinder_endpoint cinder的url地址命令设置cinder的endpoint，可在HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索“DMK_g_regions:fsp_Cascading.cinder”获取cinder的url地址。再次执行2.c。
- 如果仍未修复与cinder的连接异常，请联系技术支持工程师协助解决。
##### - 可能原因：备份配额不足。
- 查看告警附加信息中“错误码”值是否为CSBS.9006。
- 是，联系告警附加信息中“租户名称”所对应租户，通知其使用VDC管理员帐号登录ManageOne运营面，选择告警上报区域，切换到告警附加信息中“项目名称”对应的项目下，进入“云硬盘备份”页面，申请备份空间。申请空间的详细操作请参见申请备份空间。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
********.11 1020770 云硬盘复制策略自动调度失败
##### 告警解释
云硬盘复制策略自动调度失败。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020770 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 租户ID | 产生告警的租户ID。 |
| 项目ID | 产生告警的项目ID。 |
| 策略ID | 调度失败的策略ID。 |
| 租户名称 | 产生告警的租户名称。 |
| 项目名称 | 产生告警的项目名称。 |
| 节点IP | 产生告警的节点IP。 |
| 请求ID | 请求ID，可用于日志搜索。 |
| 错误码 | 故障对应的错误码。 |
| 策略名称 | 调度失败的策略名称。 |
##### 对系统的影响
复制副本未按照设定好的策略自动生成，影响后续的恢复操作。
##### 可能原因
- 组件状态异常。
- 对接cinder的连接异常，无法获取卷信息。
- 复制配额不足。
##### 处理步骤
##### - 可能原因：组件状态异常。
- 检查是否存在告警“组件状态异常”。
- 是，根据告警修复建议修复组件异常。
- 否，请执行2。
- 联系告警附加信息中“租户名称”所对应租户，通知其使用VDC管理员帐号登录ManageOne运营面，选择告警上报区域，切换到告警附加信息中“项目名称”对应的项目下，进入“云硬盘备份”页面，对告警附加信息中“策略名称”对应的策略手动执行复制。
##### - 可能原因：对接cinder的连接异常，无法获取卷信息。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行check_karbor_connect命令，查看“Cinder”行对应的“Check_Result”值。
- 如果为“OK”，执行3。
- 如果为“SSLError”，请参见通过ManageOne界面方式单个或批量更换证书更新FusionSphere部件的“FusionSphere-API”证书。再次执行2.c。
- 如果提示“Error”，执行set_karbor_endpoints --cinder_endpoint cinder的url地址命令设置cinder的endpoint，可在HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索“DMK_g_regions:fsp_Cascading.cinder”获取cinder的url地址。再次执行2.c。
- 如果仍未修复与cinder的连接异常，请联系技术支持工程师协助解决。
##### - 可能原因：复制配额不足。
- 查看告警附加信息中“错误码”值是否为CSBS.9006。
- 是，联系告警附加信息中“租户名称”所对应租户，通知其使用VDC管理员帐号登录ManageOne运营面，选择告警上报区域，切换到告警附加信息中“项目名称”对应的项目下，进入“云硬盘备份”页面，申请复制空间。申请空间的详细操作请参见申请备份空间。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
********.12 1020768 云硬盘备份失败
##### 告警解释
云硬盘备份失败。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020768 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 租户ID | 产生告警的租户ID。 |
| 项目ID | 产生告警的项目ID。 |
| 云硬盘ID | 备份失败的云硬盘ID。 |
| 租户名称 | 产生告警的租户名称。 |
| 项目名称 | 产生告警的项目名称。 |
| 节点IP | 产生告警的节点IP。 |
| 策略名称 | 绑定此云硬盘的策略名称。 |
| 副本ID | 本次生成状态为“错误”的副本ID。 |
| 请求ID | 请求ID，可用于日志搜索。 |
| 错误码 | 故障对应的错误码。 |
##### 对系统的影响
备份未成功生成，影响后续的恢复操作。
##### 可能原因
- 与cinder连接不通。
- eBackup故障。
##### 处理步骤
##### - 可能原因：与cinder连接不通。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行check_karbor_connect命令，查看“Cinder”对应行的“Check_Result”值是否为“OK”。
- 是，请执行2。
- 否，请执行1.d。
- 执行命令docker exec –ti karborapi bash –c "cat /etc/karbor/karbor.conf" | grep cinder_endpoint获取cinder的url地址，在HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索“DMK_g_regions:fsp_Cascading.cinder”查看其参数值，对比二者是否一致。
- 否，执行set_karbor_endpoints --cinder_endpoint cinder的url地址命令重新设置cinder的endpoint。再次执行check_karbor_connect，若“Cinder”对应行的“Check_Result”值不为“OK”，请联系技术支持工程师协助解决。
- 是，请联系技术支持工程师协助解决。
- 联系告警附加信息中“租户名称”所对应租户，通知其使用VDC管理员帐号登录ManageOne运营面，选择告警上报区域，切换到告警附加信息中“项目名称”对应的项目下，进入“云硬盘备份”页面，对告警附加信息中的 “策略名称”对应的策略立即重新执行备份。
##### - 可能原因：eBackup故障。
- 使用告警附加信息中“租户名称”所对应租户下的VDC管理员帐号登录ManageOne运营面，选择告警上报区域，切换到告警附加信息中“项目名称”对应的项目下，进入“云硬盘备份”页面，获取对应任务或副本的失败原因。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 查看日志文件/var/log/huawei/dj/services/system/karbor/karbor-protection/karbor-protection.log，收集失败产生时刻的错误日志详情。
- 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.13 1020762 FSP证书校验失败
##### 告警解释
FSP证书校验失败。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020762 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 证书全路径 | 校验失败的证书全路径。 |
| 节点IP | 产生告警的节点IP。 |
##### 对系统的影响
和FSP连接时不校验证书。
##### 可能原因
FSP证书过期、被吊销或者未被可信CA签发。
##### 处理步骤
##### - 可能原因：FSP证书过期、被吊销或者未被可信CA签发。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行以下命令检查FSP证书是否合法。
python -c 'import requests;requests.get("nova_endpoint", verify="/opt/huawei/dj/DJSecurity/server-cert/karbor/openstack/nova_ca.crt")'
其中nova_endpoint可通过执行docker exec -ti karborapi bash -c "cat /etc/karbor/karbor.conf" | grep nova_endpoint命令获取。
- 查看命令的输出信息，是否有证书校验相关的报错信息。
- 是，请根据报错信息进行处理。请参见通过ManageOne界面方式单个或批量更换证书更新FusionSphere部件的“FusionSphere-API”证书。
- 否，请执行1.e。
- 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.14 1020761 IAM证书校验失败
##### 告警解释
IAM证书校验失败
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020761 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 证书全路径 | 校验失败的证书全路径。 |
| 节点IP | 产生告警的节点IP。 |
##### 对系统的影响
和IAM连接时不校验证书。
##### 可能原因
IAM证书过期、被吊销或者未被可信CA签发。
##### 处理步骤
##### - 可能原因：IAM证书过期、被吊销或者未被可信CA签发。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行以下命令检查IAM证书是否合法。
python -c 'import requests;requests.get("auth_url", verify="/opt/huawei/dj/DJSecurity/server-ca/trust.cer")'
其中auth_url可通过执行docker exec -ti karborapi bash -c "cat /etc/karbor/karbor.conf" | grep auth_url命令获取。
- 查看命令的输出信息，是否有证书校验相关的报错信息。
- 是，请根据报错信息进行处理。请参考通过ManageOne界面方式单个或批量更换证书更新ManageOne部件的“ManageOne-ProductER”证书。
- 否，请执行1.e。
- 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.15 1023299 节点状态异常
##### 告警解释
系统周期性检查所有的节点状态，如果存在状态不正常的节点，产生此告警。当该节点状态变成正常后，告警恢复。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1023299 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 节点IP | 产生告警的节点IP。 |
##### 对系统的影响
该节点不能提供服务。
##### 可能原因
- 虚拟机掉电。
- 节点网络故障。
##### 处理步骤
##### - 可能原因：虚拟机掉电。
- 使用admin帐号登录ManageOne运维面。
- 在“集中告警”页面，查看当前告警的“区域”。
- 回到主页，进入“ServiceOM > 对应区域 > 弹性云服务器 > 计算实例”。按名称搜索Service-CSBS对应的虚拟机，查看其状态、电源状态是否均为“运行中”。
- 否，请执行1.d。
- 是，请执行2。
- 单击“重启”以重启虚拟机。
等待节点系统启动后，再等待若干分钟，查看告警是否恢复。
- 是，处理结束。
- 否，请执行2。
##### - 可能原因：节点网络故障。
- 查看是否存在“备份服务节点间网络异常”告警。
- 是，根据告警修复建议修复网络故障。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
********.16 1023298 组件状态异常
##### 告警解释
系统周期性检查每个节点上的组件状态，如果该节点存在状态不正常的组件，产生此告警；当该组件状态变成正常后，告警恢复。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1023298 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 组件 | 异常的组件。 |
| 节点IP | 产生告警的节点IP。 |
##### 对系统的影响
如果节点上存在状态不正常的组件，则该节点不能正常的承载业务。
##### 可能原因
组件进程异常退出。
##### 处理步骤
##### - 可能原因：组件进程异常退出。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行如下操作查询告警节点的组件状态。
show_service --node 节点名
查看回显信息中是否存在状态为“fault”的组件。
- 是，请执行1.d。
- 否，请执行1.h。
- 执行如下操作停止故障组件。
stop_service --service 组件名
查看回显信息中，执行结果是否为“Successfully”。
- 是，请执行1.e。
- 否，请执行1.h。
- 执行如下操作重新启动故障组件。
start_service --service 组件名
查看回显信息中，执行结果是否为“Successfully”。
- 是，请执行1.f。
- 否，请执行1.h。
- 执行如下操作重新查询告警节点的组件状态。
show_service --node 节点名
查看回显信息中是否存在状态为“fault”的组件。
- 是，请执行1.d。
- 否，请执行1.g。
- 等待一分钟，查看告警是否恢复。
- 是，处理结束。
- 否，请执行1.h。
- 联系技术支持工程师协助解决。
##### 参考信息
无。
********.17 1023296 外部NTP时钟同步异常
##### 告警解释
当系统与外部NTP服务器时钟同步异常时，上报此告警。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1023296 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| NTP服务器IP | 同步异常的NTP服务器IP。 |
| 节点IP | 产生告警的节点IP。 |
##### 对系统的影响
系统不再与外部时钟源进行时间同步。
##### 可能原因
- 系统与外部NTP服务器网络通信异常。
- 外部NTP服务器运行异常。
##### 处理步骤
##### - 可能原因：系统与外部NTP服务器网络通信异常。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行ntpq -p命令获取外部NTP服务器的IP地址列表。
- 执行ping 外部NTP服务器的IP地址（ipv6场景下使用ping6）命令，检查与外部NTP服务器之间的网络通信是否正常。
- 是，请执行2。
- 否，请执行1.e。
- 联系网络管理员进行网络故障的排除，等待NTP自动同步，完成后检查该告警是否清除。
- 是，处理结束。
- 否，请执行2。
##### - 可能原因：外部NTP服务器运行异常。
- 通过SSH登录NTP服务器节点，华为云Stack的NTP服务器默认帐号：untp，默认密码：*****。
- 执行sudo su命令切换到root，默认密码：*****。
- 执行systemctl status ntpd.service命令查看NTP服务是否正常，当回显中有“active(running)”则表示NTP服务正常。
- 是，请联系技术支持工程师协助解决。
- 否，执行systemctl start ntpd.service命令重启NTP服务。
- 等待10分钟，检查告警是否清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
********.18 1023295 备份系统数据发生失败
##### 告警解释
备份系统数据发生失败。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1023295 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 节点IP | 产生告警的节点IP。 |
##### 对系统的影响
系统数据无法完成备份，影响系统的可靠性。
##### 可能原因
- 备份目录所在磁盘空间不足。
- 系统与外部备份服务器网络通信异常。
##### 处理步骤
262. 查看告警附加信息中的“节点IP”参数，确定发生故障的节点。
##### - 可能原因：备份目录所在磁盘空间不足。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行cd /opt/djbackup/db命令进入管理数据备份目录，删除目录下无用的备份数据。
##### - 可能原因：系统与外部备份服务器网络通信异常。
- 检查系统与外部备份服务器的物理网络连接是否正确。
- 是，请执行3.b。
- 否，请联系技术支持工程师协助解决。
- 检查外部备份服务器ftps服务是否正常：如果是ftp类型的服务器，使用curl -u {ftp_username}:{ftp_password} -k ftp://{ftp_ip}:{ftp_port} --noproxy {ftp_ip}查看是否能够获取到数据；如果是ftps类型的服务器，使用curl -u {ftp_username}:{decrypt_pwd} --ftp-ssl-reqd -k ftps://{ftp_ip}:{ftp_port}/ --noproxy {ftp_ip}。
- 是，请联系技术支持工程师协助解决。
- 否，恢复外部备份服务器ftps服务，完成后查看告警是否恢复。若仍未恢复，请联系技术支持工程师协助解决。
##### 参考信息
无。
********.19 1023282 FTP服务器证书校验失败
##### 告警解释
FTP服务器证书校验失败时，上报此告警。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1023282 | 次要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 节点IP | 产生告警的节点IP。 |
##### 对系统的影响
连接FTP服务器时会使用不安全的连接。
##### 可能原因
证书过期、被吊销或者未被可信CA签发。
##### 处理步骤
##### - 可能原因：证书过期、被吊销或者未被可信CA签发。
- 请参考导入FTPS服务器的CA证书更换正确的FTP服务器证书，然后再次确认告警是否被清除。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
********.20 1023279 系统证书即将过期
##### 告警解释
系统证书文件即将过期。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1023279 | 重要->紧急 | 是 |
- 证书使用期限小于7天，告警级别为紧急。
- 证书使用期限小于30天，告警级别为重要。
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 证书全路径 | 即将过期的证书全路径。 |
| 节点IP | 产生告警的节点IP。 |
| 过期时间 | 证书过期时间。 |
##### 对系统的影响
如果系统证书过期，证书将不受信任，可能影响系统功能。
##### 可能原因
证书距离过期时间小于阈值。
##### 处理步骤
##### - 可能原因：证书距离过期时间小于阈值。
- 查看告警定位信息中的“证书全路径”。
- 根据证书全路径，重新导入对应的新证书。重新导入证书的操作请参见通过ManageOne界面方式单个或批量更换证书更新CSBS_VBS部件的“CSBS_VBS-internal”证书。
- 一天后检查告警是否恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
********.21 1023278 系统证书已经过期
##### 告警解释
系统证书文件已经过期。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1023278 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 证书全路径 | 已过期的证书全路径。 |
| 节点IP | 产生告警的节点IP。 |
##### 对系统的影响
证书不受信任，可能影响系统功能。
##### 可能原因
证书已经过期。
##### 处理步骤
##### - 可能原因：证书已经过期。
- 查看告警定位信息中的“证书全路径”。
- 根据证书全路径，重新导入对应的新证书。重新导入证书的操作请参见通过ManageOne界面方式单个或批量更换证书更新CSBS_VBS部件的“CSBS_VBS-internal”证书。
- 一天后检查告警是否恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
********.22 1023277 消息队列卡死
##### 告警解释
部分或全部消息队列卡死，服务无法正常生产和消费消息。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1023277 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 节点IP | 产生告警的节点IP。 |
##### 对系统的影响
可能导致依赖rabbitmq的服务业务异常。
##### 可能原因
rabbitmq长时间运行有概率导致卡死。
##### 处理步骤
##### - 可能原因：rabbitmq长时间运行有概率导致卡死。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行命令：stop_service --service rabbitmq停止所有节点rabbitmq。
- 执行命令：start_service --service rabbitmq启动所有节点rabbitmq。操作完成后，如果告警未清除，请执行1.e。
- 请收集日志目录“/var/log/huawei/dj/services/system/rabbitmq”，然后请联系技术支持工程师协助解决。
##### 参考信息
无。
********.23 1023276 消息队列产生网络分区
##### 告警解释
网络故障导致rabbitmq节点间无法相互访问，导致数据不一致，功能异常。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1023276 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名称 | 产生告警的节点名。 |
| 节点IP | 产生告警的节点IP。 |
##### 对系统的影响
可能导致依赖rabbitmq的服务业务异常。
##### 可能原因
内部面网络故障，rabbitmq节点无法相互访问。
##### 处理步骤
##### - 可能原因：网络故障，rabbitmq节点无法相互访问。
- 查看是否存在“备份服务节点间网络异常”告警。
- 是，根据告警修复建议修复网络故障。
- 否，请执行1.b。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行命令：stop_service --service rabbitmq停止所有节点rabbitmq。
- 执行命令：start_service --service rabbitmq启动所有节点rabbitmq。操作完成后，如果告警未清除，请执行1.f。
- 请收集日志目录“/var/log/huawei/dj/services/system/rabbitmq”，然后联系技术支持工程师协助解决。
##### 参考信息
无。
********.24 1023099 CPU使用率超过阈值
##### 告警解释
当CPU占用率超过指定阈值时，上报此告警。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1023099 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 节点IP | 产生告警的节点IP。 |
| 已使用 | CPU已使用率。 |
| 阈值 | 产生告警需要超过的CPU已使用率。 |
| 清除阈值 | 清除告警需要低于的CPU已使用率。 |
##### 对系统的影响
可能会造成系统运行速度慢。
##### 可能原因
主机业务繁忙负载过重。
##### 处理步骤
##### - 可能原因：主机业务繁忙负载过重。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行top命令，查看CPU占用率过高的进程，并记录其PID。
- 执行kill PID命令强制结束进程。
- 等待若干分钟后，再次执行top命令，查看CPU占用率是否明显下降。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
********.25 1023098 内存使用率超过阈值
##### 告警解释
当内存占用率超过指定阈值时，上报此告警。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1023098 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 节点IP | 产生告警的节点IP。 |
| 总量 | 内存总量。 |
| 已使用 | 内存已使用量。 |
| 阈值 | 产生告警需要超过内存已使用/总量的百分比。 |
| 清除阈值 | 清除告警需要低于内存已使用/总量的百分比。 |
##### 对系统的影响
可能会造成系统运行速度慢。
##### 可能原因
主机业务繁忙负载过重。
##### 处理步骤
##### - 可能原因：主机业务繁忙负载过重。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行top命令，查看内存占用率过高的进程，并记录其PID。
- 执行kill PID命令强制结束进程。
- 等待若干分钟后，再次执行top命令，查看内存占用率是否明显下降。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
********.26 1023097 磁盘使用率超过阈值
##### 告警解释
当磁盘空间占用率超过指定阈值时，上报此告警。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1023097 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 分区名 | 使用率超过阈值的磁盘分区名。 |
| 节点IP | 产生告警的节点IP。 |
| 总量 | 磁盘分区总量。 |
| 已使用 | 磁盘分区已使用量。 |
| 阈值 | 产生告警需要超过磁盘分区已使用/总量的百分比。 |
| 清除阈值 | 清除告警需要低于磁盘分区已使用/总量的百分比。 |
##### 对系统的影响
可能会造成系统的性能下降，并且无法存储系统新产生的数据。
##### 可能原因
磁盘上存储的文件占用空间过大。
##### 处理步骤
##### - 可能原因：磁盘上存储的文件占用空间过大。
- 查看告警信息，确定有哪些分区的使用率过大。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行以下命令，进入使用率过大的分区。
cd 使用率过大的分区路径
- 执行以下命令，清理无用文件。
rm -rf 无用文件名字
- 执行以下命令，查看分区使用率。
df -h
显示每个分区的空间使用情况，查看第6列（Mounted on），分区目录对应的第5列（Use%），已用磁盘空间是否超过告警阈值。
- 是，请执行1.e。
- 否，请执行1.g。
- 根据实际情况清理分区，再次执行df -h查看是否还有分区的使用率超过告警阈值。
- 是，请执行1.d。
- 否，请执行1.h。
- 等待1分钟，检查告警是否自动恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
********.27 1020800 执行复制策略失败
##### 告警解释
执行复制策略失败。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020800 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 租户ID | 产生告警的租户ID。 |
| 项目ID | 产生告警的项目ID。 |
| 策略ID | 执行失败的策略ID。 |
| 租户名称 | 产生告警的租户名称。 |
| 项目名称 | 产生告警的项目名称。 |
| 节点IP | 产生告警的节点IP。 |
| 请求ID | 请求ID，可用于日志搜索。 |
| 错误码 | 故障对应的错误码。 |
| 策略名称 | 执行失败的策略名称。 |
| 目标区域名称 | 复制的目标区域名称。 |
| 目标项目名称 | 复制的目标项目名称。 |
##### 对系统的影响
复制未成功，无法利用复制副本在指定区域发放云服务器。
##### 可能原因
- 与eBackupWorkFlow连接不通。
- eBackup故障。
##### 处理步骤
##### - 可能原因：与eBackupWorkFlow连接不通。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行check_karbor_connect命令，查看“eBackupWorkFlow”对应行的“Check_Result”值是否为“OK”。
- 是，请执行2。
- 否，请执行1.d。
- 执行docker exec -ti karborapi bash -c "cat /etc/karbor/karbor.conf" | grep ebackup_lb_ip_address命令获取配置的eBackupWorkFlow的IP地址，在部署或扩容云服务HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索“workflow_management_float_ip”，查看二者是否一致。
- 是，请联系技术支持工程师协助解决。
- 否，执行set_ebackup_plugin --ebackup_url eBackupWorkFlow的IP地址命令重新配置eBackupWorkFlow的IP地址，默认密码为“*****”。再次执行check_karbor_connect，若“eBackupWorkFlow”对应行的“Check_Result”值不为“OK”，请联系技术支持工程师协助解决。
- 联系告警附加信息中“租户名称”所对应租户，通知其使用VDC管理员帐号登录ManageOne运营面，选择告警上报区域，切换到告警附加信息中“项目名称”对应的项目下，进入“云服务器备份”页面，对告警附加信息中“策略名称”对应策略立即重新执行复制。
##### - 可能原因：eBackup故障。
- 使用告警附加信息中“租户名称”所对应租户下的VDC管理员帐号登录ManageOne运营面，选择告警上报区域，切换到告警附加信息中“项目名称”对应的项目下，进入“云服务器备份”页面，获取对应任务或副本的失败原因。
- 使用PuTTY，“CSBS_Service”字段对应的IP地址登录每一个Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐户的密码，切换至root帐户。
root帐户的默认密码为*****。
- 查看日志文件/var/log/huawei/dj/services/system/karbor/karbor-protection/karbor-protection.log，收集失败产生时刻的错误日志详情。
- 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.28 1020803 跨区域复制策略自动调度失败
##### 告警解释
跨区域复制策略自动调度失败。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020803 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 租户ID | 产生告警的租户ID。 |
| 项目ID | 产生告警的项目ID。 |
| 策略ID | 执行失败的策略ID。 |
| 目标区域ID | 复制的目标区域ID。 |
| 目标项目ID | 复制的目标项目ID。 |
| 租户名称 | 产生告警的租户名称。 |
| 项目名称 | 产生告警的项目名称。 |
| 节点IP | 产生告警的节点IP。 |
| 请求ID | 请求ID，可用于日志搜索。 |
| 错误码 | 故障对应的错误码。 |
| 策略名称 | 自动调度失败的策略名称。 |
| 目标区域名称 | 复制的目标区域名称。 |
| 目标项目名称 | 复制的目标项目名称。 |
##### 对系统的影响
跨region复制副本生成失败，影响后续跨region复制副本的创建镜像操作。
##### 可能原因
- 组件状态异常。
- 对接nova的连接异常，无法获取虚拟机信息。
- 对接cinder的连接异常，无法获取卷信息。
- 复制配额不足。
##### 处理步骤
##### - 可能原因：组件状态异常。
- 检查是否存在告警“组件状态异常”。
- 是，根据告警修复建议修复组件异常。
- 否，请执行2。
- 联系告警附加信息中“租户名称”所对应租户，通知其使用VDC管理员帐号登录ManageOne运营面，选择告警上报区域，切换到告警附加信息中“项目名称”对应的项目下，进入“云服务器备份”页面，对告警附加信息中“策略名称”对应的策略手动执行复制。
##### - 可能原因：对接nova的连接异常，无法获取虚拟机信息。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行check_karbor_connect命令，查看“Nova”行对应的“Check_Result”值。
- 如果为“OK”，执行3。
- 如果为“SSLError”，请参见通过ManageOne界面方式单个或批量更换证书更新FusionSphere部件的“FusionSphere-API”证书。再次执行2.c。
- 如果为“Error”，执行set_karbor_endpoints --nova_endpoint nova的url地址命令设置nova的endpoint，可在HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索DMK_g_regions:fsp_Cascading.nova获取nova的url地址。再次执行2.c。
- 如果仍未修复与nova的连接异常，请联系技术支持工程师协助解决。
##### - 可能原因：对接cinder的连接异常，无法获取卷信息。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行check_karbor_connect命令，查看“Cinder”行对应的“Check_Result”值。
- 如果为“OK”，执行4。
- 如果为“SSLError”，请参见通过ManageOne界面方式单个或批量更换证书更新FusionSphere部件的“FusionSphere-API”证书。再次执行3.c。
- 如果提示endpoint错误，执行set_karbor_endpoints --cinder_endpoint cinder的url地址命令设置cinder的endpoint，可在HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索DMK_g_regions:fsp_Cascading.cinder获取cinder的url地址。再次执行3.c。
- 如果仍未修复与cinder的连接异常，请联系技术支持工程师协助解决。
##### - 可能原因：复制配额不足。
- 查看附加信息中“错误码”值是否为CSBS.9006。
- 是，请执行4.b。
- 否，请联系技术支持工程师协助解决。
- 联系附加信息中“租户名称”所对应租户，通知其使用VDC管理员帐号登录ManageOne运营面，根据附加信息中的“目标区域名称”和“目标项目名称”切换到对应区域的对应项目。
- 进入“云服务器备份 ”页面申请复制空间。申请空间的详细操作请参见申请备份空间。
##### 参考信息
无。
********.29 1020759 连接ManageOne运营平台失败
##### 告警解释
连接ManageOne运营平台失败。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020759 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| SC地址 | ManageOne运营平台接口地址。 |
| 节点IP | 产生告警的节点IP。 |
| HTTP返回码 | 请求响应的HTTP返回码。 |
##### 对系统的影响
云服务器备份服务和云硬盘备份服务无法申请备份空间和复制空间。
##### 可能原因
- Karbor节点和ManageOne运营平台之间的网络中断。
- ManageOne运营平台的URL地址配置不正确。
- ManageOne运营平台故障。
##### 处理步骤
##### - 可能原因：Karbor节点和ManageOne运营平台之间的网络中断。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行docker exec -ti karborapi bash -c "cat /etc/karbor/karbor.conf" | grep sc_endpoint命令获取ManageOne运营平台的url地址。
- 使用ping命令检查到ManageOne运营平台的网络是否连通。
- 是，请执行2。
- 否，请联系网络管理员修复网络。
##### - 可能原因：ManageOne运营平台的URL地址配置不正确。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 使用命令set_karbor_endpoints --sc_endpoint sc_url命令配置正确的运营平台URL地址，可在HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索“DMK_g_console:silvan.rest_address”获取sc_url地址。
- 使用check_karbor_connect命令检测Karbor节点和ManageOne运营平台的通信是否恢复正常。
- 是，处理结束。
- 否，请执行3。
##### - 可能原因：ManageOne运营平台故障。
- 请联系运营平台管理员，确保运营平台运行正常。
##### 参考信息
无。
********.30 1020758 上报计量数据失败
##### 告警解释
上报计量数据失败。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020758 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点IP | 产生告警的节点IP。 |
| 错误码 | 故障对应的错误码。 |
| Meter地址 | 计量服务接口地址。 |
##### 对系统的影响
云服务器备份无法生成计费信息。
##### 可能原因
- Karbor节点和FusionSphere OpenStack之间的网络中断。
- 对接Ceilometer的连接异常。
##### 处理步骤
##### - 可能原因：Karbor节点和FusionSphere OpenStack之间的网络中断。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行命令docker exec -ti karborapi bash -c "cat /etc/karbor/karbor.conf" | grep ceilometer_endpoint获取配置的ceilometer的url地址。
- 执行ping ceilometer的url地址命令检查到FusionSphere OpenStack Ceilometer的网络是否连通。
- 是，请执行2。
- 否，请联系网络管理员修复网络。
##### - 可能原因：对接Ceilometer的连接异常。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行check_karbor_connect命令，查看“Ceilometer”行对应的“Check_Result”值。
- 如果为“OK”，请联系技术支持工程师协助解决。
- 如果为“SSLError”，请参见通过ManageOne界面方式单个或批量更换证书更新FusionSphere部件的“FusionSphere-API”证书。再次执行2.c。
- 如果为“Error”，执行set_karbor_endpoints --cei_endpoint Ceilometer的url地址命令设置 Ceilometer的endpoint，可在HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索“DMK_g_regions:fsp_Cascading.metering”获取Ceilometer的url地址。再次执行2.c。
- 如果仍未修复与Ceilometer的连接异常，请联系技术支持工程师协助解决。
##### 参考信息
无。
********.31 1023093 备份服务节点间网络异常
##### 告警解释
当网络通信丢包率或者网络延迟过高时，上报此告警。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1023093 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 设备名称 | 用于网络通信的网络设备名称。 |
| 目标IP | 与当前节点通信异常的目标节点IP。 |
| 节点IP | 产生告警的节点IP。 |
| 故障原因 | 导致上报告警的网络状态检测结果。 |
| 额外信息 | 网络状态检测结果的说明信息。 |
##### 对系统的影响
可能会造成系统响应超时，甚至影响业务。
##### 可能原因
- 虚拟机状态异常。
- ip地址不存在。
- 防火墙禁用访问。
- 路由设置不当。
- 网络设备故障。
##### 处理步骤
##### - 可能原因：虚拟机状态异常。
- 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
- 在“集中告警”页面，查看当前告警的“区域”。
- 在“运维地图”页面右边的“快速访问”导航栏中，单击“Service OM”，选择区域后进入Service OM界面。
- 选择“资源 > 计算资源 > 虚拟机”。
- 按IP搜索“节点IP”对应的虚拟机。
- 确认虚拟机的状态、电源状态是否均为运行中。
- 否，请执行1.g。
- 是，请执行2。
- 恢复虚拟机的电源并重启虚拟机。
等待节点系统启动后，再等待一分钟，查看告警是否恢复。
- 是，处理结束。
- 否，请执行2。
##### - 可能原因：ip地址不存在。
- 单击“更多 > VNC登录”登录Karbor节点。
- 执行ifconfig命令检查是否存在ip地址。
- 否，执行service network restart命令重启网络，查看告警是否恢复。
- 是，请执行3。
##### - 可能原因：防火墙禁用访问。
执行iptables -nL命令检查防火墙规则，查看告警定位信息“目标IP”所在网段是否被禁用。
- 是，执行iptables命令删除禁用的条目。可执行iptables -h命令查看该命令的详细使用说明。
- 否，请执行4。
##### - 可能原因：路由设置不当。
执行route -n命令，请收集命令执行结果后联系技术支持工程师协助解决。
##### - 可能原因：网络设备故障。
请联系技术支持工程师协助解决。
##### 参考信息
无。
********.32 1020756 注册CSBS-VBS到统一证书管理服务失败
##### 告警解释
注册CSBS-VBS服务到ManageOne的统一证书管理服务，注册失败则发送此告警。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020756 | 次要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点IP | 产生告警的节点IP。 |
| 统一证书管理服务URL | 统一证书管理服务URL。 |
| HTTP返回码 | 请求响应的HTTP返回码。 |
##### 对系统的影响
无法通过统一证书管理服务批量替换CSBS-VBS证书。
##### 可能原因
统一证书管理服务URL错误。
##### 处理步骤
263. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
264. 在主菜单中选择“集中告警 > 当前告警”。
265. 查看告警附加信息中“统一证书管理服务URL”的值是否满足格式“https://console-silvan.{domain_name}:26335”，并且{domain_name}的值为HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all_CN.xlsm》“基本参数”页签中“global_domain_name”对应的规划值。
- 是，请执行7。
- 否，请执行4。
266. 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
267. 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
268. 执行set_karbor_endpoints --cmc_endpoint cmc_url命令配置正确的统一证书管理服务URL地址，其中“cmc_url”请根据3中所述格式及“global_domain_name”规划值构造。查看回显信息是否为“Set cmc_endpoint successfully.”并且15分钟后告警是否自动清除。
- 是，处理结束。
- 否，请执行7。
269. 请联系技术支持工程师协助解决。
##### 参考信息
无。
< 上一节
******** eReplication
< 上一节
********.1 0x3230014 备份失败
##### 告警解释
系统配置数据备份至SFTP服务器失败。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230014 | 警告 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 服务器IP地址 | 备份配置数据的服务器IP地址 |
##### 对系统的影响
无法自动备份配置数据。
##### 可能原因
- 管理服务器与SFTP服务器间网络异常。
- 配置访问SFTP服务器的用户名及密码错误。
- 配置访问SFTP服务器的用户权限不足。
- SFTP服务器剩余的备份系统配置数据空间不足。
##### 处理步骤
270. 通过告警详细信息当中的服务器IP地址登录BCManager服务管理界面(登录链接：https://IP:9443/，9443为默认端口)，选择“设置 > 数据维护 > 系统配置数据”获取到SFTP IP地址，用户名和端口。
271. 使用PuTTY，以告警详情信息中的服务器IP地址登录到对应节点。
默认帐号：DRManager，默认密码：*****。
272. 然后执行su - root命令切换到root帐号。
root帐号的默认密码为*****。
273. 根据1获取到的SFTP IP地址，执行ping SFTP IP地址命令，检查是否可以ping通SFTP IP地址。
- 是，请转5。
- 否，请修复主备节点间的网络连接，在1当中的“系统配置数据”界面手动执行备份操作后，如果已清除，流程结束，否则请转5。
274. 根据4的执行结果，查看是否存在丢包的现象。
- 是，联系管理员提高网络性能质量，保证管理服务器与SFTP服务器之间通信稳定，处理结束。
- 否，执行6。
275. 使用FTP客户端工具，通过用户和密码登录SFTP服务器，检查用户名和密码是否正确。
- 是，请转7。
- 否，请重新配置访问SFTP服务器的用户名及密码，具体操作参考1中的“ 系统配置数据”界面。配置成功后，在1当中的“系统配置数据”界面手动执行备份操作后，如果已清除，流程结束，否则请转7。
276. 使用FTP客户端上传文件，确认是否上传成功。
- 是，请转8。
- 否，请登录SFTP服务器，并重新配置用户的读写权限和系统配置数据空间。配置完成后，在1当中的“系统配置数据”界面手动执行备份操作后，如果已清除，流程结束，否则请转8。
277. 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.2 0x3230024 证书已过期
##### 告警解释
网元的证书已过期，导致证书校验失败。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230024 | 警告 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 网元IP地址 | 网元证书所在节点的IP地址 |
| 服务器IP地址 | 服务器的IP地址 |
##### 对系统的影响
存在对端被仿冒风险。
##### 可能原因
证书已过期。
##### 处理步骤
278. 通过告警详细信息当中的服务器IP地址，以DRManager帐号登录到服务器后台操作系统。
默认帐号：DRManager，默认密码：*****。
279. 执行su - root命令切换到root帐号。
默认帐号：root，默认密码：*****。
280. 执行命令cd /opt/BCManager/Runtime;./jre6.0.18/bin/keytool -list -v -keystore ./LegoRuntime/certs/bcm.keystore检查根证书是否过期（密钥库默认密码：BCM@DataProtect123）。
回显中“Valid from”行对应的“until”是否已经超过当前日期。
- 如果已超过，表示证书已经过期，请记录过期证书对应的“Owner”行信息。请转4。
- 如果未超过，请转6。
281. 登录ManageOne运维面，通过“系统管理 > 统一证书”进入统一证书管理界面。在所有区域中查找主体信息与3中记录的“Owner”行信息相同的证书，并单击证书右侧的“更新”完成更新操作。
282. 通过ManageOne运维面的快速访问“eReplication”进入BCManager服务管理界面，在BCManager界面选择“资源 > localServer > FusionSphere”界面，选择 Openstack组件，单击“刷新”，查看告警是否清除。
- 是，流程结束。
- 否，请转6。
283. 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.3 0x3230025 证书校验失败
##### 告警解释
网元的证书不受信任，导致证书校验失败。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230025 | 警告 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 网元IP地址 | 网元证书所在节点的IP地址 |
##### 对系统的影响
存在对端被仿冒风险。
##### 可能原因
- 未导入根CA证书。
- 网元证书的根CA证书已变更。
##### 处理步骤
284. 检查是否已导入根CA证书。
- 是，请转2。
- 否，请参考《OceanStor BCManager 8.0.0 eReplication 用户指南》的“导入设备CA证书”章节，导入该网元的根CA证书。如果告警未清除，请转2。
285. 检查网元证书的根CA证书是否已变更。
- 否，请转3。
- 是，请参考《OceanStor BCManager 8.0.0 eReplication 用户指南》的“导入设备CA证书”章节，替换在容灾管理服务器上该网元的根CA证书。如果告警未清除，请转3。
286. 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.4 0x323002C 占位虚拟机不存在
##### 告警解释
服务实例所在虚拟机的占位虚拟机不存在。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x323002C | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 服务实例名称 | 服务实例的名称 |
| 虚拟机名称 | 服务实例中的生产虚拟机名称 |
| 服务实例类型 | 服务实例的类型 |
##### 对系统的影响
导致故障恢复或计划性迁移失败，业务无法恢复。
##### 可能原因
占位虚拟机被删除。
##### 处理步骤
287. 确认该实例是否需要删除，删除实例操作请参见删除CSHA实例，CSDR和VHA+CSDR服务实例删除操作请参考云服务器容灾服务“删除实例”的操作方法。待该执行任务完成后，检查告警是否自动清除。
- 是，该操作结束。
- 否，请转2。
288. 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.5 0x323002D 占位虚拟机未配置
##### 告警解释
服务实例所在虚拟机的占位虚拟机未配置。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x323002D | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 服务实例名称 | 服务实例的名称 |
| 虚拟机名称 | 服务实例中的生产虚拟机名称 |
| 服务实例类型 | 服务实例的类型 |
##### 对系统的影响
导致故障恢复或计划性迁移失败，业务无法恢复。
##### 可能原因
无。
##### 处理步骤
289. 确认该实例是否需要删除，CSHA服务实例删除实例请参见删除CSHA实例，云服务器容灾服务“删除实例”的操作方法与此相同。待该执行任务完成后，检查告警是否自动清除。
- 是，该操作结束。
- 否，请转2。
290. 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.6 0x323002F 一致性组状态异常
##### 告警解释
服务实例中的一致性组状态不满足。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x323002F | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 服务实例名称 | 服务实例的名称 |
| 一致性组ID | 服务实例对应存储的一致性组ID |
| 服务实例类型 | 服务实例的类型 |
##### 对系统的影响
服务实例无法满保护的要求，可能导致数据不一致。
##### 可能原因
一致性组状态异常。
##### 处理步骤
291. 登录ManageOne运营面，更新状态异常的服务实例或重启eRepliction服务。
- VHA服务实例更新服务实例的具体操作请参见更新VHA实例中的保护对象。云服务器容灾服务和云服务器高可用服务“更新实例”的操作方法与此相同。
更新后检查告警是否已清除。如果已清除，流程结束，否则请转3。
- 重启eRepliction服务的具体操作请参考2。
292. 重启eReplication服务。
- 停止eReplication服务。
- 使用浏览器，以“eReplication”帐号登录当前DMK。
- 登录地址：https:// DMK浮动IP:8443。可在HUAWEI CLOUD Stack Deploy工具导出的参数信息汇总文件《xxx_export_all_CN.xlsm》的“portal”页签中获得登录地址。
- “eReplication”帐号默认密码：*****。
- 在菜单栏中，选择“部署导向”。
- 在“服务配置”栏，选择“服务”页签，选择“eReplication”服务。
- 选择版本：请选择“8.0.x”，其中x代表实际版本号，请根据实际安装的eReplication版本号选择。
- 选择部署操作：选择“停止服务”。
- 选择区域：选择“local”。
- 主机仓库：选择“否”。
- 灰度部署：选择“否”。
- 单击“下一步”。
- 单击“执行”，在弹出的“确认”框中单击“确定”。
- 待执行完毕后，回显中显示failed=0，启动eReplication服务。
- 在菜单栏中，选择“部署导向”。
- 在“服务配置”栏，选择“服务”页签，选择“eReplication”服务。
- 选择版本：请选择“8.0.x”，其中x代表实际版本号，请根据实际安装的eReplication版本号选择。
- 选择部署操作：选择“启动服务”。
- 选择区域：选择“local”。
- 主机仓库：选择“否”。
- 灰度部署：选择“否”。
- 单击“下一步”。
- 单击“执行”，在弹出的“确认”框中单击“确定”。
- 待服务启动成功后，回显中显示failed=0，等待15分钟，检查告警是否已清除。
- 是，流程结束。
- 否，请转3。
293. 请获取一致性组ID联系技术支持工程师协助处理。
##### 参考信息
无。
********.7 0x3230030 HA心跳中断
##### 告警解释
主节点未接收到备节点的心跳信息。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230030 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 主节点名称 | 主节点的名称 |
| 主节点IP地址 | 主节点的IP地址 |
| 备节点名称 | 备节点的名称 |
| 备节点IP地址 | 备节点的IP地址 |
##### 对系统的影响
可能引起仲裁，使备端切换为主端。
##### 可能原因
- 主节点或备节点处于异常状态（如复位、下电等）。
- 网络连接异常，造成链路中断。
- 网络配置变更，造成链路中断。
##### 处理步骤
294. 使用PuTTY，以告警详细信息当中的主节点IP地址登录主节点服务器后台操作系统。
默认帐号：DRManager，默认密码：*****。
295. 执行su - root命令切换到root帐号。
root帐号的默认密码为*****。
296. 检查主节点是否可以ping通备节点。如果是IPv4，执行ping 备节点IP地址命令，如果是IPv6，执行ping6 备节点IP地址命令，检查是否可以ping通。备节点IP地址通过告警详细信息当中的备节点IP地址获取。
- 是，转到4。
- 否，请修复主节点和备节点间的网络连接，修复成功后，等待5分钟，检查告警是否自动清除，如果已清除，流程结束，否则请转4。
297. 使用PuTTY，以主节点和备节点字段对应的IP地址登录主节点和备节点。
默认帐号：DRManager，默认密码：*****。
298. 执行su - root命令切换到root帐号。
root帐号的默认密码为*****。
299. 执行cd /opt/BCManager/Runtime/bin;sh showSystemStatus.sh查看主节点和备节点上的服务状态。
如果命令回显中ResStatus列中的值存在非Normal或Active_normal，表示有服务处理未运行状态。请转7。
如果命令回显中ResStatus的值为Normal或Active_normal，表示服务都处于运行状态，请联系技术工程师协助解决。
300. 等待10分钟后，检查服务是否都已经处于运行状态。
- 是，检查告警是否自动清除，如果已清除，流程结束。如果未清除，请联系技术工程师协助解决。
- 否，请联系技术工程师协助解决。
##### 参考信息
无。
********.8 0x3230031 HA同步失败
##### 告警解释
主节点向备节点同步文件失败。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230031 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 主节点名称 | 主节点的名称 |
| 主节点IP地址 | 主节点的IP地址 |
| 备节点名称 | 备节点的名称 |
| 备节点IP地址 | 备节点的IP地址 |
##### 对系统的影响
主备两端信息不一致，切换后配置文件可能异常，导致HA无法正常启动。
##### 可能原因
- 备节点处于异常状态（如复位、下电等）。
- 文件同步链路故障。
- 磁盘空间不足。
##### 处理步骤
301. 使用PuTTY，以告警详细信息当中的主节点IP地址登录主节点后台操作系统。
默认帐号：DRManager，默认密码：*****。
302. 执行su - root命令切换到root帐号。
root帐号的默认密码为*****。
303. 检查主节点是否可以ping通备节点。如果是IPv4，执行ping 备节点IP地址命令，如果是IPv6，执行ping6 备节点IP地址命令，检查是否可以ping通。备节点IP地址通过告警详细信息中的备节点IP地址获取。
- 是，转到4。
- 否，请修复主节点和备节点间的网络连接，修复成功后，等待5分钟，检查告警是否自动清除，如果已清除，流程结束，否则请转4。
304. 使用PuTTY，以主节点和备节点字段对应的IP地址登录主节点和备节点。
默认帐号：DRManager，默认密码：*****。
305. 执行su - root命令切换到root帐号。
root帐号的默认密码为*****。
306. 执行cd /opt/BCManager/Runtime/bin;sh showSystemStatus.sh查看主节点和备节点上的服务状态。
如果命令回显中ResStatus列中的值存在非Normal或Active_normal，表示有服务处于未运行状态。请转7。
如果命令回显中ResStatus的值为Normal或Active_normal，表示服务都处于运行状态，请联系技术工程师协助解决。
307. 等待10分钟后，检查服务是否都已经处于运行状态。
- 是，转到8。
- 否，请联系技术工程师协助解决。
308. 在备节点服务器上执行cd /opt/BCManager/Runtime/ha/module/hasync/plugin/conf命令，切换到conf目录，执行df -h命令依次检查conf目录下hasync_mod.xml文件中所有目录是否存在存储空间利用率高于95%的情况。
- 是，请增加备节点目录的磁盘空间，增加磁盘空间后等待10分钟，如果告警已清除，流程结束。如果告警未清除，请转9。
- 否，请转9。
309. 请联系技术支持工程师协助处理。
##### 参考信息
无。
********.9 0x3230033 HA链路中断
##### 告警解释
主节点链路无法连接到备端。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230033 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 主节点名称 | 主节点名称 |
| 主节点IP地址 | 主节点IP地址 |
| 主节点端口 | 主节点端口 |
| 备节点名称 | 备节点名称 |
| 备节点IP地址 | 备节点IP地址 |
##### 对系统的影响
同步链路、仲裁链路的可靠性降低。
##### 可能原因
- 当前链路网络连接异常。
- 网络配置发生变更。
##### 处理步骤
310. 使用PuTTY，通过告警详细信息中的主节点IP地址登录到主节点服务器后台操作系统。
默认帐号：DRManager，默认密码：*****。
311. 执行su - root命令切换到root帐号。
root帐号的默认密码为*****。
312. 检查主节点是否可以ping通备节点。如果是IPv4，执行ping 远端节点IP地址命令，如果是IPv6，执行ping6 远端节点IP地址命令，检查是否可以ping通。备节点IP地址通过告警详细信息中的备节点IP地址获取。
- 是，转到4。
- 否，请修复主备节点间的网络连接，修复成功后，等待5分钟，检查告警是否自动清除，如果已清除，流程结束，否则请转4。
313. 查看是否存在丢包的现象。
- 是，联系管理员提高网络性能质量，保证主节点和备节点服务器之间通信稳定。检查告警是否自动清除，如果已清除，流程结束。如果未清除，请转5。
- 否，执行5。
314. 使用PuTTY，以主节点和备节点IP分别登录到主节点和备节点服务器后台操作系统。
默认帐号：DRManager，默认密码：*****。
315. 执行su - root命令切换到root帐号。
root帐号的默认密码为*****。
316. 执行cd /opt/BCManager/Runtime/bin;sh showSystemStatus.sh命令，查看主节点和备节点上的服务状态。
如果命令回显中eReplicationServer的值为Not Normal，表示有服务处于未运行状态。请转8。
如果命令回显中eReplicationServer的值为Normal，表示服务都处于运行状态，请联系技术工程师协助解决。
317. 等待10分钟后，检查服务是否都已经处于运行状态。
- 是，检查告警是否自动清除，如果已清除，流程结束。
- 否，请联系技术工程师协助解决。
##### 参考信息
无。
********.10 0x3230034 HA网关不通
##### 告警解释
主节点与网关通信异常。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230034 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 主节点名称 | 主节点的名称 |
| 主节点IP地址 | 主节点的IP地址 |
| 主节点角色 | 主节点的角色名称 |
| 网关 | 网关的IP地址 |
##### 对系统的影响
备端不能切换为主端。
##### 可能原因
- 网关服务器处于异常状态（如复位、下电等）。
- 连接网关的网络连接异常，造成链路中断。
- 连接网关的网络配置发生变更，造成链路中断。
##### 处理步骤
318. 查看网关服务器是否处于复位或下电状态。
- 是，转到2。
- 否，转到3。
319. 若网关服务器处于复位状态，则等待复位启动完成，若网关服务器处于下电状态，则给机器上电，启动完成后，查看实时告警，确认告警是否清除。
- 是，处理完毕。
- 否，转到3。
320. 使用PuTTY，登录到管理服务器后台操作系统节点。
默认帐号：DRManager，默认密码：*****。
321. 使用ping命令检查管理服务器与网关的连接是否正常。
- 是，转到5。
- 否，修复管理服务器与网关之间的网络连接，等待5分钟，检查告警是否自动清除，如果已清除，流程结束，否则请转到5。
322. 使用PuTTY，登录到管理服务器后台操作系统节点。
默认帐号：DRManager，默认密码：*****。
323. 执行su - root命令切换到root帐号。
root帐号的默认密码为*****。
324. 执行cd /opt/BCManager/Runtime/bin; sh configSystem.sh -a命令检查告警主机连接网关的网络配置是否正常。
- 是，转到8。
- 在《OceanStor BCManager 8.0.0 eReplication 用户指南》中搜索configSystem.sh命令，按指导重新配置，配置完成后，检查告警是否已清除， 如果已清除，流程结束， 否则请转8。
325. 请联系技术支持工程师协助处理。
##### 参考信息
无。
********.11 0x3230036 仲裁服务异常
##### 告警解释
仲裁服务异常。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230036 | 紧急 | 是 |
##### 对系统的影响
可能导致故障发生时，业务不能恢复。
##### 可能原因
- eReplication仲裁服务配置错误。
- 仲裁服务器证书校验失败。
- 仲裁服务内部错误。
##### 处理步骤
326. 在HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索“Third_site_IP_of_Arbitration_Servic”、“Arbitration_DC1_01_API”、“Arbitration_DC1_02_API”、“Arbitration_DC2_01_API”、“Arbitration_DC2_02_API”获取仲裁服务器的IP地址，搜索“csha_region_map_info”对应值中“|”符号后面对应的值为AZ配置信息。
327. 使用PuTTY，以告警详细信息中的本端链路IP地址登录到本端服务器节点。
默认帐号：DRManager，默认密码：*****。
328. 执行su - root命令切换到root帐号。
root帐号的默认密码为*****。
329. 执行cd /opt/BCManager/Runtime/bin && sh configArbitration.sh -a命令，回显中localAZ，PeerAZ，ArbIP行对应的配置值与1获取的信息是否相同。
- 是，请转5。
- 否，在《OceanStor BCManager 8.0.0 eReplication 用户指南》中搜索configArbitration.sh命令，按指导重新配置仲裁服务器信息，等待10秒钟后重新查询仲裁服务，检查告警是否清除。如果未清除，请转5。
330. 执行cd /opt/BCManager/Runtime/LegoRuntime/certs;../../jre6.0.18/bin/keytool -list -v -keystore arb.keystore命令检查仲裁服务器证书是否过期（密钥库默认密码：*****）。回显中“Valid from”行对应的“until”是否已经超过当前日期。若超过，表示证书已经过期。
- 是，获取最新的仲裁服务器证书，并参考《OceanStor BCManager 8.0.0 eReplication 用户指南》的“替换仲裁服务器证书” 章节完成证书替换，然后检查告警是否清除，如果已清除，流程结束，否则请转6。
- 否，请转6。
331. 请联系技术工程师协助解决。
##### 参考信息
无。
********.12 0x3230037 服务实例故障恢复失败
##### 告警解释
服务实例自动故障恢复失败。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230037 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 服务实例名称 | 服务实例的名称 |
##### 对系统的影响
故障恢复失败，可能导致业务无法正常恢复。
##### 可能原因
无。
##### 处理步骤
332. 使用浏览器，登录ManageOne运维面，在“快速访问”页签单击“eReplication”，登录eReplication。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
333. 选择“恢复”，根据服务实例名称选择到相应的恢复计划，在“执行历史”当中，根据执行步骤的失败原因进行修复。修复后如果未执行成功，则转3。
334. 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.13 0x3230038 服务实例重保护失败
##### 告警解释
服务实例自动重保护失败。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230038 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 服务实例名称 | 服务实例的名称 |
##### 对系统的影响
重保护失败，可能导致服务实例不能被保护。
##### 可能原因
无。
##### 处理步骤
335. 使用浏览器，登录ManageOne运维面，在“快速访问”页签单击“eReplication”，登录eReplication。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
336. 选择“恢复”，根据服务实例名称选择到相应的恢复计划，在“执行历史”当中，根据执行步骤的失败原因进行修复。修复后如果未执行成功，则转3。
337. 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.14 0x323003A 虚拟机不满足保护要求
##### 告警解释
服务实例中虚拟机不满足保护要求。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x323003A | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 虚拟机名称 | 服务实例中不满足保护要求的虚拟机名称 |
| 服务实例名称 | 服务实例的名称 |
| 服务实例类型 | 服务实例的类型 |
##### 对系统的影响
虚拟机不支持容灾，不能被一致性保护。
##### 可能原因
虚拟机使用的存储或虚拟机类型不满足保护的要求。
##### 处理步骤
338. 登录ManageOne运营面，检查服务实例中是否存在不满足保护要求的虚拟机。CSHA服务实例具体操作请参见查看受保护的ECS。云服务器容灾服务“查看受保护的ECS/BMS”的操作方法与此相同。
- 是，请转2。
- 否，请转3
339. 在ManageOne运营面，取消不满足保护要求的虚拟机。CSHA服务实例具体操作请参见从CSHA实例中取消ECS容灾保护。云服务器容灾服务“取消云服务器容灾保护”的操作方法与此相同。取消后检查告警是否已清除。
- 是，操作结束。
- 否，请转3。
340. 在ManageOne运营面，添加未保护的虚拟机。CSHA服务实例具体操作请参见添加新ECS到CSHA实例。云服务器容灾服务“添加新云服务器”的操作方法与此相同。添加后检查告警是否已清除。
- 是，操作结束。
- 否，请转4。
341. 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.15 0x323003B 虚拟机中已卸载的卷未从服务实例中清理
##### 告警解释
虚拟机中已卸载的卷未从服务实例中清理。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x323003B | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 服务实例名称 | 服务实例的名称 |
| 服务实例类型 | 服务实例的类型 |
##### 对系统的影响
占用系统存储资源。
##### 可能原因
虚拟机的卷被卸载。
##### 处理步骤
342. 登录ManageOne运营面，检查服务实例中是否存在“云硬盘已被卸载”的虚拟机。VHA服务实例具体操作请参见查看受保护的ECS/BMS。云服务器容灾服务和云服务器高可用服务“查看受保护的ECS/BMS”的操作方法与此相同。
- 是，VHA服务实例请参见从VHA实例中取消云硬盘容灾保护，删除不再被虚拟机使用的卷资源。 云服务器容灾服务和云服务器高可用服务“取消云硬盘保护”的操作方法与此相同。
- 否，请转3。
343. 删除后检查告警是否已清除。
- 流程结束。
- 否，请转3。
344. 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.16 0x323003C 虚拟机的卷未创建容灾保护
##### 告警解释
服务实例中虚拟机的卷未创建容灾保护，导致该虚拟机不能进行数据一致性保护。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x323003C | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 服务实例名称 | 服务实例的名称 |
| 虚拟机名称 | 服务实例中未创建容灾保护的卷对应的虚拟机名称 |
| 服务实例类型 | 服务实例的类型 |
##### 对系统的影响
虚拟机不能被一致性保护。
##### 可能原因
虚拟机使用的卷未创建复制保护。
##### 处理步骤
345. 登录ManageOne运营面，添加未保护的卷。VHA服务实例具体操作请参见《华为云Stack 6.5.1 用户指南(Region Type I)》中添加新云硬盘到VHA实例，云服务器容灾服务和云服务器高可用服务“添加新云硬盘”的操作方法与此相同。
346. 添加后检查告警是否已清除。
- 是，流程结束。
- 否，请转3。
347. 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.17 0x323003D 受保护虚拟机从服务实例中被移除
##### 告警解释
服务实例中虚拟机被移除，导致该虚拟机不能被保护组保护。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x323003D | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 服务实例名称 | 服务实例的名称 |
| 虚拟机名称 | 服务实例中已经被移除的虚拟机名称 |
##### 对系统的影响
虚拟机被删除/迁移，导致该虚拟机不在服务实例中或部分在服务实例中。
##### 可能原因
- 虚拟机被删除。
- 虚拟机没有使用服务实例对应的数据存储。
##### 处理步骤
348. 确认虚拟机是否已经被删除。具体操作请参见筛选与搜索。确认是否存在该虚拟机。
- 否，则手工清除告警。如果告警未清除，请转2。
- 是，请转2。
349. 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.18 0x323003E 服务实例不满足故障恢复要求
##### 告警解释
服务实例不满足故障恢复要求。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x323003E | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 服务实例名称 | 服务实例的名称 |
##### 对系统的影响
导致服务实例无法进行故障恢复。
##### 可能原因
- 虚拟机被删除。
- 服务实例中虚拟机的卷状态不满足。
##### 处理步骤
350. 检查服务实例对应的告警，根据告警提示的修复建议修复，然后等待下次服务实例进行恢复。如果告警未清除，请转步骤2。
351. 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.19 0x323003F IAM证书更新失败
##### 告警解释
从IAM服务器更新证书失败
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x323003F | 警告 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| IAM IP地址或者域名地址 | IAM服务器的IP地址或者域名 |
##### 对系统的影响
更新IAM证书失败，可能导致用户token校验失败。
##### 可能原因
- IAM服务器无法连接。
- 对接IAM的用户名密码错误。
##### 处理步骤
352. 使用浏览器，登录ManageOne运维面，在“快速访问”页签单击“eReplication”，登录eReplication。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
353. 使用PuTTY，根据1浏览器中的服务器IP地址登录到主服务器节点。
默认帐号：DRManager，默认密码：*****。
354. 执行su - root命令切换到root帐号。
root帐号的默认密码为*****。
355. 执行ping命令检查BCManager与IAM服务器间网络连接是否正常。
- 是，请转6。
- 否，请修复网络连接，请转5。
356. 修复后在BCManager界面选择“资源 > localServer > FusionSphere”界面，选择 Openstack组件，单击“刷新”按钮。检查告警是否清除。
- 是，处理结束。
- 否，请执行6。
357. 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.20 0x3230041 上报计量信息失败
##### 告警解释
上报容灾计量信息失败。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230041 | 警告 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 上报失败的Project名称。如果是多个Project，用逗号隔开。 | 计量信息上报失败的项目名称 |
##### 对系统的影响
计量信息上报失败，可能导致计量系统中容灾服务的计量信息不准确。
##### 可能原因
无。
##### 处理步骤
358. 使用浏览器，登录ManageOne运维面，在“快速访问”页签单击“eReplication”，登录eReplication。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
359. 选择“资源 > localServer > FusionSphere” 界面，选择 Openstack组件，在“Region”页签中根据Metering列获取计量URL地址。
360. 在HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索“cascading_region”和“cascading_domain”获取到region和domain信息，并替换为链接中https://metering.{cascading_region}.{cascading_domain}:443/的cascading_region和cascading_domain值，与2获取的地址对比是否相同。或在汇总文件《xxx_export_all_CN.xlsm》中搜索“openstack_region”和“openstack_domain”获取到region和domain信息，并替换为链接中https://metering.{openstack_region}.{openstack_domain}:443/的openstack_region和openstack_domain值，与2获取的地址对比是否相同。
- 是，请转6。
- 否，请根据Metering列所在的修改按钮完成计量URL地址的修改。如果告警未清除，请转6。
361. 使用PuTTY，以告警详细信息中的本端链路IP地址登录到本端服务器节点。
默认帐号：DRManager，默认密码：*****。
362. 执行su - root命令切换到root帐号。
root帐号的默认密码为*****。
363. 执行ping命令检查BCManager与计量服务器（可通过2中的计量URL地址获取）间网络连接是否正常。
- 是，请转7。
- 否，请修复网络连接，然后转7。
364. 待网络连接恢复正常，等待5分钟，系统重试上报后，检查告警是否清除。
- 是，处理结束。
- 否，请转8。
365. 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.21 0x3230042 连接日志服务器异常
##### 告警解释
服务器连接日志服务器异常。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230042 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 日志类型 | 上报的日志类型 |
| 服务器IP地址 | 上报日志的服务器IP地址 |
| 日志服务器IP地址或域名 | 日志服务器的IP地址或域名 |
| 端口 | 日志服务器的监听端口 |
##### 对系统的影响
连接日志服务器异常，可能导致需要上报的日志无法上报。
##### 可能原因
- BCManager与日志服务器间网络连接异常。
- 日志服务器运行异常。
##### 处理步骤
366. 使用PuTTY，以告警详细信息中的本端链路IP地址登录到本端服务器节点。
默认帐号：DRManager，默认密码：*****。
367. 执行su - root命令切换到root帐号。
root帐号的默认密码为*****。
368. 执行ping命令检查BCManager与日志服务器间网络连接是否正常。
- 是，请转5。
- 否，请修复网络连接，然后转4。
369. 待网络连接恢复正常，等待5分钟，系统重试上报后，检查告警是否清除。
- 是，处理结束。
- 否，请转5。
370. 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.22 0x3230043 高可用集群中高斯数据库主备间复制中断
##### 告警解释
高可用集群中高斯数据库主备间复制中断。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230043 | 紧急 | 是 |
##### 对系统的影响
主端数据库数据无法同步到备端，备端数据和主端不一致，主备切换后业务数据丢失。
##### 可能原因
- 备端节点宕机。
- 备端eReplication服务挂掉。
- 主备高斯数据库的复制链路中断。
##### 处理步骤
371. 使用PuTTY，以告警详细信息中的本端链路IP地址登录到本端服务器节点。
默认帐号：DRManager，默认密码：*****。
372. 执行su - root命令切换到root帐号。
root帐号的默认密码为*****。
373. 执行export LD_LIBRARY_PATH=/opt/BCManager/Runtime/ha/libs; cd /opt/BCManager/Runtime/ha/module/hacom/script;sh config_ha.sh -a命令，回显中“HaArbLK”行的第二个值即表示远端节点IP地址。
374. 执行cd /opt/BCManager/Runtime/bin;sh showSystemStatus.sh查看本端节点和远端节点上的服务状态，
如果命令回显中ResStatus列中的值都是非Normal或Active_normal，表示服务处理未运行状态。请转5。
如果命令回显中ResStatus的值为Normal或Active_normal，表示服务都处于运行状态，请转7。
375. 执行cd /opt/BCManager/Runtime/bin/;sh startSystem.sh启动备端eReplication服务，如果命令回显中存在“System started completely.”，表示服务已启动成功。查看告警是否清除。
- 是，流程结束。
- 否，请转6。
376. 分别在主、备服务所在操作系统中通过ping命令检查主备服务器之间的网络连接是否正常。
- 是，请转7。
- 否，请修复网络连接，待网络连接正常后，检查告警是否清除，如果告警未清除，请转7。
377. 根据6的执行结果，查看是否存成在丢包的现象。
- 是，联系管理员提高网络性能质量，保证主备服务器之间通信稳定。流程结束。
- 否，执行8。
378. 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.23 0x3230046 共享卷关联的虚拟机不在同一个服务实例中
##### 告警解释
服务实例中缺少共享卷关联的虚拟机。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230046 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 服务实例名称 | 服务实例的名称 |
| 虚拟机名称 | 服务实例中缺少共享卷关联的虚拟机名称 |
| 服务实例类型 | 服务实例的类型 |
##### 对系统的影响
虚拟机不能被一致性保护。
##### 可能原因
共享卷关联的虚拟机未全部加入到服务实例中。
##### 处理步骤
379. 登录ManageOne运营面，完成添加新云服务器。CSDR服务实例具体操作请参见添加新ECS/BMS到容灾实例。云服务器高可用服务“添加新云服务器”的操作方法与此相同。添加后检查告警是否已清除。
- 是，流程结束。
- 否，请转2。
380. 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.24 0x3230047 订单实施结果通知失败
##### 告警解释
订单实施结果通知失败。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230047 | 警告 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 订单ID | 上报配额失败的订单ID |
| 管理平台IP或域名 | 管理平台IP或域名 |
| 管理平台端口 | 管理平台端口 |
| 服务器IP | 服务器的IP地址 |
##### 对系统的影响
订单实施结果通知失败，将导致订单系统中该订单的状态一直处于实施中，租户配额无法得到修正。
##### 可能原因
无。
##### 处理步骤
381. 通过告警详细信息当中的服务器IP地址，以DRManager帐号登录到服务器后台操作系统。
默认帐号：DRManager，默认密码：*****。
382. 执行su - root命令切换到root帐号。
默认帐号：root，默认密码：*****。
383. 通过告警详细信息当中的管理平台IP地址，执行ping管理平台IP或域名命令。
- 如果能ping通，执行4。
- 如果不能ping通，请修复网络连接，网络连接修复正常后，执行4。
384. 等待60分钟，查看告警是否清除。
- 如果告警清除，流程结束。
- 如果该告警未清除，请转5。
385. 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.25 0x3230048 License授权容量耗尽
##### 告警解释
License授权容量耗尽。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230048 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 受保护的VHA服务器数量 | VHA服务实例已保护的服务器总数量 |
| 受保护的CSHA服务器数量 | CSHA服务实例已保护的服务器总数量 |
| 受保护的CSDR服务器数量 | CSDR服务实例已保护的服务器总数量 |
| 授权的VHA服务器数量 | VHA服务实例能够保护的服务器总数量 |
| 授权的CSHA服务器数量 | CSHA服务实例能够保护的服务器总数量 |
| 授权的CSDR服务器数量 | CSDR服务实例能够保护的服务器总数量 |
##### 对系统的影响
仅能进行容灾保护，容灾恢复、容灾拓扑等特性不可使用。
##### 可能原因
无。
##### 处理步骤
386. 请根据告警详细中已超过License授权容量的容灾类型申请扩容License。具体操作请参考文档《OcenaStor BCManager 8.0.0 eReplication License申请指导书 》中的申请扩容项目License章节，并保证新License文件中的授权个数上限大于原有License文件的授权个数。
387. 导入License，具体操作请参考文档《OcenaStor BCManager 8.0.0 eReplication License申请指导书 》中的加载License章节。检查告警是否已清除。
- 是，操作结束。
- 否，请转3。
388. 请联系技术支持工程师协助处理。
##### 参考信息
无。
********.26 0x3230049 系统90天试用期到期
##### 告警解释
系统90天试用期已到期。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230049 | 重要 | 是 |
##### 对系统的影响
389. 对容灾服务实例的业务操作仅限查看、删除和修改服务实例的基本信息（名称、描述）。
390. CSHA的自动故障恢复和自动重保护功能无法使用。
##### 可能原因
90天的试用期到期。
##### 处理步骤
391. 申请License，具体操作请参考《OcenaStor BCManager 8.0.0 eReplication License申请指导书 》中的申请新建项目License章节。
392. 导入License，具体操作请参考《OcenaStor BCManager 8.0.0 eReplication License申请指导书 》中的加载License章节。然后检查告警是否已清除，如果未清除，请转3。
393. 请联系技术支持工程师协助处理。
##### 参考信息
无。
********.27 0x323005C 证书即将过期
##### 告警解释
证书即将过期。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x323005C | 警告/紧急 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 过期有效剩余天数（30、7） | 证书过期前的有效剩余天数 |
| 组件名称 | 证书的名称 |
##### 对系统的影响
证书过期后存在被攻击方仿冒的风险。
##### 可能原因
证书即将过期。
##### 处理步骤
394. 完成证书的替换，若组件名称为eReplication-WebServer，请参考更换灾备服务eReplication证书（eReplication-Portal），其他组件名称参考通过ManageOne界面方式单个或批量更换证书。如果告警未清除，请转2。
395. 请联系技术支持工程师协助处理。
##### 参考信息
无。
********.28 0x323005D 证书已经过期
##### 告警解释
证书已经过期，导致证书校验失败。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x323005D | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 证书已过期天数 | 证书已经过期后的天数 |
| 组件名称 | 证书的名称 |
##### 对系统的影响
存在被攻击方仿冒的风险。
##### 可能原因
服务证书已过期。
##### 处理步骤
396. 完成证书的替换，若组件名称为eReplication-Portal，请参考更换灾备服务eReplication证书（eReplication-Portal），其他组件名称参考通过ManageOne界面方式单个或批量更换证书。如果告警未清除，请转2。
397. 请联系技术支持工程师协助处理。
##### 参考信息
无。
********.29 0x3230064 对接管理平台失败
##### 告警解释
服务器对接管理平台失败。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230064 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 服务器IP | 服务器的IP地址 |
| 微服务类型 | 微服务类型（证书或者统一备份） |
| 管理平台IP或域名 | 管理平台IP或域名 |
| 管理平台端口 | 管理平台端口 |
##### 对系统的影响
无法完成管理平台下发的任务。
##### 可能原因
服务器与管理平台之间网络连接异常。
##### 处理步骤
398. 通过告警详细信息当中的服务器IP地址，以DRManager帐号登录到服务器后台操作系统。
默认帐号：DRManager，默认密码：*****。
399. 执行su - root命令切换到root帐号。
默认帐号：root，默认密码：*****。
400. 通过告警详细信息当中的管理平台IP地址，执行ping 管理平台IP或域名命令。
- 如果能ping通，执行4。
- 如果不能ping通，请修复网络连接，网络连接修复正常后，执行4。
401. 等待5分钟，查看告警是否清除。
- 如果告警清除，流程结束。
- 如果该告警未清除，请转5。
402. 请联系技术支持工程师协助解决。
##### 参考信息
无。
< 上一节
5.2.15 消息通知服务
******** ALM-2000401 tomcat进程不存在
##### 告警解释
SMN服务使用tomcat作为smn-home应用的WEB容器，当部署tomcat的节点检测到tomcat进程不存在时，系统产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 2000401 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | tomcat进程监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
节点无法正常提供服务，需尽快处理。
##### 可能原因
- 系统crontab任务异常没有拉起。
- tomcat配置文件错误无法拉起该进程。
##### 处理步骤
403. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
404. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
405. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
406. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
407. 使用“PuTTY”，通过4中获取的节点管理IP地址登录告警节点。
默认帐号：hermes，默认密码：*****。
408. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
409. 执行以下命令，切换到root用户。
sudo su - root
默认密码：*****
410. 执行以下命令检查crontab是否被注销。
cat /etc/crontab |grep tomcat
*/1 * * * * root su hermes -c "/opt/hermes/smn_home/bin/tomcat_monitor.sh check" >/dev/null 2>&1
当回显信息最前面有“#”，表示crontab被注销。
- 是，请删除“#”取消注销。
- 否，执行9。
411. 执行以下命令切换到hermes用户下，手动执行重启进程命令。
su - hermes
sh /opt/hermes/smn_home/bin/tomcat_monitor.sh restart
412. 重启完成后，执行如下命令检查tomcat进程运行是否正常。
ps -ef|grep tomcat |grep hermes
- 存在回显，执行11。
- 否，请联系技术支持工程师协助解决。
413. 等待10分钟，检查告警是否已自动清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
******** ALM-2000420 ns进程不存在
##### 告警解释
SMN服务当部署ns的节点检测到ns进程不存在时，系统产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 2000420 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | NS进程监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
节点无法正常提供服务，需尽快处理。
##### 可能原因
- 系统crontab任务异常没有拉起。
- notification-server配置文件错误无法拉起该进程。
##### 处理步骤
414. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
415. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
416. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
417. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
418. 使用“PuTTY”，通过4中获取的节点管理IP地址登录告警节点。
默认帐号：hermes，默认密码：*****。
419. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
420. 执行以下命令，切换到root用户。
sudo su - root
默认密码：*****
421. 执行以下命令，检查crontab是否被注销。
cat /etc/crontab | grep notification
[root@SMN-PS-NS-DB-MEM01 config]# cat /etc/crontab | grep notification
*/1 * * * * root su hermes -c "/opt/hermes/notification-server/bin/ns_monitor.sh check" >/dev/null 2>&1
当回显信息最前面有“#”，表示crontab被注销。
- 是，请删除“#”取消注销。
- 否，执行9。
422. 切换到hermes用户下，手动执行启动进程命令。
su - hermes
sh /opt/hermes/notification-server/bin/ns_monitor.sh restart
423. 重启完成后执行如下命令，检查ns进程运行是否正常。
ps -ef | grep notification-server |grep -v grep
- 存在回显，执行11。
- 否，请联系技术支持工程师协助解决。
424. 等待10分钟，检查告警是否已自动清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
******** ALM-2000421 ps进程不存在
##### 告警解释
SMN服务当部署ps的节点检测到ps进程不存在时，系统产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 2000421 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 进程监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
节点无法正常提供服务，需尽快处理。
##### 可能原因
- 系统crontab任务异常没有拉起。
- publishserver配置文件错误无法拉起该进程。
##### 处理步骤
425. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
426. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
427. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
428. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
429. 使用“PuTTY”，通过4中获取的节点管理IP地址登录告警节点。
默认帐号：hermes，默认密码：*****。
430. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
431. 执行以下命令，切换到root用户。
sudo su - root
默认密码：*****
432. 执行以下命令，检查crontab是否被注销。
cat /etc/crontab | grep publishServer
[root@SMN-PS-NS-DB-MEM01 config]# cat /etc/crontab | grep publishServer
*/1 * * * * root su hermes -c /opt/hermes/publishServer/bin/start_publishServer.sh >/dev/null 2>&1
当回显信息最前面有“#”，表示crontab被注销。
- 是，请删除“#”取消注销。
- 否，执行9。
433. 切换到hermes用户下，手动执行启动进程命令。
su - hermes
sh /opt/hermes/publishServer/bin/start_publishServer.sh
434. 重启完成后执行如下命令，检查ps进程运行是否正常。
ps -ef | grep publishServer |grep -v grep
- 存在回显，执行11。
- 否，请联系技术支持工程师协助解决。
435. 等待10分钟，检查告警是否已自动清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
5.2.15.4 ALM-2000456 memcached进程不存在
##### 告警解释
SMN服务使用memcached提供缓存服务，当部署memcached的节点检测到memcached进程不存在时，系统产生此告警。
此告警提示系统故障或者风险，告警处理完毕后，会自动恢复。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 2000456 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 进程监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
节点无法正常提供服务，需尽快处理。
##### 可能原因
- 系统crontab任务异常没有拉起。
- Memcached配置文件错误无法拉起该进程。
##### 处理步骤
436. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
437. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
438. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
439. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
440. 使用“PuTTY”，通过4中获取的节点管理IP地址登录告警节点。
默认帐号：hermes，默认密码：*****。
441. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
442. 执行以下命令切换到root用户。
sudo su - root
默认密码：*****
443. 执行以下命令，检查crontab是否被注销。
cat /etc/crontab | grep memcached
[root@SMN-PS-NS-DB-MEM01 ~]# cat /etc/crontab |grep memcached
*/1 * * * * root su onframework -c "/opt/onframework/memcached/bin/memcached_monitor.sh check"
当回显信息最前面有“#”，表示crontab被注销。
- 是，请删除“#”取消注销。
- 否，执行9。
444. 切换到onframework用户下，手动执行重启进程命令。
su - onframework
sh /opt/onframework/memcached/bin/memcached_monitor.sh restart
445. 重启完成后执行以下命令检查memcached进程运行是否正常。
ps -ef | grep "/memcached" |grep -v grep
- 存在回显，执行11。
- 否，请联系技术支持工程师协助解决。
446. 等待10分钟，检查告警是否已自动清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
******** ALM-2000469 kafka进程不存在
##### 告警解释
SMN服务当部署kafka的节点检测到kafka进程不存在时，系统产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 2000469 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 进程监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
节点无法正常提供服务，需尽快处理。
##### 可能原因
- 系统crontab任务异常没有拉起。
- kafka配置文件错误无法拉起该进程。
##### 处理步骤
447. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
448. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
449. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
450. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
451. 使用“PuTTY”，通过4中获取的节点管理IP地址登录告警节点。
默认帐号：hermes，默认密码：*****。
452. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
453. 执行以下命令，切换到root用户。
sudo su - root
默认密码：*****
454. 执行以下命令，检查crontab是否被注销。
cat /etc/crontab | grep kafka
[root@SMN-KFK-ZK01 ~]# cat /etc/crontab |grep kafka
*/1 * * * * root su hermes -c "/opt/hermes/service/kafka/bin/monitor_kafka.sh start" >/dev/null 2>&1
当回显信息最前面有“#”，表示crontab被注销。
- 是，请删除“#”取消注销。
- 否，执行9。
455. 检查配置文件server.properties中的zookeeper.connect参数配置，是否配置为该kafka集群的三个IP和2181端口。
cd /opt/hermes/service/kafka/config
vi server.properties
456. 切换到hermes用户下，手动执行启动进程命令。
su - hermes
sh /opt/hermes/service/kafka/bin/start_kafka.sh
457. 重启完成后执行以下命令检查kafka进程运行是否正常。
ps -ef | grep kafka |grep -v grep
- 存在回显，执行12。
- 否，请联系技术支持工程师协助解决。
458. 等待10分钟，检查告警是否已自动清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
******** ALM-2000488 smn_haproxy进程不存在
##### 告警解释
haproxy进程不存在，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 2000488 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 进程监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
haproxy进程不存在会引起服务异常。
##### 可能原因
无
##### 处理步骤
459. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
460. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
461. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
462. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
463. 使用“PuTTY”，通过4中获取的节点管理IP地址登录告警节点。
默认帐号：hermes，默认密码：*****。
464. 执行以下命令，切换到root用户。
sudo su - root
默认密码：*****
465. 执行以下命令，防止系统超时退出。
TMOUT=0
466. 执行以下命令，查看haproxy是否正常。
service haproxy status
- 若回显中存在running，请联系技术支持工程师协助解决。
- 若回显中不存在running，请执行9。
467. 执行以下命令，重启服务，并运行10分钟后检查告警是否恢复。
service haproxy restart
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
******** ALM-2000489 smn_keepalived进程不存在
##### 告警解释
LB节点keepalived进程异常，就会产生此告警 。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 2000489 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 进程监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
LB的心跳不起作用，可能会引起LB功能异常，需尽快排查。
##### 可能原因
无
##### 处理步骤
468. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
469. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
470. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
471. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
472. 使用“PuTTY”，通过4中获取的节点管理IP地址登录告警节点。
默认帐号：hermes，默认密码：*****。
473. 执行以下命令，切换到root用户。
sudo su - root
默认密码：*****
474. 执行以下命令，防止系统超时退出。
TMOUT=0
475. 执行以下命令，查看keepalived是否正常。
service keepalived status
- keepalived正常，请联系技术支持工程师协助解决。
- keepalived异常，请执行9。
476. 执行以下命令，重启服务，并运行10分钟后检查告警是否恢复。
rm -rf /home/<USER>/keepalived/run/*;
service keepalived start
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
******** ALM-2000460 GaussdbHA服务进程异常
##### 告警解释
数据库正常状态为Primary或者Standby，如果出现其他状态则表示数据库异常上报此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 2000460 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 进程监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
数据库异常可能导致对应的服务无法访问数据库。
##### 可能原因
网络、磁盘等异常，或者软件缺陷导致的数据库异常。
##### 处理步骤
477. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
478. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
479. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
480. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
481. 使用“PuTTY”工具，通过4中获取的节点IP地址登录产生告警的节点。
默认帐号：hermes，默认密码：*****。
482. 执行以下命令，并按提示输入“root”用户的密码，默认密码：*****，切换至“root”用户。
sudo su - root
483. 执行如下命令，防止“PuTTY”超时退出。
TMOUT=0
484. 执行如下命令确认数据库状态是否正常。
service gaussdb query|grep LOCAL
回显信息如下，表示数据库状态正常。
LOCAL_ROLE                     : Standby
LOCAL_ROLE                     : Standby
- 是，请联系技术支持工程师协助解决。
- 否，执行9。
485. 执行如下命令停止本节点数据库系统。
source /etc/profile
haStopAll -a
486. 执行如下命令启动本节点数据库系统。
haStartAll -a
487. 执行如下命令确认数据库状态是否正常。
service gaussdb query|grep LOCAL
回显信息如下，表示数据库状态正常。
LOCAL_ROLE                     : Standby
LOCAL_ROLE                     : Standby
- 是，执行12。
- 否，请联系技术支持工程师协助解决。
488. 等待10分钟，查看告警是否自动清除。
- 是，告警自动清除。
- 否，请联系技术支持工程师协助解决。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。
******** ALM-2000462 GaussdbHA备份失败
##### 告警解释
数据库本地备份失败时会触发此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 2000462 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 进程监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
数据库本地备份失败会导致数据库发生数据异常，出现严重故障时，没有可用的恢复数据。
##### 可能原因
数据库磁盘空间满等原因。
##### 处理步骤
489. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
490. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
491. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
492. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
493. 使用“PuTTY”工具，通过4中获取的节点IP地址登录产生告警的节点。
默认帐号：hermes，默认密码：*****。
494. 执行以下命令，并按提示输入“root”用户的密码，默认密码：*****，切换至“root”用户。
sudo su
495. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
496. 执行如下命令检查备份失败的原因。
cat /home/<USER>/dbAlarmData/backup_zabbix_alarm
执行结果回显信息如下所示，则表示本地备份失败。
b0(backup fail)u1(upload fail)
497. 请执行命令df -lh /opt/backup/ 检查备份目录空间。
498. Filesystem                Size  Used Avail Use% Mounted on
/dev/mapper/publicvg-lv3  167G  2.1G  156G   2% /opt/backup
如果备份目录空间充足，请联系技术支持工程师协助解决。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。
********0 ALM-2000463 GaussdbHA上传远端备份服务器失败
##### 告警解释
数据库备份上传到sftp服务器失败时会触发此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 2000463 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 进程监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
数据库备份上传sftp服务器失败会导致数据库发生数据异常，出现严重故障时，没有可用的恢复数据。
##### 可能原因
远端存储异常等原因。
##### 处理步骤
499. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
500. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
501. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
502. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
503. 使用“PuTTY”工具，通过4中获取的节点IP地址登录产生告警的节点。
默认帐号：hermes，默认密码：*****。
504. 执行以下命令，并按提示输入“root”用户的密码，默认密码：*****，切换至“root”用户。
sudo su
505. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
506. 执行如下命令检查备份失败的原因。
cat /home/<USER>/dbAlarmData/backup_zabbix_alarm
- 执行结果回显信息如下所示，则表示备份文件上传远端存储失败。
b0(backup success)u1(upload fail)
请检查当前/opt/gaussdb/ha/tools/backupAndRestore/Upload_Server.cfg配置文件中是否已配置远端存储。
- 如下所示，即FTP_SERVER_IP为********，则表示未配置远端存储。请参考手动备份数据章节配置远端存储。
- [root@I-SMN-PS-NS-DB-MEM01 ~]# cat /opt/gaussdb/ha/tools/backupAndRestore/Upload_Server.cfg
- # UDS
- endpoint:********
- ak:U2FsdGVkX18arqCYf+f2gEEuzP/g1d7D5DXZPa2vjUGMh6gYoNLI06vP2DRVAvty
- sk1:U2FsdGVkX18l2Gsyj3GjTGVfYOEppi5FEQeP6uuS2c/eZWm6jpxNnLjpSEgV0Bp1
- sk2:U2FsdGVkX18kUFXApHuaaE6nqycG2GR/aB2UijOg09MoUoSXbaGF/ntIR30+/G61
- bucket:op-svc-i-backup
- # FTP
- FTP_SERVER_IP:********
- FTP_SERVER_PORT:22
- FTP_SERVER_USER:root
- FTP_SERVER_PASSWD:U2FsdGVkX1/6ETkW0Ii4u2nrquYuIrq/wyqWI3oTmaA=
- FTP_SERVER_FILEPATH:/opt/uploadFTPDir
[root@I-SMN-PS-NS-DB-MEM01 ~]#
- 如果回显信息中FTP_SERVER_IP为非********，则表示已配置远端存储。请检查配置是否正确。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。
********1 ALM-2000471 zookeeper进程不存在
##### 告警解释
SMN服务当部署zookeeper的节点检测到zookeeper进程不存在时，系统产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 2000471 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 进程监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
节点无法正常提供服务，需尽快处理。
##### 可能原因
- 系统crontab任务异常没有拉起。
- zookeeper配置文件错误无法拉起该进程。
##### 处理步骤
507. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
508. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
509. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
510. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
511. 使用“PuTTY”，通过4中获取的节点管理IP地址登录告警节点。
默认帐号：hermes，默认密码：*****。
512. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
513. 执行以下命令，切换到root用户。
sudo su - root
默认密码：*****
514. 执行以下命令，检查crontab是否被注销。
cat /etc/crontab | grep zookeeper
[root@SMN-KFK-ZK01 hermes]# cat /etc/crontab | grep zookeeper
*/1 * * * * root su hermes -c /opt/hermes/service/zookeeper/bin/start_zookeeper.sh >/dev/null 2>&1
当回显信息最前面有“#”，表示crontab被注销。
- 是，请删除“#”取消注销。
- 否，执行9。
515. 检查配置文件zoo.cfg中的server.*参数配置，是否配置为该zookeeper集群的三个IP和8880:7770端口.
cd /opt/hermes/service/zookeeper/conf
vi zoo.cfg
#autopurge.purgeInterval=1
server.0=*************:8880:7770
server.1=*************:8880:7770
server.2=*************:8880:7770
516. 切换到hermes用户下，手动执行启动进程命令。
su - hermes
sh /opt/hermes/service/zookeeper/bin/start_zookeeper.sh
517. 重启完成后执行如下命令，检查zookeeper进程运行是否正常。
ps -ef|grep zoo.cfg
[root@SMN-KFK-ZK01 ~]# ps -ef|grep zoo.cfg
root      1169   929  0 16:37 pts/0    00:00:00 grep --color=auto zoo.cfg
hermes    9299     1  0 Apr25 ?        00:05:16 /opt/hermes/jre/bin/java -Dzookeeper.log.dir=/var/log/hermes/zookeeper -Dzookeeper.root.logger=INFO,ROLLINGFILE -cp /opt/hermes/service/zookeeper/bin/../build/classes:/opt/hermes/service/zookeeper/bin/../build/lib/*.jar:/opt/hermes/service/zookeeper/bin/../lib/wcc_log-1.2.4.jar:/opt/hermes/service/zookeeper/bin/../lib/slf4j-log4j12-1.6.1.jar:/opt/hermes/service/zookeeper/bin/../lib/slf4j-api-1.6.1.jar:/opt/hermes/service/zookeeper/bin/../lib/netty-3.7.0.Final.jar:/opt/hermes/service/zookeeper/bin/../lib/log4j-1.2.16.jar:/opt/hermes/service/zookeeper/bin/../lib/jline-0.9.94.jar:/opt/hermes/service/zookeeper/bin/../zookeeper-3.4.6.jar:/opt/hermes/service/zookeeper/bin/../src/java/lib/*.jar:/opt/hermes/service/zookeeper/bin/../conf: -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.local.only=false org.apache.zookeeper.server.quorum.QuorumPeerMain /opt/hermes/service/zookeeper/bin/../conf/zoo.cfg
[root@SMN-KFK-ZK01 ~]#
- 是，执行12。
- 否，请联系技术支持工程师协助解决。
518. 等待10分钟，检查告警是否已自动清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
********2 ALM-2000493 证书异常
##### 告警解释
自定义监控项，系统每隔一个小时检查一次消息通知服务所使用的证书，证书过期、有效期在30天内、证书无效都会产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 2000493 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 进程监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
证书过期后系统不可用，无法提供服务，需尽快处理异常。
##### 可能原因
- 阈值为1：证书即将过期。
- 阈值为2：证书过期。
- 阈值为3：证书无效或者证书不存在。
前提条件
- 已获取.jks格式的证书和证书加密密码。证书名称为smn_ps.jks。
- JKS中存在别名（alias）为silvan_server的服务证书。
- 已准备跨平台远程访问工具，如“PuTTY”。
- 已准备网络传输工具，如“WinSCP”。
- 已获取ps节点的管理IP地址、hermes和root帐户的登录密码。
“root”帐户的默认密码是“*****”，“hermes”帐户的默认密码是“*****”。可以使用默认jks证书库的默认密码：*****。
操作步骤
519. 使用网络传输工具（例如“WinSCP”），将证书上传到ps节点的“/opt/hermes/publishServer/config”路径下。
520. 在本地使用解压缩工具解压部署包。
加密工具“safetool-x.x.x-release.tar.gz”在解压后的部署包中。
521. 使用网络传输工具（例如“WinSCP”），将加密工具“safetool-x.x.x-release.tar.gz”上传到ps节点的“ /opt/hermes”路径下
522. 使用PuTTY，以“hermes”帐户登录ps节点。
523. 执行以下命令，切换到root用户。
sudo su - root
默认密码：*****
524. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
若使用默认密码*****，请跳过7~13。
525. 执行以下命令导入环境变量。
source /etc/profile
526. 执行以下命令，进入证书库所在目录。
cd /opt/hermes/publishServer/config
527. 执行以下命令，修改证书权限。
chmod 400 smn_ps.jks
chown hermes:hermes smn_ps.jks
528. 执行以下命令，加密证书密码。
cd /opt/hermes/safetool/bin
sh safetool -b
显示以下信息：
Please input the path of root key:
529. 输入“/opt/hermes/publishServer/config/rootkey”，按“Enter”。
显示以下信息：
Please input your password:
530. 输入帐户证书加密使用的密码，按“Enter”。
显示以下信息：
Please input your password again:
531. 再次输入帐户证书加密使用的密码，按“Enter”。
生成密码的密文形式，需记录该密文。
532. 执行以下命令，使用vi编辑器打开rest.properties文件。
vi /opt/hermes/publishServer/config/rest.properties
533. 按“i”进入编辑状态。
534. 修改配置文件中的参数“server.rest.ssl.keystore”。
新jks证书库与原证书库名称相同，都是smn_ps.jks，则不需要修改。将参数“server.rest.ssl.keystore”的值修改为“config/smn_ps.jks”，即证书文件的保存路径。
535. 按“Esc”，输入:wq，按“Enter”。
保存修改并退出vi编辑器。
536. 执行以下命令，删除加密工具。
cd /opt/hermes
rm -rf safetool*
537. 执行以下命令删除原有证书：
rm -rf /opt/hermes/publishServer/config/smn_ps.jks
538. 执行以下命令，重启publishServer。
sh /opt/hermes/publishServer/bin/publishServer_monitor.sh restart
publishServer可以正常重启时，表示替换证书成功。
##### 参考信息
无。
< 上一节
5.2.17 公共组件
5.2.17.1 ECS UI