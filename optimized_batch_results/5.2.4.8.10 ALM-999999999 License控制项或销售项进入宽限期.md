# 5.2.4.8.10 ALM-999999999 License控制项或销售项进入宽限期

##### 告警解释
当License控制项或销售项进入宽限期，产生该告警。申请新的License文件后，更新系统中的License文件，并且License控制项或销售项在有效期内时，该告警会自动清除。
- 当License控制项或销售项的截止日期与license文件截止日期一致时，不产生该告警。
- License控制项指License资源控制项和功能控制项。
- License不合法时，系统提供了一个宽限期，宽限期的天数参见License文件。宽限期后该License将无法使用。
- 该告警仅适用于3.00版本的License文件。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 999999999 | 重要 | 业务质量告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 产品名称 | 产品名称。 |
| SALE | 销售项ID。 |
| RESOURCE | 资源项ID。 |
| FUCTION | 功能项ID。 |
##### 对系统的影响
该告警发生时，若不及时处理，该License控制项或销售项将会不可用。
##### 可能原因
当前系统时间距离License控制项或销售项截止时间的天数小于宽限期。
##### 处理步骤
1. 在主菜单选择“系统管理 > 系统设置 > License管理”。
2. 在“License信息”页面中，确认当前系统时间距离License控制项或销售项宽限截止日期的天数是否小于宽限期。
- 是，执行3。
- 否，联系华为技术支持工程师。
3. 申请License文件。
- 获取License失效码。
- 执行此操作需要具备“失效License”的操作权限。
- 失效License的操作不可恢复。设置当前License失效后，该License进入宽限期，宽限期参见License文件。宽限期后该License将无法使用。
- 在左侧导航树中选择“License文件”。
- 单击目标License文件“操作”列的“失效License”。
- 在“确认”对话框中，单击“确定”。
- 在License文件列表中，单击目标License文件的查看并获取“失效码”。
- 联系华为技术支持工程师使用获取的License失效码申请新的License文件。
4. 更新License文件。
- 在左侧导航树中选择“更新License”。
- 在“更新License”页面中，单击“License文件”后面的。
- 选择已申请的License文件，单击“打开”。
- 单击“上传”，查看License更新前后的结果对比。
- 单击“应用”，立即应用新的License文件。
5. 查看本告警是否已清除。
- 是，告警处理结束。
- 否，请收集上述告警处理过程中的信息，联系华为技术支持工程师。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。
< 上一节
5.2.4.9 远程通知管理