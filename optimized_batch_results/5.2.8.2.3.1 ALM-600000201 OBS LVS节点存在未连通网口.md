# *******.3 ALM-600000201 OBS LVS节点存在未连通网口

*******.3.1 ALM-600000201 OBS LVS节点存在未连通网口
告警解释
当OBS LVS节点存在未连通网口的时候，上报此警告。
告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 600000201 | 重要 | 是 |
告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 区域 | 产生告警信息的区域名称。 |
| 定位信息 | 云服务 | 产生告警信息的云服务名称。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 模板ID | 监控模板唯一ID。 |
| 附加信息 | 指标名称 | 产生告警信息的指标名称。 |
| 附加信息 | 监控类型 | 业务监控。 |
| 附加信息 | 详细信息 | 最近几个周期的监控数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
对系统的影响
影响OBS LVS服务的可靠性。
可能原因
- 网口未启动。
- 网络闪断等原因导致交换机端口Down。
- 网口连线存在问题。
- 网卡故障。
- 接口频繁闪断触发交换机链路振荡保护功能。
处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 当前告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
- 可能原因：网口未启动。
- 使用“PuTTY”工具，通过4中获取的节点IP地址登录产生告警的节点。
默认帐号：udslvs，默认密码：*****。
- 执行以下命令，并按提示输入“root”用户的密码，默认密码：*****，切换至“root”用户。
sudo su
- 执行以下命令查看并获取bond中的网口名。
cat /proc/net/bonding/bond0
- 使用ifconfig ethx up命令启动bond中的网口。其中ethx为5.c获取的网口名。
- 执行ifconfig ethx，查看输出结果。其中ethx为5.c获取的网口名。
- 出现提示UP BROADCAST RUNNING MULTICAST、UP BROADCAST RUNNING SLAVE MULTICAST或者UP BROADCAST RUNNING MASTER MULTICAST表示网口已正常启动，请执行5.f。
- 未出现此提示，网口存在异常，请执行6。
- 查看告警是否在下一个监控周期（约3分钟后）消除。
- 是，处理结束。
- 否，请执行6。
- 可能原因：网络闪断等原因导致交换机端口Down。
- 登录交换机，在故障接口所连交换机接口的接口视图中，先执行命令shutdown，然后再执行命令undo shutdown。
- 查看告警是否在下一个监控周期（约3分钟后）消除。
- 是，处理结束。
- 否，请执行7。
- 可能原因：网口连线存在问题。
- 检查网口物理连接，找到故障网口，将告警网口的网线重新插拔一下，确保网线两头均插紧。检查网口连接指示灯是否常亮。
- 是，请执行7.d。
- 否，请执行7.b。
- 更换网线。检查网口连接指示灯是否常亮。
- 是，请7.d。
- 否，请执行7.c。
- 如果存在光模块，更换光模块。检查网口连接指示灯是否常亮。
- 是，请7.d。
- 否，请执行8。
- 查看告警是否在下一个监控周期（约3分钟后）消除。
- 是，处理结束。
- 否，请执行8。
- 可能原因：网卡故障。
- 关闭该故障设备。
- 设备已经关闭后，拔掉该设备电源。
- 取下顶部外壳螺丝，打开顶部外壳，取下网卡螺丝，更换网卡，插紧螺丝，盖好顶部外壳，插上网线，然后将设备上电。
该操作属于高危操作，请在操作前联系技术支持工程师。
- 重新检查网口，查看网卡状态是否正常。
- 是，请执行8.e。
- 否，请执行9。
- 查看告警是否在下一个监控周期（约3分钟后）消除。
- 是，处理结束。
- 否，请执行9。
- 可能原因：接口频繁闪断触发交换机链路振荡保护功能。
- 登录交换机，在故障接口所连交换机接口的接口视图中，依次执行命令shutdown和undo shutdown，或者执行命令restart，重启接口，具体操作命令参见对应型号交换机的产品文档。
- 查看告警是否在下一个监控周期（约3分钟后）消除。
- 是，处理结束。
- 否，请执行10。
- 如果仍然存在故障，联系技术支持工程师。
参考信息
无。