# ********.7 ALM-48110 未知告警

##### 告警解释
API网关GaussDB服务出现异常，apimgr无法访问GaussDB。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48110 | 重要 | 通信告警 |
##### 告警参数
| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Fault_Component | 故障组件名称 |
| 定位信息 | Fault_Node | 故障节点IP地址 |
| 定位信息 | Source_Component | 告警源组件 |
| 定位信息 | Source_Node | 告警源节点IP地址 |
| 附加信息 | Alarm_Reason | 告警原因 |
##### 对系统的影响
网关管理面告警功能无法使用。
##### 可能原因
- keepalived服务出现异常。
- 数据库浮动IP地址丢失。
- GaussDB服务出现故障或GaussDB主备服务都停用。
##### 1. 处理步骤
##### 处理步骤
2. 查看告警的定位信息。
- 以admin帐号登录ManageOne运维面。
登录地址：https://ManageOne运维面主页的访问地址:31943，例如，https://oc.type.com:31943。默认密码：*****。
- 在页面上方的菜单栏中，选择“集中告警 > 当前告警”。
- 在告警列表中，找到待处理的告警记录。单击告警对应的“名称”列，弹出“告警详情和处理建议”窗口。
- 在“告警详情和处理建议”窗口的“基本信息”列表中，查看告警的定位信息。
- Source_Node：表示告警源节点IP地址。
- Fault_Node：表示故障节点IP地址。
如果未能找到节点信息，返回告警列表，单击告警前的“>”展开详细信息，在“被汇聚告警”页签中可找到告警的定位信息。
3. 在该环境的参数信息汇总文件《xxx_export_all_CN.xlsm》中的“2.1 工具生成的IP参数”页签，查找APIGWZK01节点对应的IP地址，使用PuTTY登录该节点。
登录HUAWEI CLOUD Stack Deploy Web界面，在“工程管理”页面单击对应的工程名称。选择“配置工程参数”，单击“参数汇总导出”可获取参数信息汇总文件《xxx_export_all_CN.xlsm》。
默认帐号：paas，默认密码：*****。
4. 执行以下命令，先切换到root用户，再切换到apigw_db用户。
sudo su - root
默认密码：*****。
su - apigw_db
5. 执行以下命令，防止会话超时退出。
TMOUT=0
6. 执行以下命令，检测是否以连接到GaussDB。
gsql -p 5432 -h PUB-DB01节点的IP地址 -d console -U apigw -W GaussDB数据库密码
PUB-DB01节点的IP地址：在文件《xxx_export_all_CN.xlsm》中的“2.1 工具生成的IP参数”页签，查找到的PUB-DB01对应的IP地址。
GaussDB数据库默认密码：*****。
- 连接正常 => 6
SSL connection (cipher: AES256-SHA, bits: 256)
- 连接失败 => 8
7. 执行以下命令，切换到apigw_apimgr用户。
exit
su - apigw_apimgr
8. 执行以下命令，重启apimgr组件。该重启操作对系统本身无不良影响。
sh /opt/apigateway/apimgr/shell/restart.sh
显示“xxx start successfully”，表示组件启动成功。
9. 查看告警是否清除。
- 是 => 处理完毕
- 否 => GaussDB服务故障，执行9。
10. 查看数据库节点状态，并联系技术支持。
- 使用PuTTY登录PUB-DB01节点的IP地址。
用户名：fsp。默认密码：*****。
- 执行如下命令，切换至root用户。
sudo su
默认密码：*****。
- 执行如下命令，查看数据库状态。
service had query
回显信息类似如下：
[root@PUB-DB01 fsp]# service had query
NODE                     ROLE           PHASE           RESS            VER             START
PUB-DB01(PUB-DB01)       active         Actived         normal          V100R001C01     2019-07-28 17:27:03
PUB-DB02(PUB-DB02)       standby        Deactived       normal          V100R001C01     2019-07-28 17:27:13
--------------------------------------------------------------------------------------------------------
ID    RES                      STAT            RET             TYPE
PUB-DB01(PUB-DB01):      1     exfloatip                Normal          Normal          Single_active
2     gaussDB                  Normal          Active_normal   Active_standby
PUB-DB02(PUB-DB02):      1     exfloatip                Normal          Abnormal        Single_active
2     gaussDB                  Normal          Standby_normal  Active_standby
- 将数据库状态信息截图保存，并联系技术支持。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。