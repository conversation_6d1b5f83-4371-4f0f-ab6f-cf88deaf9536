# ********* ALM-2000463 GaussdbHA上传远端备份服务器失败

********* ALM-2000463 GaussdbHA上传远端备份服务器失败
告警解释
数据库备份上传到sftp服务器失败时会触发此告警。
告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 2000463 | 重要 | 是 |
告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 进程监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
对系统的影响
数据库备份上传sftp服务器失败会导致数据库发生数据异常，出现严重故障时，没有可用的恢复数据。
可能原因
远端存储异常等原因。
处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
5. 使用“PuTTY”工具，通过4中获取的节点IP地址登录产生告警的节点。
默认帐号：hermes，默认密码：*****。
6. 执行以下命令，并按提示输入“root”用户的密码，默认密码：*****，切换至“root”用户。
sudo su
7. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
8. 执行如下命令检查备份失败的原因。
cat /home/<USER>/dbAlarmData/backup_zabbix_alarm
- 执行结果回显信息如下所示，则表示备份文件上传远端存储失败。
b0(backup success)u1(upload fail)
请检查当前/opt/gaussdb/ha/tools/backupAndRestore/Upload_Server.cfg配置文件中是否已配置远端存储。
- 如下所示，即FTP_SERVER_IP为********，则表示未配置远端存储。请参考手动备份数据章节配置远端存储。
- [root@I-SMN-PS-NS-DB-MEM01 ~]# cat /opt/gaussdb/ha/tools/backupAndRestore/Upload_Server.cfg
- # UDS
- endpoint:********
- ak:U2FsdGVkX18arqCYf+f2gEEuzP/g1d7D5DXZPa2vjUGMh6gYoNLI06vP2DRVAvty
- sk1:U2FsdGVkX18l2Gsyj3GjTGVfYOEppi5FEQeP6uuS2c/eZWm6jpxNnLjpSEgV0Bp1
- sk2:U2FsdGVkX18kUFXApHuaaE6nqycG2GR/aB2UijOg09MoUoSXbaGF/ntIR30+/G61
- bucket:op-svc-i-backup
- # FTP
- FTP_SERVER_IP:********
- FTP_SERVER_PORT:22
- FTP_SERVER_USER:root
- FTP_SERVER_PASSWD:U2FsdGVkX1/6ETkW0Ii4u2nrquYuIrq/wyqWI3oTmaA=
- FTP_SERVER_FILEPATH:/opt/uploadFTPDir
[root@I-SMN-PS-NS-DB-MEM01 ~]#
- 如果回显信息中FTP_SERVER_IP为非********，则表示已配置远端存储。请检查配置是否正确。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
参考信息
无。