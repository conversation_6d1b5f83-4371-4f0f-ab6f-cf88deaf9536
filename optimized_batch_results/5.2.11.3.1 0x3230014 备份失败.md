# ********.1 0x3230014 备份失败

********.1 0x3230014 备份失败
告警解释
系统配置数据备份至SFTP服务器失败。
告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230014 | 警告 | 是 |
告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 服务器IP地址 | 备份配置数据的服务器IP地址 |
对系统的影响
无法自动备份配置数据。
可能原因
- 管理服务器与SFTP服务器间网络异常。
- 配置访问SFTP服务器的用户名及密码错误。
- 配置访问SFTP服务器的用户权限不足。
- SFTP服务器剩余的备份系统配置数据空间不足。
处理步骤
1. 通过告警详细信息当中的服务器IP地址登录BCManager服务管理界面(登录链接：https://IP:9443/，9443为默认端口)，选择“设置 > 数据维护 > 系统配置数据”获取到SFTP IP地址，用户名和端口。
2. 使用PuTTY，以告警详情信息中的服务器IP地址登录到对应节点。
默认帐号：DRManager，默认密码：*****。
3. 然后执行su - root命令切换到root帐号。
root帐号的默认密码为*****。
4. 根据1获取到的SFTP IP地址，执行ping SFTP IP地址命令，检查是否可以ping通SFTP IP地址。
- 是，请转5。
- 否，请修复主备节点间的网络连接，在1当中的“系统配置数据”界面手动执行备份操作后，如果已清除，流程结束，否则请转5。
5. 根据4的执行结果，查看是否存在丢包的现象。
- 是，联系管理员提高网络性能质量，保证管理服务器与SFTP服务器之间通信稳定，处理结束。
- 否，执行6。
6. 使用FTP客户端工具，通过用户和密码登录SFTP服务器，检查用户名和密码是否正确。
- 是，请转7。
- 否，请重新配置访问SFTP服务器的用户名及密码，具体操作参考1中的“ 系统配置数据”界面。配置成功后，在1当中的“系统配置数据”界面手动执行备份操作后，如果已清除，流程结束，否则请转7。
7. 使用FTP客户端上传文件，确认是否上传成功。
- 是，请转8。
- 否，请登录SFTP服务器，并重新配置用户的读写权限和系统配置数据空间。配置完成后，在1当中的“系统配置数据”界面手动执行备份操作后，如果已清除，流程结束，否则请转8。
8. 请联系技术支持工程师协助解决。
参考信息
无。