# ******* ALM-1200025 ntp进程故障

******* ALM-1200025 ntp进程故障
告警解释
系统每隔60秒检查一次NTP进程是否存在，如果不存在，产生此告警。
告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200025 | 紧急 | 是 |
告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源系统 | 告警来源。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 此条告警信息最近发生时间。 |
| 定位信息 | 产生告警信息的定位信息。 |
| 附加信息 | 告警附加信息。 |
对系统的影响
系统时间不同步，影响计费及问题定位，需尽快处理异常。
可能原因
NTP进程异常。
处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 单击“登录”。
3. 在页面上方的菜单栏，选择“集中告警”。
4. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
5. 在“告警详情”页的“定位信息”中，“主机IP”即告警节点IP地址。
6. 使用“PuTTY”，登录告警节点。
默认帐号： vpc，默认密码： *****
7. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
8. 执行下面命令，切换到root用户。
sudo su - root
9. 执行下面命令，检查NTP进程是否存在。
ps -ef |grep ntp
- 是，执行10。
- 否，执行12。
回显如下所示时，表示NTP进程存在。回显具体内容与系统相关，请以实际为准。
[root@PUB-SRV01 vpc]# ps -ef | grep ntp
ntp      10898     1  0 Jun20 ?        00:00:02 /usr/sbin/ntpd -u ntp:ntp -g
10. 执行下面命令，重启NTP进程。
service ntp restart
回显如下所示时，表示NTP进程重启成功。回显具体内容与系统相关，请以实际为准。
[root@PUB-SRV01 vpc]# service ntp restart
Redirecting to /bin/systemctl restart ntp.service
11. 检查NTP进程是否重启成功。
- 是，执行14。
- 否，请联系技术支持工程师协助解决。
12. 执行下面命令，启动NTP进程。
service ntp start
回显如下所示时，表示NTP进程启动成功。回显具体内容与系统相关，请以实际为准。
[root@PUB-SRV01 vpc]# service ntp start
Redirecting to /bin/systemctl start ntp.service
13. 检查NTP进程是否启动成功。
- 是，执行14。
- 否，请联系技术支持工程师协助解决。
14. 等待10分钟，检查告警是否已自动清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
参考信息
无。