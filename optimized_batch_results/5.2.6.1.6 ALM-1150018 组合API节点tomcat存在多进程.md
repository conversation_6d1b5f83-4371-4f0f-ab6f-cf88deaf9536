# *******.6 ALM-1150018 组合API节点tomcat存在多进程

##### 告警解释
同时存在不止一个tomcat进程时触发该告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1150018 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 告警名称 | 告警名称 | 此条告警信息的名称 |
| 来源系统 | 来源系统 | 对接系统的名称 |
| IP地址 | IP地址 | 产生告警信息的业务服务器的IP地址 |
| 首次发生时间 | 首次发生时间 | 此条告警信息首次发生时间 |
| 定位信息 | 服务 | 产生此告警的服务名 |
| 定位信息 | 微服务 | 产生告警的微服务名 |
##### 对系统的影响
系统服务不可用。
##### 可能原因
手动启动进程。
##### 处理步骤
1. 观察1分钟，查看该告警是否清除。
- 是，处理结束。
- 否，执行2。
2. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
3. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
4. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
5. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
6. 使用PuTTY，登录5中确认的虚拟机。
默认帐号：apicom，默认密码：*****。
7. 执行以下命令 ，查看当前所有tomcat进程。
ps -ef | grep tomcat | grep apicom
apicom 3087 1 2 Apr25 ? 00:39:02 /opt/common/jre/bin/java
回显中的“3087”即为tomcat进程的进程号。
8. 执行以下命令，停止所有tomcat进程。
kill -9 进程号
其中进程号为7中获取的进程号。
9. 执行以下命令，启动进程。
sh /opt/apicom/tomcat/bin/startup.sh
10. 观察1分钟，查看该告警是否清除。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
< 上一节
******* 云硬盘
*******.1 1060036 evs周期性检测cinder连通性失败
##### 告警解释
EVS周期性检测cinder连通性失败。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1060036 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 告警ID | 告警ID | 告警ID |
| 通知类型 | 通知类型 | 告警 |
| 告警级别 | 告警级别 | 告警的严重级别：<br>1 紧急<br>2 重要<br>3 次要<br>4 提示 |
| 最近发生时间 | 最近发生时间 | 此条告警信息最近一次发生时间。 |
| 定位信息 | 云服务 | 云服务名 |
| 定位信息 | 主机地址 | 发送告警的主机地址 |
| 附加信息 | 云服务 | 云服务名 |
| 附加信息 | 服务 | 服务名 |
| 附加信息 | Cinder URL | 无法连通的Cinder节点的URL |
| 附加信息 | 错误信息 | 错误信息 |
##### 对系统的影响
EVS与Cinder连接失败，EVS服务不可用。
##### 可能原因
- Cinder 服务故障。
- Cinder的网络故障。
- Cinder URL的配置错误。
##### 处理步骤
11. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
12. 在上方导航栏选择“集中告警 > 当前告警”，单击“过滤”，通过告警名称过滤该告警。
13. 查询以下告警信息：
- IP地址
- 定位信息（主机地址）
- 最后发生时间
- 附加信息
14. 查询附加信息，若ErrorMessage中包含“statusCode is Unauthorized (401) - The request requires user authentication”，说明组合API节点配置帐号op_svc_evs在IAM中不存在或节点配置的帐号密码错误，请联系IAM运维人员，查看IAM中是否存在op_svc_evs帐号。
- 如果op_svc_evs帐号不存在，请向IAM运维人员提变更，新增该帐号并获取帐号密码；如果帐号存在，请获取该帐号的密码。
- 获取到帐号密码之后，根据修改配置文件中对接其他组件或服务的帐户密码章节，修改组合API节点iam.evs.password配置项的值。
- 配置修改成功后，等待6分钟后，如果告警已清除，则问题已解决，否则执行5。
15. 查询附加信息，检查Cinder URL是否正确。
- 若ErrorMessage中包含“java.net.ConnectException: Connection refused”、“java.net.NoRouteToHostException: No route to host (Host unreachable)”、“java.net.UnknownHostException:”、“Name or service not known”等描述与Cinder间连接不可达的信息，说明Cinder URL不正确，请申请配置变更，变更该节点的Cinder URL，具体操作可参考如下步骤：
- Region Type I场景，使用PuTTY，以“Cascading-Reverse-Proxy”字段（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）对应的IP地址登录级联层FusionSphere OpenStack节点。
Region Type II或Region Type III场景，使用PuTTY，以“Reverse-Proxy”（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）字段对应的IP地址登录FusionSphere OpenStack节点。
默认帐号：fsp，默认密码：*****
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行以下命令，导入环境变量。
source set_env
导入过程中，请输入1，选择环境变量“openstack environment variable (keystone v3)”，默认密码为*****。
- 执行以下命令查询cinder安装部署时的地址，服务名为cinder，接口属于public范围的URL地址为cinder真实的地址：
openstack endpoint list | grep cinder
- 使用PuTTY，依次以“CPT-SRV01”字段（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）对应的IP地址登录“CPT-SRV01”节点，以“CPT-SRV02”字段（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）对应的IP地址登录“CPT-SRV02”节点。
默认帐户：apicom，默认密码：*****
- 执行以下命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
sudo su root
- 执行以下命令查看到云硬盘配置文件：
vim /opt/apicom/tomcat/taskmgr/WEB-INF/classes/taskmgr-config.properties
在配置文件中查找“cinder.endpoint.publicurl”配置项的值，即EVS配置的cinder地址：
对比两者，若不一致，取cinder真实的地址在配置文件中编辑更新该配置项。
- 若ErrorMessage中包含“503 Service Unavailable”、“No server is available to handle this request.”等描述服务不可用的信息，说明Cinder API层的服务异常，无法响应请求，请联系FusionSphere OpenStack Cinder运维人员定位并解决问题。
- 若Cinder URL正确，请参考典型Cinder问题定位指导定位问题，如果还无法解决请将定位信息提供给FusionSphere OpenStack Cinder运维人员定位并解决问题。
16. 问题解决并等待6分钟后，告警将自动清除。
##### 参考信息
无。
*******.2 修改配置文件中对接其他组件或服务的帐户密码
操作场景
为了确保组合API与对接的组件或服务正常通信，当对方修改了对接的帐户密码，用户需要同步修改组合API业务节点的配置文件中记录的对接组件或服务的帐户密码。
当以下组件或服务修改了帐户密码时，需要同步修改组合API业务节点的配置文件中记录的信息。
17. ServiceOM的告警对接帐户
18. IAM对接帐户
前提条件
19. 已准备跨平台远程访问工具，如“PuTTY”。
20. 已获取对接组件或服务上配置的对接帐户密码。
21. 已获取组合API业务节点的管理IP地址，apicom帐户的登录密码。
操作步骤
22. 使用PuTTY，依次以“CPT-SRV01”字段（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）对应的IP地址登录“CPT-SRV01”节点，以“CPT-SRV02”字段（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）对应的IP地址登录“CPT-SRV02”节点。
默认帐户：apicom，默认密码：*****
23. 执行以下命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
sudo su root
24. 执行以下命令，运行加密工具。
cd /opt/apicom/tomcat/taskmgr/WEB-INF/classes/tools
source /etc/profile
java -jar wcc_crypter.jar -t encrypt 用户密码
25. 输入重置后的对接组件或服务的对接密码，按“Enter”，系统会自动生成密码密文。
请保存密文。
26. 执行以下命令，打开配置文件。
vi /opt/apicom/tomcat/taskmgr/WEB-INF/classes/taskmgr-config.properties
27. 按“i”进入编辑状态。
28. 修改配置文件中的参数。
修改OperationCenter的告警对接帐户的密码，将参数“alarm.value”修改为加密后的密文。
修改IAM对接帐户的密码，将参数“iam.auto.password”修改为加密后的密文。
29. 按“Esc”，输入:wq，按“Enter”。
保存修改并退出vi编辑器。
30. 执行以下命令，查询Tomcat进程ID。
ps -ef | grep tomcat
31. 执行以下命令，根据9查询到的Tomcat进程ID，停止Tomcat进程。
kill -9 $PID
例如查询到的Tomcat进程ID为“62666”，则将“$PID”替换为“62666”。
32. 执行以下命令，重启服务。
cd /opt/apicom/tomcat/bin
sh startup.sh
zabbix监控服务处于开启状态时，如果监测到tomcat进程停止，会自动重启tomcat进程，无须手动执行本步骤重启tomcat。
*******.3 典型Cinder问题定位指导
< 上一节
*******.3.1 排查FusionSphere OpenStack
Region Type I场景，本章节指导排查级联层FusionSphere OpenStack。
Region Type I场景，cinder分为级联层和被级联层。当产生错误的时候，需要对级联层和被级联层依次进行排查，主要方式为搜索错误日志，根据具体错误信息进行分析处理。
Region Type II，Region Type III场景，本章节指导排查FusionSphere OpenStack。
33. 若是cinder模块发生错误，根据日志即可定位错误原因。
34. 若是环境配置错误，参考文档手册，其中列出了常见问题的处理方法。
35. 若是周边模块产生问题，则将日志信息传递给相关人员协同定位错误原因。
本文重点探讨cinder模块中错误的情形。
*******.3.1.1 查询对应卷信息
36. Region Type I场景，使用PuTTY，以“Cascading-Reverse-Proxy”字段（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）对应的IP地址登录级联层FusionSphere OpenStack节点。
Region Type II或Region Type III场景，使用PuTTY，以“Reverse-Proxy”（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）字段对应的IP地址登录FusionSphere OpenStack节点。
默认帐号：fsp，默认密码：*****
37. 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
38. 执行以下命令，导入环境变量。
source set_env
导入过程中，请输入1，选择环境变量“openstack environment variable (keystone v3)”，默认密码为*****。
39. 执行如下命令，查询卷详情。
cinder show volume_id
- 若返回“Service Unavailable (HTTP 503)”，如图1所示，说明cinder-api服务未启动。
- 执行如下命令，启动cinder-api服务，如图2所示，启动服务大概需要几十秒时间，请稍作等候。
cps host-template-instance-operate --action start --service cinder cinder-api
- 执行如下命令，查看服务的状态。
cps template-instance-list --service cinder cinder-api
- 若仍旧未启动，查看/var/log/fusionsphere/component/cinder-api/cinder-api_*.log日志，以及/var/log/fusionsphere/component/cinder-apiControl/cinder-apiControl_*.log启动日志，排查失败原因。
- 服务正常后，执行4。
图1 Service Unavialable
图2 启动cinder-api服务
- 若回显显示卷详情，则说明该卷未被删除。
执行如下命令，查询host的详细信息，如图3所示。
cinder show volume_id | zgrep host
图3 host信息
- 若回显信息类似图4，则说明该卷已被删除，执行以下步骤在数据库查看host详细信息。
图4 卷被删除后的回显信息
- 执行如下命令，查询数据库所在节点的IP地址，即“omip”对应的IP地址。如图5所示。
cps template-instance-list --service gaussdb gaussdb
图5 数据库信息
- 使用PuTTY，通过4.a中查询到的IP地址（“status”为“active”的IP地址）登录数据库主节点。
默认帐号：fsp，默认密码：*****
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行如下命令，导入环境变量。
source set_env
导入过程中，请输入1，选择环境变量“openstack environment variable (keystone v3)”。
- 执行如下命令，登录数据库。
su gaussdba
- 执行如下命令，进入数据库。
gsql -d cinder
数据库的默认密码为*****。
- 执行如下命令，查询该volume_id的host信息，如图6所示。
select host from volumes where id = 'volume_id';
图6 volume_id的host信息
通过上述操作可以获得host的信息。
- 若host信息不为“None”，说明调度成功已选择AZ。
Region Type I场景，参考排查被级联层OpenStack继续在FusionSphere OpenStack被级联层进行错误定位。
Region Type II，Region Type III场景，参考查询cinder-volume日志查询cinder-volumer日志进行错误定位。
- 若host信息为“None”，表示未选择到AZ，说明cinder-api或cinder-scheduler报错，继续执行查看cinder-api日志进行错误定位。
*******.3.1.2 查看cinder-api日志
40. Region Type I场景，使用PuTTY，以“Cascading-Reverse-Proxy”字段（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）对应的IP地址登录级联层FusionSphere OpenStack节点。
Region Type II或Region Type III场景，使用PuTTY，以“Reverse-Proxy”（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）字段对应的IP地址登录FusionSphere OpenStack节点。
默认帐号：fsp，默认密码：*****
41. 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
42. 执行以下命令，导入环境变量。
source set_env
导入过程中，请输入1，选择环境变量“openstack environment variable (keystone v3)”，默认密码为*****。
43. 执行如下命令，查询所有cinder-api控制节点的IP地址，即“omip”对应的所有IP地址，如图1所示。
cps template-instance-list --service cinder cinder-api
图1 控制节点信息
44. 通过4中查询到的IP地址依次登录所有cinder-api控制节点。
默认帐号：fsp，默认密码：*****
- 由于控制节点是分布式部署，需要对每一个控制节点进行查询。
- 所有控制节点均有cinder-api日志，上层下发的请求可能在任何一个节点上处理。
45. 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
46. 执行以下步骤根据卷volume_id查找request id，定位处理请求的节点。
- 因为节点处理该请求是随机分配的，所以在每个一个控制节点上都要根据卷volume_id查找request id。
- request id格式为req-xxx。
- 执行如下命令，查看cinder-api文件目录，如图2所示。
ll /var/log/fusionsphere/component/cinder-api
图2 cinder-api文件目录
- 执行如下命令，通过volume_id搜索日志文件查找request id。根据错误发生的时间，匹配查找到的日志信息，获得request id，如图3所示。
zgrep volume_id /var/log/fusionsphere/component/cinder-api/*
图3 查找request id
- 执行如下命令，在该节点根据request id继续查询日志信息，根据ERROR字样，查看报错信息，如图4所示。
zgrep req_id /var/log/fusionsphere/component/cinder-api/*
- 找到报错信息，则根据日志信息解决问题。
- 没有找到报错信息。
- 若是创建空白卷，或者使用镜像创卷产生错误（备份、快照创卷不涉及cinder-scheduler过程），进入查看cinder-scheduler日志继续定位。
- Region Type I场景，若创建的不是空白卷，或者不是使用镜像创卷，进入排查被级联层OpenStack继续定位。
图4 报错信息
*******.3.1.3 查看cinder-scheduler日志
47. Region Type I场景，使用PuTTY，以“Cascading-Reverse-Proxy”字段（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）对应的IP地址登录级联层FusionSphere OpenStack节点。
Region Type II或Region Type III场景，使用PuTTY，以“Reverse-Proxy”（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）字段对应的IP地址登录FusionSphere OpenStack节点。
默认帐号：fsp，默认密码：*****
48. 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
49. 执行如下命令，导入环境变量。
source set_env
导入过程中，请输入1，选择环境变量“openstack environment variable (keystone v3)”，默认密码为*****。
50. 执行如下命令，查询所有cinder-scheduler控制节点。
cps template-instance-list --service cinder cinder-scheduler
51. 通过4中查询到的IP地址依次登录所有cinder-scheduler控制节点。
默认帐号：fsp，默认密码：*****
52. 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
53. 执行如下命令，在所有cinder-scheduler控制节点上，根据7中获得的request id搜索cinder-scheduler日志，根据错误发生的时间，找到ERROR字样，查看报错信息。
zgrep req_id /var/log/fusionsphere/component/cinder-scheduler/ *
54. 根据报错信息分析错误产生的原因并进行处理。
*******.3.1.4 查询cinder-volume日志
若日志查询到了卷对应的后端存储，即host字段不为None，说明cinder-volume组件报错。
操作步骤
55. Region Type I场景，使用PuTTY，以“Cascading-Reverse-Proxy”字段（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）对应的IP地址登录级联层FusionSphere OpenStack节点。
Region Type II或Region Type III场景，使用PuTTY，以“Reverse-Proxy”（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）字段对应的IP地址登录FusionSphere OpenStack节点。
默认帐号：fsp，默认密码：*****
56. 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
57. 如果该卷未被删除，执行如下命令，查询卷详情中的host信息，判断后端存储类型，如图1所示。
cinder show volume_id | zgrep host
图1 卷详情中的host信息
58. 如果该卷已被删除，则需要在数据库查询host信息，判断后端存储类型。
- 执行如下命令，查询数据库所在节点的IP地址，即“omip”对应的IP地址。如图2所示。
cps template-instance-list --service gaussdb gaussdb
图2 数据库信息
- 使用PuTTY，通过4.a中查询到的IP地址（“status”为“active”的IP地址）登录数据库主节点。
默认帐号：fsp，默认密码：*****
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行如下命令，导入环境变量。
source set_env
导入过程中，请输入1，选择环境变量“openstack environment variable (keystone v3)”。
- 执行如下命令，登录数据库。
su gaussdba
- 执行如下命令，进入数据库。
gsql -d cinder
数据库的默认密码为*****。
- 执行如下命令，查询该volume_id的host信息，如图3所示。
select host from volumes where id = 'volume_id';
图3 volume_id的host信息
59. 通过host的字段信息查询所有cinder-volume控制节点的IP地址，即使“omip”对应的IP地址。如图4所示。
cps template-instance-list --service cinder cinder-volume-xxx
如果4.g中查询的后端存储为cinder-vrm001，则cinder-volume-xxx为cinder-volume-vrm001；如果查询到的是cinder-kvm001，则cinder-volume-xxx为cinder-volume-kvm001。
图4 后端存储节点
60. 通过5中查询到的IP地址依次登录所有cinder-volume控制节点。
默认帐号：fsp，默认密码：*****
61. 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
62. 执行如下命令，导入环境变量。
source set_env
导入过程中，请输入1，选择环境变量“openstack environment variable (keystone v3)”，默认密码为*****。
63. 执行如下命令，查询每个节点上的cinder-volume-xxx日志。
zgrep req_id /var/log/fusionsphere/component/cinder-volume-xxx/*
req_id 为7中查询到的request id。
cinder-volume-xxx与5中的配置相同。
64. 依据查询到的日志信息进行分析和处理。
< 上一节
*******.3.2 排查被级联层OpenStack
查看被级联层cinder-api日志
查询被级联层cinder-scheduler日志
查询被级联层cinder-volume日志
< 上一节
*******.3.2.1 查看被级联层cinder-api日志
65. 使用PuTTY，以“Cascaded-Reverse-Proxy”字段（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）对应的IP地址登录被级联层FusionSphere OpenStack节点。
默认帐号：fsp，默认密码：*****
66. 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
67. 执行如下命令，导入环境变量。
source set_env
导入过程中，请输入1，选择环境变量“openstack environment variable (keystone v3)”，默认密码为*****。
68. 执行如下命令，根据级联层volume_id，查询被级联层的卷详情。
通常级联层和被级联层的volume_id是相同的。
cinder show 级联层volume_id
级联层volume_id即为在ManageOne运营面显示的磁盘ID。
- 若能够查询到卷详情，执行如下命令，查看该卷的host信息。
cinder show 级联层volume_id | zgrep host
- 若能够查询到host信息，即host字段不为None，如图1所示，则说明调度过程没有错误，进入查询被级联层cinder-volume日志。
- 若不能够查到host信息，则执行5。
图1 查询后端存储
- 如果未查询到卷详情信息，则说明级联层volume_id可能与被级联层volume_id不一致，或该卷已经被删除，此时需要查询数据库。
- 执行如下命令，查询数据库所在节点的IP地址，即“omip”对应的IP地址。如图2所示。
cps template-instance-list --service gaussdb gaussdb
图2 数据库信息
- 使用PuTTY，通过4.a中查询到的IP地址（“status”为“active”的IP地址）登录数据库主节点。
默认帐号：fsp，默认密码：*****
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行如下命令，导入环境变量。
source set_env
导入过程中，请输入1，选择环境变量“openstack environment variable (keystone v3)”。
- 执行如下命令，登录数据库。
su gaussdba
- 执行如下命令，进入数据库。
gsql -d cinder
数据库的默认密码为*****。
- 执行如下命令，查询被级联层的volume id，如图3所示。
select id from volumes where display_name = 'volume@级联层volume_id';
图3 查询数据库
- 执行如下命令，查询该被级联层卷对应的host信息。
select host from volumes where id = '被级联层volume_id';
- 若能够查询到host信息，即host字段不为None，则说明已经筛选到主机，进入步骤查询被级联层cinder-volume日志。
- 若不能够查到host信息，则执行5。
69. 执行如下命令，查询被级联层所有cinder-api控制节点的IP地址，即“omip”对应的IP地址。
cps template-instance-list --service cinder cinder-api
70. 通过5中查询到的IP地址依次登录所有cinder-api控制节点。
默认帐号：fsp，默认密码：*****
71. 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
72. 执行如下命令，查询每一个被级联节点上的cinder-api日志，以获取request id，request id的格式为req-xxx。
zgrep volume_id /var/log/fusionsphere/component/cinder-api/ *
volume_id 为4.g中查询到的被级联层volume id。
73. 执行如下命令，依据request id，查询该节点cinder-api日志，获取报错信息。
zgrep rep-id /var/log/fusionsphere/component/cinder-api/*
req_id 为7中查询到的request id。
如未定位错误，则继续查询被级联层cinder-scheduler日志。
*******.3.2.2 查询被级联层cinder-scheduler日志
仅在创建空白卷和从镜像创卷时有需要查询被级联层cinder-scheduler日志，从备份和快照创卷不涉及。
操作步骤
74. 使用PuTTY，以“Cascaded-Reverse-Proxy”字段（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）对应的IP地址登录被级联层FusionSphere OpenStack节点。
默认帐号：fsp，默认密码：*****
75. 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
76. 执行如下命令，导入环境变量。
source set_env
导入过程中，请输入1，选择环境变量“openstack environment variable (keystone v3)”，默认密码为*****。
77. 执行如下命令，查看所有cinder-scheduler控制节点。
cps template-instance-list --service cinder cinder-scheduler
78. 通过4中查询到的IP地址依次登录所有cinder-scheduler控制节点。
默认帐号：fsp，默认密码：*****
79. 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
80. 执行如下命令，通过request id查询每一个节点上的日志信息。
zgrep req_id /var/log/fusionsphere/component/cinder-scheduler/*
req_id 为7中查询到的request id。
81. 根据日志错误信息进行分析和处理。
*******.3.2.3 查询被级联层cinder-volume日志
若日志查询到了卷对应的后端存储信息，即host字段不为None，说明cinder-volume组件报错。
操作步骤
82. 使用PuTTY，以“Cascaded-Reverse-Proxy”字段（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）对应的IP地址登录被级联层FusionSphere OpenStack节点。
默认帐号：fsp，默认密码：*****
83. 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
84. 执行如下命令，导入环境变量。
source set_env
导入过程中，请输入1，选择环境变量“openstack environment variable (keystone v3)”，默认密码为*****。
85. 参考查询cinder-volume日志查询被级联层cinder-volume的日志进行定位分析。
< 上一节
******* 镜像服务
*******.1 告警参考
*******.1.1 ALM-1131007 ntp进程不存在
##### 告警解释
镜像转换节点的ntp进程不存在时，就会产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1131007 | 次要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源设备名称 | 产生告警信息的设备名称。 |
| 监控系统名称 | 产生告警信息的监控系统名称。 |
| 来源系统 | 告警来源。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 此条告警信息最近一次发生时间。 |
| 附加信息 | 告警附加信息。 |
##### 对系统的影响
影响镜像服务的功能正常运行，同时，可能导致磁盘中的数据随时会被删除。
##### 可能原因
网络故障、ntp进程异常、ntp服务器下电或故障等。
##### 处理步骤
86. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
87. 单击“登录”。
88. 在页面上方的菜单栏，选择“集中告警”，进入“集中告警”页面。
89. 在告警列表中，找到待处理的告警记录，单击“名称”列，弹出“告警详情和处理建议”窗口。
90. 查询以下告警信息：
- 告警ID
- 告警来源设备名称
##### - 在告警信息的可能原因中获取云服务器的IP地址
- 使用“PuTTY”工具，登录CPT-SRV01或CPT-SRV02节点。
登录地址：5中获取的附加信息的IP地址
默认帐号：sysadmin，默认密码：*****。
- 执行以下命令，并按提示输入“root”用户的密码，默认密码：*****，切换至“root”用户。
sudo su - root
- 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
- 执行以下命令，检查网络是否正常。
ping NTP服务器的IP地址
- 是，执行10。
- 否，请联系技术支持工程师协助解决。
- 执行以下命令，查看ntp进程是否存在。
ps -ef |grep ntp
- 是，执行11。
- 否，执行12。
- 执行以下命令，终止ntp进程。
kill -9 进程号
其中进程号为10中查询到的进程号。
系统会自动启动ntp进程，如过长时间未自动启动进程，执行12启动进程。
- 执行以下命令，启动进程。
service ntp restart
- 等待3分钟，观察告警通知是否消除？
- 是，任务结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
*******.1.2 ALM-1131009 tomcat多进程
##### 告警解释
当镜像转换服务器出现两个及以上的tomcat进程时，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1131009 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源设备名称 | 产生告警信息的设备名称。 |
| 监控系统名称 | 对接系统的类型。 |
| 来源系统 | 对接系统的名称。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 此条告警信息最近一次发生时间。 |
| 附加信息 | 告警附加信息。 |
##### 对系统的影响
影响用户创建镜像。
##### 可能原因
镜像转换服务器内部异常。
##### 处理步骤
91. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
92. 单击“登录”。
93. 在页面上方的菜单栏，选择“集中告警”，进入“集中告警”页面。
94. 在告警列表中，找到待处理的告警记录，单击“名称”列，弹出“告警详情和处理建议”窗口。
95. 查询以下告警信息：
- 告警ID
- 来源设备名称
- IP地址
96. 使用“PuTTY”工具，登录CPT-SRV01或CPT-SRV02节点。
登录地址：5中获取的附加信息的IP地址
默认帐号：sysadmin，默认密码：*****。
97. 执行以下命令，并按提示输入“root”用户的密码，默认密码：*****，切换至“root”用户。
sudo su - root
98. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
99. 执行以下命令，是否有多个tomcat进程。
ps -ef| grep tomcat| grep imcs
- 是，10。
- 否，12。
100. 执行以下命令，终止所有tomcat进程。
kill -9 进程号
其中进程号为9中查询到的进程号。
101. 执行以下命令，启动tomcat进程。
sh /opt/imcs/tomcat/bin/startup.sh
102. 等待3分钟，观察告警通知是否消除？
- 是，任务结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无
*******.1.3 ALM-1131011 tomcat进程不存在
##### 告警解释
镜像转换节点tomcat进程不存在，系统产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1131011 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源设备名称 | 产生告警信息的设备名称。 |
| 监控系统名称 | 对接系统的类型。 |
| 来源系统 | 对接系统的名称。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 此条告警信息最近一次发生时间。 |
| 附加信息 | 告警附加信息。 |
##### 对系统的影响
通过镜像文件创建镜像的功能异常。
##### 可能原因
tomcat进程异常。
##### 处理步骤
103. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
104. 单击“登录”。
105. 在页面上方的菜单栏，选择“集中告警”，进入“集中告警”页面。
106. 在告警列表中，找到待处理的告警记录，单击“名称”列，弹出“告警详情和处理建议”窗口。
107. 查询以下告警信息：
- 告警ID
- 来源设备名称
- IP地址
108. 使用“PuTTY”工具，登录CPT-SRV01或CPT-SRV02节点。
登录地址：5中获取的附加信息的IP地址。
默认帐号：sysadmin，默认密码：*****。
109. 执行以下命令，并按提示输入“root”用户的密码，默认密码：*****，切换至“root”用户。
sudo su - root
110. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
111. 执行以下命令，查看tomcat进程是否存在？
ps -ef| grep tomcat| grep imcs
- 是，执行10。
- 否，执行11。
112. 等待3分钟，观察告警通知是否消除？
- 是，任务结束。
- 否，请联系技术支持工程师协助解决。
113. 执行如下命令，重启tomcat进程。
sh /opt/imcs/tomcat/bin/startup.sh
114. 参考9，查看tomcat进程是否存在？
- 是，则等待3分钟观察告警是否消除。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
*******.1.4 ALM-1131012 tomcat进程down掉
##### 告警解释
镜像转换节点tomcat进程异常，系统产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1131012 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源设备名称 | 产生告警信息的设备名称。 |
| 监控系统名称 | 对接系统的类型。 |
| 来源系统 | 对接系统的名称。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 此条告警信息最近一次发生时间。 |
| 附加信息 | 告警附加信息。 |
##### 对系统的影响
通过镜像文件创建镜像的功能异常。
##### 可能原因
tomcat进程异常等。
##### 处理步骤
115. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
116. 单击“登录”。
117. 在页面上方的菜单栏，选择“集中告警”，进入“集中告警”页面。
118. 在告警列表中，找到待处理的告警记录，单击“名称”列，弹出“告警详情和处理建议”窗口。
119. 查询以下告警信息：
- 告警ID
- 来源设备名称
- IP地址
120. 使用“PuTTY”工具，登录CPT-SRV01或CPT-SRV02节点。
登录地址：5中获取的附加信息的IP地址
默认帐号：sysadmin，默认密码：*****。
121. 执行以下命令，并按提示输入“root”用户的密码，默认密码：*****，切换至“root”用户。
sudo su - root
122. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
123. 执行以下命令，查看tomcat进程是否存在？
ps -ef| grep tomcat| grep imcs
- 是，执行10。
- 否，执行11。
124. 等待3分钟，观察告警通知是否消除？
- 是，任务结束。
- 否，请联系技术支持工程师协助解决。
125. 执行如下命令，重启tomcat进程。
sh /opt/imcs/tomcat/bin/startup.sh
126. 参考9，查看tomcat进程是否存在？
- 是，则等待3分钟观察告警是否消除。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
< 上一节
******* 弹性云服务器
< 上一节