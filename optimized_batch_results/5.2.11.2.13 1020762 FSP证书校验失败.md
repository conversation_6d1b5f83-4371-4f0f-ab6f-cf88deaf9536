# ********.13 1020762 FSP证书校验失败

********.13 1020762 FSP证书校验失败
告警解释
FSP证书校验失败。
告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020762 | 紧急 | 是 |
告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 证书全路径 | 校验失败的证书全路径。 |
| 节点IP | 产生告警的节点IP。 |
对系统的影响
和FSP连接时不校验证书。
可能原因
FSP证书过期、被吊销或者未被可信CA签发。
处理步骤
- 可能原因：FSP证书过期、被吊销或者未被可信CA签发。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行以下命令检查FSP证书是否合法。
python -c 'import requests;requests.get("nova_endpoint", verify="/opt/huawei/dj/DJSecurity/server-cert/karbor/openstack/nova_ca.crt")'
其中nova_endpoint可通过执行docker exec -ti karborapi bash -c "cat /etc/karbor/karbor.conf" | grep nova_endpoint命令获取。
- 查看命令的输出信息，是否有证书校验相关的报错信息。
- 是，请根据报错信息进行处理。请参见通过ManageOne界面方式单个或批量更换证书更新FusionSphere部件的“FusionSphere-API”证书。
- 否，请执行1.e。
- 请联系技术支持工程师协助解决。
参考信息
无。