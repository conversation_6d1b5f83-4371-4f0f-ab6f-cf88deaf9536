# 5.2.4.11.1 ALM-servicemonitor_redis.connectedClientsRate Redis数据库实例的客户端连接使用率阈值告警

5.2.4.11.1.16 ALM-servicemonitor_redis.connectedClientsRate Redis数据库实例的客户端连接使用率阈值告警
告警解释
当被监控对象的数据库连接率满足用户设定的告警上报阈值条件时，上报此告警。
告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_redis.connectedClientsRate | 次要 | 业务质量 |
告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 资源名称 | 产生告警的监控对象名称（应用、应用实例等）。 |
| 资源类型 | 产生告警的资源类型。 |
| 主机IP | 产生告警的IP地址（一般为数据库节点）。 |
对系统的影响
当数据库连接率过高时，当前可用数据库连接数过少，导致后来的数据库操作请求失败，影响业务系统的性能。
可能原因
- 客户端频繁发起数据库连接。
- 单个数据库操作耗时过长。
- 连接池数量设置不合理。
处理步骤
1. 打开告警对应的“告警详情”查看“IP地址/URL/域名”，获取告警源的主机IP地址。
2. 使用Putty工具以具有登录权限的系统用户登录上报告警的异常节点，切换到root用户。
- ManageOne节点登录root用户的方法如下。
- 使用sopuser用户登录上报告警的异常节点IP地址。
默认密码：*****
- 执行如下命令，切换root用户。
sudo su root
- 非ManageOne节点登录root用户的方法请从《华为云Stack 6.5.1 安全管理指南》“帐户管理 > 帐户一览表”中“A类”页签查找节点对应云服务的root用户登录方法。
3. 查看Redis实例信息。例如查看“mosmrdb-6-41”实例信息（实例信息名称具体以环境为准），其他实例信息也可按后续步骤操作。
cd /opt/redis/data/mosmrdb-6-41
ll
4. 查看实例的端口信息和IP地址。
cat redis.conf
5. 登录Redis实例数据库。
/opt/redis/bin/redis-cli -h 主机IP地址 -p 数据库端口 -cipherdir /opt/redis/etc/cipher/ -a mosmrdb@Redis数据库用户@Redis数据库密码
mosmrdb为数据库实例，Redis数据库用户和密码参见帐户一览表。
6. 查询连接客户端信息。
INFO clients
7. 持续观察数据库连接率，如果长期超过阈值且不断增长时，执行如下操作。否则，请联系技术支持处理。
- 检查应用系统中对数据库的访问是否合理，如果存在不合理访问需要修改应用系统。
- 调整数据库连接池容量。
- 优化单次数据库操作，减少单次操作的时间。
8. 如果还无法清除告警，请联系技术支持工程师处理。
参考信息
无。
< 上一节