# ********.2 ALM-2001006 证书异常

告警解释
当Nginx证书距离过期少于30天时，上报此告警。
告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 2001006 | 紧急 | 是 |
告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
对系统的影响
证书即将过期对系统暂时无影响，证书过期后登录网站时会有访问不安全提示，用户继续访问可正常访问网站，但需尽快处理异常。
可能原因
- 阈值为1：证书即将过期。
- 阈值为2：证书过期。
- 阈值为3：证书无效或者证书不存在。
前提条件
- 已获取证书文件、私钥文件以及私钥密码。例如：证书文件名称为server.crt，私钥文件名称为server.key。证书制作方法请参见生成各组件证书章节。
- 已获取的私钥文件名称如果不是server.key，例如：serverkey.pem或serverkey.key请手动修改为server.key。
- 已准备跨平台远程访问工具，如“PuTTY”。
- 已准备网络传输工具，如“WinSCP”。
处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
5. 使用PuTTY，登录到需替换证书的Nginx节点。
默认帐户：onframework，默认密码：*****。
6. 执行以下命令，切换到root用户
sudo su - root
默认密码：*****
7. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
8. 执行如下命令，备份证书。
cd /opt/onframework/nginx
tar zcvf /tmp/nginx_config.tar.gz conf/
9. 使用网络传输工具（例如“WinSCP”），将server.key和server.crt文件上传到Nginx节点下。
10. 执行以下命令，导入环境变量。
source /etc/profile
11. 进入server.key和server.crt所在目录，执行以下命令，加密私钥。
openssl rsa -aes256 -in server.key -out server.key
- 如果私钥文件本身有密码，会有如下提示。
Enter pass phrase for server.key：
请先输入私钥本身的密码（即客户申请证书时生成.key文件时使用的密码），然后再输入一个用来加密私钥文件的密码，需要输入两次。（此密码任意输入，长度需大于4，并妥善保存，在13需要使用）。
图1 执行界面
- 如果私钥文件本身无密码，直接输入加密私钥文件的密码，需要输入两次。（此密码任意输入，长度需大于4，并妥善保存，在13需要使用）。
图2 执行界面
12. 执行如下命令，更新私钥和证书。
cp server.key /opt/onframework/nginx/conf/SSL/server.key
cp server.crt /opt/onframework/nginx/conf/SSL/server.crt
更新私钥和证书，执行以上命令提示覆盖时，请输入yes。
13. 执行以下命令，使用safetool工具对“加密私钥的密码”进行加密。
/opt/onframework/nginx/tools/safetool/bin/safetool -b
输入11中加密私钥的密钥。
将生成的密文拷贝到/opt/onframework/nginx/conf/SSL/server.pass中。
14. 执行以下命令，重启Nginx服务。
/opt/onframework/nginx/bin/nginx_monitor.sh restart
15. 进入原始server.key和server.crt文件所在目录，执行以下命令，删除原始证书文件。
rm server.key
rm server.crt
rm xxx.pfx
rm /tmp/nginx_config.tar.gz
16. 观察告警是否清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
参考信息
无。
< 上一节