# 5.2.4.18.2 ALM-36 磁盘占用率过高告警

5.2.4.18.2.5 ALM-36 磁盘占用率过高告警
告警解释
当部署面检测（检测周期为15秒）到磁盘或者某一分区的占用率大于等于告警产生阈值时，产生该告警。当磁盘或者某一分区的占用率小于等于告警清除阈值时，该告警会自动清除。
告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 36 | 重要 | 越限 |
告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 产生告警的节点名称。 |
| 操作系统 | 服务器的操作系统。 |
| 磁盘 | 产生告警的服务器磁盘名称。 |
| 站点名称 | 产生告警的站点名称。 |
对系统的影响
磁盘占用率过高可能导致部署面服务进行磁盘写操作失败，并可能引发数据库异常。
可能原因
- 部署面磁盘占用率的告警产生阈值设置不合理。
- 磁盘中的无用文件过多。
- 回收站未被清空。
- 部署面收到过多的网元告警、事件、日志，短期内大量数据从数据库中导出到磁盘文件中。
- 保留了过多的暂存的数据文件、备份文件。
处理步骤
1. 使用浏览器登录ManageOne部署面。
登录地址：https://ManageOne部署面浮动IP地址:31945。例如，https://***********:31945。
默认帐号：admin，默认密码：*****
2. 在部署面主菜单中选择“产品 > 系统监控”。
3. 在“系统监控”界面的“节点”页签右侧单击，检查“磁盘分区”的“产生告警阈值”和“清除告警阈值”是否设置合理（缺省值分别为80和75）。
- 是，执行4。
- 否，重新设置阈值为合理的值。如果告警清除，处理结束，否则，执行4。
4. 清理磁盘中的无用文件。
- 使用PuTTY工具以sopuser用户通过SSH方式登录产生告警的节点，请在如何查找节点对应的IP地址中查询节点对应的IP地址。
sopuser的默认密码为*****。
- 执行以下命令，切换到ossadm用户。
su - ossadm
ossadm的默认密码为*****。
- 执行以下命令，切换到root用户。
> su - root
Password: root用户的密码
- 执行以下命令，检查和确认哪些位置磁盘使用率过高。
# df -k
如果发现告警参数中的“磁盘”之外的位置也存在磁盘占用率高但还没有达到告警产生阈值，可以一起处理。
- 执行以下命令，进入磁盘使用率高的目录后查询该位置所有文件、目录大小，并排序后写入文件“du_k.txt”。
# cd filepath
# du -k | sort -nr > /tmp/du_k.txt
- 执行以下命令，查看“du_k.txt”文件，找到引起磁盘空间过高的子目录。
# more /tmp/du_k.txt
- 执行以下命令，进入引起磁盘空间过高的子目录后查询该位置所有文件、目录大小，并排序后写入文件“ls_l.txt”。
# cd filepath
# ls -l | sort -nr > /tmp/ls_l.txt
- 执行以下命令，查看“ls_l.txt”文件，找到引起磁盘空间过高的目录或文件。
# more /tmp/ls_l.txt
- 反复执行4.e到4.h，直到找到引起磁盘空间过高的文件，自行判断哪些文件是无用文件并进行清理。
建议优先清理历史备份的安装包、补丁包、适配层安装包，安装过程中的备份文件，core文件等。可清理的系统及数据库无用文件。执行文件清理后，执行5。
5. 如果无法确认文件是否可以删除，请联系华为技术支持工程师。
6. 等待1分钟，查看本告警是否自动清除。
- 是，告警处理结束。
- 否，请收集上述告警处理过程中的信息，联系华为技术支持工程师协助解决。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
以下情况会导致之前产生的该告警无法自动被清除，需要手工清除。
7. 产生该告警的节点名称发生了变化。
8. 在告警产生后升级了操作系统或者安装了操作系统补丁。
9. 产生该告警的站点名称发生了变化。
10. 产生该告警的硬盘的挂接点发生了变化。
11. 产生该告警的服务器不被监控了。
参考信息
无。