# ********.29 1020759 连接ManageOne运营平台失败

********.29 1020759 连接ManageOne运营平台失败
告警解释
连接ManageOne运营平台失败。
告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020759 | 重要 | 是 |
告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| SC地址 | ManageOne运营平台接口地址。 |
| 节点IP | 产生告警的节点IP。 |
| HTTP返回码 | 请求响应的HTTP返回码。 |
对系统的影响
云服务器备份服务和云硬盘备份服务无法申请备份空间和复制空间。
可能原因
- Karbor节点和ManageOne运营平台之间的网络中断。
- ManageOne运营平台的URL地址配置不正确。
- ManageOne运营平台故障。
处理步骤
- 可能原因：Karbor节点和ManageOne运营平台之间的网络中断。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行docker exec -ti karborapi bash -c "cat /etc/karbor/karbor.conf" | grep sc_endpoint命令获取ManageOne运营平台的url地址。
- 使用ping命令检查到ManageOne运营平台的网络是否连通。
- 是，请执行2。
- 否，请联系网络管理员修复网络。
- 可能原因：ManageOne运营平台的URL地址配置不正确。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 使用命令set_karbor_endpoints --sc_endpoint sc_url命令配置正确的运营平台URL地址，可在HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索“DMK_g_console:silvan.rest_address”获取sc_url地址。
- 使用check_karbor_connect命令检测Karbor节点和ManageOne运营平台的通信是否恢复正常。
- 是，处理结束。
- 否，请执行3。
- 可能原因：ManageOne运营平台故障。
- 请联系运营平台管理员，确保运营平台运行正常。
参考信息
无。