# 5.2.4.5.2 ALM-832 同类告警数量超出门限

告警解释
当系统在短时间内上报了多条相同的告警时，用户可通过汇聚规则将此类告警汇聚为一条“同类告警数量超出门限”告警。在“汇聚规则”的“汇聚动作”中设置生成汇聚告警的告警源为“OSS”，当上报的告警符合汇聚规则时，产生该告警。
告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 832 | 重要 | 时间域告警 |
告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 规则ID | 告警匹配的汇聚规则ID。<br>该参数显示在“定位信息”中。 |
| 源告警信息 | 产生汇聚告警的源告警信息，包含源告警的名称、源告警的告警源名称和汇聚规则中设置的关键参数。<br>该参数显示在“定位信息”中。 |
| 汇聚次数 | 上报源告警的次数。<br>该参数显示在“附加信息”中。 |
| IP地址 | 源告警的IP地址。<br>该参数显示在“附加信息”中。 |
| 保留字段 | 源告警的告警参数，当存在多个源告警参数时以“&”符号分隔。<br>该参数的值在汇聚规则的“定位信息保留字段”中设置，如果未设置则不显示该参数。<br>该参数显示在“附加信息”中。 |
| 汇聚类型 | 取值为告警，表示该同类告警数量超出门限告警是由源告警汇聚触发。<br>该参数显示在“附加信息”中。 |
对系统的影响
影响服务器处理告警的效率。
可能原因
- 汇聚规则中设置的上报阈值过低。
- 网元处于调测状态或出现异常，短时间上报大量告警。
处理步骤
1. 在主菜单中选择“集中告警 > 当前告警”。
2. 在告警列表中单击该告警名称，在弹出的窗口中选择“告警详情和处理建议”页签，在“其它”区域中查看“告警匹配的规则名”字段。
3. 在主菜单中选择“集中告警 > 告警设置”。
4. 在左侧导航树中选择“汇聚规则”，检查与2中获取的规则名称一致的规则触发条件。
- 如果汇聚的触发条件设置不合理，修改设置后在“当前告警”页面选中该汇聚告警，单击右上角“清除”或单击该汇聚告警操作列中手工清除汇聚告警，查看是否重复上报。如果没有重复上报，则处理完毕；如果重复上报，则执行5。
- 如果汇聚的触发条件设置合理，请执行5。
5. 检查网元是否处于调测状态。
- 是：当调测结束后，手工清除汇聚告警，查看是否重复上报。如果没有重复上报，则处理完毕；如果重复上报，则执行6。
- 否：执行6。
6. 根据附加信息中的源告警信息处理网元故障。
7. 检查网元的运行状态是否恢复正常。
- 是：在相应汇聚规则的结束条件的周期结束后，查看告警是否已清除。如果已清除，则处理完毕；如果未清除，则执行8。
- 否：执行8。
8. 请联系华为技术支持工程师。
告警清除
此告警修复后，需要手工清除此告警。
参考信息
无。
< 上一节