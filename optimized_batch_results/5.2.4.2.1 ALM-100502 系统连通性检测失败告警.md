# 5.2.4.2.1 ALM-100502 系统连通性检测失败告警

##### 告警解释
当对接系统的连通性测试失败时，产生该告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 100502 | 重要 | 处理错误告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 系统连接性测试失败的云服务名称。 |
| 服务 | 系统连通性测试失败的服务名称。 |
| 产品类型 | 系统连通性测试失败的对接系统所属的产品类型。 |
| 系统名称 | 系统连通性测试失败的对接系统的名称。 |
| 对接地址 | 系统连通性测试失败的对接系统的IP地址。 |
| 产品 | 系统连通性测试失败的对接系统所属的产品。 |
##### 对系统的影响
对接系统连接故障，会导致对接系统相关业务无法下发。
##### 可能原因
- 连通性检测失败。
- 对接系统的IP地址或端口信息错误。
- 对接系统参数错误，如用户名和密码等错误。
- 网络故障，请检查网络连通性。
- 对接系统故障，请检查对接系统是否正常。
- 驱动服务异常。
##### 处理步骤
1. 使用浏览器，登录ManageOne运维面。
登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
默认帐号：admin，默认密码：*****
2. 在主菜单中选择“集中告警 > 当前告警”。
3. 单击目标告警所在行的，展开当前告警详细信息。
其中“级别”的颜色是根据告警级别进行变换，具体请以实际为准。
##### - 查看并记录告警详情页面中的定位信息和告警可能原因。
##### - 根据告警可能原因执行对应的操作。
##### | 表1 告警可能原因及处理操作 | 表1 告警可能原因及处理操作 |
| --- | --- |
##### | 告警可能原因 | 操作 |
| 对接系统IP地址或端口信息错误 | 请联系对接系统的系统管理员获取并重新填写正确的IP地址和端口信息。<br>请联系对接系统的系统管理员获取正确的IP地址和端口信息。<br>在主菜单中选择“系统管理 > 系统设置 > 接入管理”。<br>在“系统类型”中单击告警详情页面定位信息中的系统名称。<br>选择“系统名称”，在搜索框中输入对接系统的系统名称，单击后显示匹配的对象。<br>说明： <br>也可选择“IP地址/URL/域名”、“部署区域”、“版本信息”和“厂商”，在搜索框中输入对应的值，单击后显示匹配的对象。<br>单击需要修改的对接系统所在列的，进入修改对接系统页面。<br>说明： <br>对于无法修改的对接系统，请联系技术工程师协助解决。<br>在“IP地址/域名”右侧输入框中输入正确的IP地址。<br>在“端口”右侧输入框中输入正确的端口信息。<br>单击“确定”。<br>单击对接系统“操作”列的进行连通性测试。<br>成功，结束。<br>失败，执行10。<br>请联系技术支持工程师协助解决。 |
| 对接系统参数错误，如用户名或密码等错误 | 请联系对接系统的系统管理员获取并重新填写正确的用户名和密码。<br>请联系对接系统的系统管理员获取正确的用户名和密码。<br>在主菜单中选择“系统管理 > 系统设置 > 接入管理”。<br>在“系统类型”中单击告警详情页面定位信息中的系统名称。<br>选择“系统名称”，在搜索框中输入对接系统的系统名称，单击后显示匹配的对象。<br>说明： <br>也可选择“IP地址/URL/域名”、“部署区域”、“版本信息”和“厂商”，在搜索框中输入对应的值，单击后显示匹配的对象。<br>单击需要修改的对接系统所在列的，进入修改对接系统页面。<br>说明： <br>对于无法修改的对接系统，请联系技术工程师协助解决。<br>在“用户名”右侧输入框中输入正确的用户名。<br>在“密码”右侧输入框中输入正确的密码，单击“确定”。<br>单击对接系统“操作”列的进行连通性测试。<br>成功，结束。<br>失败，执行9。<br>请联系技术支持工程师协助解决。 |
##### | 网络故障 | 使用PuTTY工具以sopuser用户通过regionAlias-ManageOne-Service01的IP地址登录到service01节点。请在参考信息中查询节点对应的节点IP地址。<br>sopuser用户的默认密码为“*****”。<br>执行如下命令，切换到ossadm帐号。<br>su - ossadm<br>ossadm用户的默认密码为“*****”。<br>执行如下命令，是否能ping通告警参数中的“对接地址”。<br>ping IP地址<br>是，表明SNMP对接系统故障，请参考“对接系统故障”中的1~10处理。<br>否，执行4。<br>请联系技术支持工程师协助解决。 |
| 对接系统故障 | 在主菜单中选择“系统管理 > 系统设置 > 接入管理”。<br>在“系统类型”页面中单击告警详情页面的定位信息中的系统名称。<br>单击选择“系统名称”，在搜索框中输入对接系统的系统名称，单击后显示匹配的对象。<br>说明： <br>也可选择“IP地址/URL/域名”、“部署区域”、“版本信息”和“厂商”，在搜索框中输入对应的值，单击后显示匹配的对象。<br>单击对接系统“操作”列的进行连通性测试。<br>查看弹框中连通性测试返回结果。<br>对端系统接口返回500错误，则表明对接系统出现内部错误，执行7。<br>对接系统网络不通，参考网络故障中的1~3处理。<br>对接系统证书错误，执行6。<br>进入“证书管理 > 信任证书”页面，查看是否有该对接系统的证书信息<br>否，参考运维帮助中心的“系统运维 > 接入管理 > 创建对接系统”章节，根据实际对接的情况，在对应的对接系统的章节，获取上传证书的方法。<br>是，执行7。<br>查看证书的有效期，看证书是否已过期。<br>是，参考《华为云Stack 6.5.1 安全管理指南》的“证书管理”章节，根据实际对接情况，找到对应的证书替换章节替换对接系统证书。<br>否，执行10。<br>证书上传成功后，执行9。<br>单击对接系统“操作”列的进行连通性测试。<br>成功，结束。<br>失败，执行10。<br>请联系技术工程师协助解决。 |
##### | 驱动服务异常 | 在主菜单中选择“系统管理 > 系统设置 > 接入管理”。<br>在“驱动生命周期管理 > 驱动管理”中单击告警详情页面中定位信息中的系统名称。<br>查看该驱动实例的部署类型，如果是“inner”，即是ManageOne内置驱动，则执行4，如果是“outer”，则执行7，参考运维帮助中心的“系统运维 > 接入管理 > 简介”章节中的“驱动类型和对接系统关系表”。<br>查看服务状态。<br>使用PuTTY工具以sopuser用户分别登录regionAlias-ManageOne-Service01和regionAlias-ManageOne-Service02节点查看驱动服务状态。请在参考信息中查询节点对应的IP地址。<br>默认账号：sopuser。<br>sopuser用户的默认密码为“*****”。<br>执行以命令，切换到ossadm账号。<br>su - ossadm<br>ossadm用户的默认密码为“*****”。<br>参考表2查看驱动名称和驱动服务的对应关系，执行以下命令查看服务状态是否正常，此处以MOFSPPMDriverService微服务为例。<br>. /opt/oss/manager/bin/engr_profile.sh<br>/opt/oss/manager/agent/bin/ipmc_adm -cmd statusapp -app MOFSPPMDriverService<br>回显信息：<br>Process Name               Process Type           App Name    Tenant Name   ProcessMode  IP              PID     Status  <br>mofsppmdriverservice-3-0   mofsppmdriverservice  MOFSPPMDriverService  Product       cluster      **************  26037   STOPPED<br>RUNNING表示正常，其他表示异常。<br>是，执行7。<br>否，执行5<br>执行以下命令，重启微服务并查看服务状态是否正常，此处以MOFSPPMDriverService微服务为例。<br>重启微服务：<br>. /opt/oss/manager/bin/engr_profile.sh<br>/opt/oss/manager/agent/bin/ipmc_adm -cmd restartapp -app MOFSPPMDriverService<br>回显信息：<br>success<br>查看微服务状态是否正常：<br>/opt/oss/manager/agent/bin/ipmc_adm -cmd statusapp -app MOFSPPMDriverService<br>回显信息：<br>Process Name               Process Type           App Name    Tenant Name   ProcessMode  IP              PID     Status  <br>mofsppmdriverservice-3-0   mofsppmdriverservice  MOFSPPMDriverService  Product       cluster      **************  26037   RUNNING<br>是，执行6。<br>否，执行7。<br>单击对接系统“操作”列的进行连通性测试。<br>成功，结束。<br>失败，执行7。<br>请联系技术工程师协助解决。 |
| 表2 驱动服务与驱动名称的对应关系 | 表2 驱动服务与驱动名称的对应关系 |
| --- | --- |
| 驱动服务 | 驱动名称 |
| MOFSPPMDriverService | plugin_driver_hw_fusionsphereopenstack_pm |
| MOFSPFMDriverService | plugin_driver_hw_fusionsphereopenstack_fm |
| MOFSPRMDriverService | plugin_driver_hw_fusionsphereopenstack_rm |
| MOFSPAccessService | plugin_driver_hw_fusionsphereopenstack_access |
| MOCommonDriverService | plugin_driver_hw_cs |
| MOCommonDriverService | plugin_driver_hw_external |
| MOCommonDriverService | plugin_driver_hw_common |
| MOVRMDriverService | plugin_driver_hw_fusioncompute |
| MORCSAPService | RestConnectorService |
| MORCSAPService | SnmpConnectorService |
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
以下介绍查找节点对应的管理IP地址的方法。
- 启动浏览器，在地址栏中输入https://部署面的客户端登录IP地址:31945，按“Enter”。
- 输入用户名、密码，单击“登录”。
- 在主菜单中，选择“产品 > 系统监控”，在“系统监控”页面左上方，光标移至并选择对应的产品。然后进入“系统监控”页面的“节点”页签。
- 在“节点名称”列查找待查询管理IP地址的节点。
- 单击节点名称，在节点详情页面上方的IP地址即为该节点的管理IP地址。