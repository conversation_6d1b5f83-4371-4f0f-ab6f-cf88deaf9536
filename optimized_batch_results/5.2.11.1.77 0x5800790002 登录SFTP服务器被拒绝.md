# 5.2.11.1.77 0x5800790002 登录SFTP服务器被拒绝

5.2.11.1.77 0x5800790002 登录SFTP服务器被拒绝
告警解释
用户（[User]）登录SFTP服务器（路径：[SFtp_path]）被拒绝。
告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x5800790002 | 重要 | 否 |
告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| User | SFTP用户。 |
| SFtp_path | SFTP服务器路径。 |
对系统的影响
管理数据备份到SFTP服务器失败。
可能原因
登录SFTP服务器的用户名或密码不正确。
处理步骤
- 可能原因1：登录SFTP服务器的用户名或密码不正确。
- 联系管理员获取正确的SFTP服务器的用户名和密码。
- 使用浏览器，依次登录Manager和Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > 配置 > 管理数据备份”，进入“管理数据备份”界面。
- 单击“设置备份存储”，进入“设置备份存储”界面，检查配置的SFTP服务器用户和密码是否正确。
- 是，联系技术支持工程师协助解决。
- 否，执行1.e。
- 重新配置SFTP备份存储，详细请参考SFTP章节。
参考信息
无