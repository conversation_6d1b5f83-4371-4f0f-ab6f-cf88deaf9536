# 5.2.11.3.22 0x3230043 高可用集群中高斯数据库主备间复制中断

5.2.11.3.22 0x3230043 高可用集群中高斯数据库主备间复制中断
告警解释
高可用集群中高斯数据库主备间复制中断。
告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230043 | 紧急 | 是 |
对系统的影响
主端数据库数据无法同步到备端，备端数据和主端不一致，主备切换后业务数据丢失。
可能原因
- 备端节点宕机。
- 备端eReplication服务挂掉。
- 主备高斯数据库的复制链路中断。
处理步骤
1. 使用PuTTY，以告警详细信息中的本端链路IP地址登录到本端服务器节点。
默认帐号：DRManager，默认密码：*****。
2. 执行su - root命令切换到root帐号。
root帐号的默认密码为*****。
3. 执行export LD_LIBRARY_PATH=/opt/BCManager/Runtime/ha/libs; cd /opt/BCManager/Runtime/ha/module/hacom/script;sh config_ha.sh -a命令，回显中“HaArbLK”行的第二个值即表示远端节点IP地址。
4. 执行cd /opt/BCManager/Runtime/bin;sh showSystemStatus.sh查看本端节点和远端节点上的服务状态，
如果命令回显中ResStatus列中的值都是非Normal或Active_normal，表示服务处理未运行状态。请转5。
如果命令回显中ResStatus的值为Normal或Active_normal，表示服务都处于运行状态，请转7。
5. 执行cd /opt/BCManager/Runtime/bin/;sh startSystem.sh启动备端eReplication服务，如果命令回显中存在“System started completely.”，表示服务已启动成功。查看告警是否清除。
- 是，流程结束。
- 否，请转6。
6. 分别在主、备服务所在操作系统中通过ping命令检查主备服务器之间的网络连接是否正常。
- 是，请转7。
- 否，请修复网络连接，待网络连接正常后，检查告警是否清除，如果告警未清除，请转7。
7. 根据6的执行结果，查看是否存成在丢包的现象。
- 是，联系管理员提高网络性能质量，保证主备服务器之间通信稳定。流程结束。
- 否，执行8。
8. 请联系技术支持工程师协助解决。
参考信息
无。