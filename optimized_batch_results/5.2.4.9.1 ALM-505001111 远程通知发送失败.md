# 5.2.4.9.1 ALM-505001111 远程通知发送失败

##### 告警解释
当设置了系统与远程通知短消息或邮箱服务器对接参数并启用时，远程通知短消息或邮件发送失败才会产生该告警。该告警需手工清除。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 505001111 | 重要 | 通信告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 通知方式 | 发送远程通知的方式。 |
| 通知内容 | 发送失败的远程通知内容。 |
| 通知失败接收用户 | 发送远程通知失败的接收用户，取部分用户并做匿名化处理。 |
##### 对系统的影响
指定用户接收不到远程通知短消息或邮件。
##### 可能原因
通讯参数（如短信网关协议参数、邮箱设置参数等）设置有误。
##### 处理步骤
1. 在主菜单中选择“集中告警 > 告警设置”。
2. 在左侧导航树中选择“远程通知规则”。查看是否已设置并启用了远程通知规则。
- 如果已设置且已启用，请执行步骤4。
- 如果没有设置或没有启用，请执行步骤3。
3. 检查远程通知规则中设置的短消息、邮箱的接收号码、接收地址是否正确。
- 如果不正确，修改为正确的短消息接收号码或邮件接收地址，再执行步骤4。
- 如果正确，请执行步骤4。
4. 在主菜单中选择“集中告警 > 当前告警”。
5. 在当前告警页面查找并单击“远程通知发送失败”告警名称，在弹出的基本信息页面查看“定位信息”和“附加信息”。
- 如果定位信息显示为“通知方式=短消息”，则可能是短消息设置、SMN设置或者是短信猫设置出现异常，附加信息显示消息接收人，请执行步骤6、步骤7。
- 如果定位信息显示为“通知方式=邮件”，附加信息显示邮件接收人，请执行步骤6、步骤8。
此告警的定位信息包含远程通知短消息或邮件的内容，最多255个字符。
6. 在主菜单中选择“系统管理 > 系统设置 > 远程通知”。
7. 根据短消息发送方式进行测试。
| 短消息发送方式 | 测试方法 | 测试成功 | 连接失败 |
| --- | --- | --- | --- |
| 短信网关 | 在左侧导航树中选择“短消息设置 > 短信网关设置”、“短消息设置 > 协议参数设置”，确认短信网关设置和协议参数设置是否配置正确，并根据步骤5获取附加信息的短消息接收人，在短信网关设置页面设置接收短消息号码，单击“测试”，测试连接短消息服务器是否正常。 | 操作结束 | 收集上述告警处理过程中的信息，联系华为技术支持工程师协助解决。 |
| SMN | 在左侧导航树中选择“SMN设置”，查看SMN设置是否正常，并根据步骤5获取附加信息的短消息接收人，设置接收短消息号码，单击“测试”，测试连接SMN服务器是否正常。 | 操作结束 | 收集上述告警处理过程中的信息，联系华为技术支持工程师协助解决。 |
| 短信猫 | 在左侧导航树中选择“短信猫设置”，并根据步骤5获取附加信息的短消息接收人，设置接收短消息号码，单击“测试”，测试连接短信猫是否正常。 | 操作结束 | 收集上述告警处理过程中的信息，联系华为技术支持工程师协助解决。 |
8. 在左侧导航树中选择“邮箱设置”，查看邮箱设置是否正常，单击“测试”，查看连接邮箱服务器是否正常。
- 如果测试成功，需根据步骤5获取附加信息的邮件接收人确认邮箱地址是否正确。
- 如果连接失败，请收集上述告警处理过程中的信息，联系华为技术支持工程师协助解决。
告警清除
必须手动清除告警。
##### 参考信息
无。
5.2.4.10 系统维护
5.2.4.10.1 告警参考
5.2.4.10.1.1 ALM-MOMaintenanceService_100100 操作系统帐号密码即将过期
##### 告警解释
系统维护定时检测操作系统帐号过期情况，当到期时间距离当前时间间隔小于等于30天时，产生此告警，告警级别为重要，当到期时间距离当前时间间隔小于等于7天时，告警级别为紧急。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| MOMaintenanceService_100100 | 紧急/重要 | 环境告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 节点IP地址 | 被检测操作系统帐号所在节点的IP地址。 |
| 帐号名称 | 被检测的操作系统帐号名称。 |
| 帐号密码即将过期天数 | 被检测帐号密码即将过期的天数。 |
##### 对系统的影响
操作系统帐号过期不可用，可能影响业务功能的正常运行。
##### 可能原因
操作系统帐号对应的密码即将过期。
##### 处理步骤
9. 检查操作系统帐号密码到期时间距离当前时间是否小于等于30天或小于等于7天。
- 使用PuTTY，登录告警发生所在节点IP。在该告警的“定位信息”列获取告警发生的节点IP。
默认帐户：sopuser，默认密码：*****。
- 执行如下命令，切换用户到root。
sudo su root
默认密码：*****。
- 切换至root用户，执行chage命令，查看帐户到期时间。例如，查看ossuser帐户的到期时间，执行如下命令。
chage -l ossuser
回显信息如下所示，其中“Password expires”对应的时间为该帐户密码的到期时间。
Last password change                                    : Jun 15, 2018
Password expires                                        : Dec 12, 2018
Password inactive                                       : Jan 16, 2019
Account expires                                         : never
Minimum number of days between password change          : 7
Maximum number of days between password change          : 180
Number of days of warning before password expires       : 15
- 计算帐户到期时间距离当前时间是否小于等于30天或小于等于7天。
- 是，执行2。
- 否，执行4。
10. 修改密码。
- 如果是root 、ossuser、dbuser、ossadm和sopuser帐户，执行passwd命令，修改操作系统即将过期的帐号密码。例如，修改ossuser帐户的密码，执行如下命令。
passwd ossuser
回显如下信息，根据提示输入新密码，并且再次确认新密码。
Changing password for user ossuser.
New password:
Retype new password:
如果用户需要再次修改为默认密码，请执行以下操作。
- 执行如下命令，登录虚拟机。
ssh username@IP
username是待修改密码的帐户。IP是帐户所在节点的IP地址。
- 执行如下命令，切换至root用户。
sudo su - root
默认密码为：*****。
- 执行以下命令，清空旧密码的记录。
true>/etc/security/opasswd
- 执行以下命令，修改为原来的默认密码。
passwd username
- 如果是meteradmin、taskfileadmin、autoopsadmin和logctadm帐户密码过期，由于这些帐户涉及与其他云服务组件交互，如果修改为非默认密码，会影响业务，建议修改密码有效期来避免因密码过期而导致服务不可用。建议将密码修改为永久不过期密码。
以meteradmin帐户为例，执行如下命令。
chage -M 180 meteradmin
其中，180为天数，设置为99999为永久不过期。
11. 待凌晨一点自动执行操作系统帐户密码过期检查之后，查看告警是否清除。
- 是，处理完毕。
- 否，执行4。
12. 请收集上述告警处理过程中的信息，联系技术支持工程师协助解决。
13. 执行如下命令，退出root用户。
exit
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。
5.2.4.10.1.2 ALM-MOMaintenanceService_100103 证书即将过期
##### 告警解释
系统维护定时检测IR证书过期情况，当过期时间距离当前时间间隔小于等于30天时，产生此告警，告警级别为重要，当过期时间距离当前时间间隔小于等于7天时，告警级别为紧急。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| MOMaintenanceService_100103 | 紧急/重要 | 环境告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 节点IP地址 | 被检测证书所在节点的IP地址。 |
| 证书服务名称 | 被检测的证书的服务名称。 |
| 证书即将过期天数 | 被检测的证书即将过期的天数。 |
##### 对系统的影响
IR证书过期不可用，影响业务功能的正常运行。
##### 可能原因
IR证书即将过期。
##### 处理步骤
14. 检查IR证书过期时间距离当前时间是否小于等于30天或小于等于7天。
- 使用PuTTY，登录告警发生所在节点。在该告警的“定位信息”列获取告警发生的节点IP。
默认帐户：sopuser，默认密码：*****。
- 执行如下命令，切换至ossadm帐户。
su - ossadm
默认密码：*****
- 执行openssl命令，查看IR身份证书和信任证书过期时间。例如，查看server.cer证书的到期时间，执行如下命令。
openssl x509 -enddate -noout -in /opt/oss/manager/etc/ssl/internal/server.cer
回显信息如下，表示该证书到期时间为2028年6月10日，凌晨0点0分0秒 。
notAfter=Jun 10 00:00:00 2028 GMT
- 计算证书到期时间距离当前时间是否小于等于30天或小于等于7天。
- 是，执行2。
- 否，执行4。
15. 参见更换ManageOne的IR证书，更新IR证书。
16. 待凌晨一点自动执行证书过期检查之后，查看告警是否清除 。
- 是，处理完毕。
- 否，执行4。
17. 请收集上述告警处理过程中的信息，联系技术支持工程师协助解决。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。
5.2.4.10.1.3 ALM-MOMaintenanceService_100106 证书即将过期
##### 告警解释
系统维护定时检测CA证书过期情况，当到期时间距离当前时间间隔小于等于30天时，产生此告警，告警级别为重要，当到期时间距离当前时间间隔小于等于7天时，告警级别为紧急。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| MOMaintenanceService_100106 | 紧急/重要 | 环境告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 节点IP地址 | 被检测证书所在节点的IP地址。 |
| 证书服务名称 | 被检测的证书服务名称。 |
| 证书即将过期天数 | 被检测的证书即将过期的天数。 |
##### 对系统的影响
CA证书过期不可用，影响业务功能的正常运行。
##### 可能原因
CA证书即将过期。
##### 处理步骤
18. 检查CA证书过期时间距离当前时间是否小于等于30天或小于等于7天。
- 使用PuTTY，登录告警发生所在节点。在该告警的“定位信息”列获取告警发生的节点IP。
默认帐户：sopuser，默认密码：*****。
- 执行如下命令，切换至ossadm帐户。
su - ossadm
默认密码：*****
- 执行openssl命令，查看CA身份证书和信任证书过期时间。例如，查看ca.cer证书的到期时间，执行如下命令。
openssl x509 -enddate -noout -in /opt/oss/manager/var/ca/ca.cer
回显信息如下，表示该证书到期时间为2032年6月10日，凌晨0点0分0秒 。
notAfter=Jun 10 00:00:00 2032 GMT
- 计算证书到期时间距离当前时间是否小于等于30天或小于等于7天。
- 是，执行2。
- 否，执行4。
19. 参见更换ManageOne的CA证书，更新CA证书。
20. 待凌晨一点自动执行证书过期检查之后，查看告警是否清除。
- 是，处理完毕。
- 否，执行4。
21. 请收集上述告警处理过程中的信息，联系技术支持工程师协助解决。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。
< 上一节
******** 服务监控
********.1 告警参考
********.1.1 ALM-servicemonitor_agent_heartbeat 节点连续中断
##### 告警解释
云服务、ManageOne所在节点部署了MOICAgent服务，用于监控云服务以及ManageOne自身服务、收集云服务日志、备份ManageOne数据库。正常情况下，MOICAgent定时上报心跳消息，当超过30分钟未上报心跳时，上报此告警，表示监控节点通讯异常。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_agent_heartbeat | 次要 | 通信告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 被监控节点IP | 通讯异常的被监控节点的IP地址。 |
| 监控节点IP | MOICAgentMgmtService服务的节点IP地址。 |
##### 对系统的影响
22. 在通讯异常节点，无法监控云服务、收集云服务日志、备份ManageOne数据库。
23. 若断连节点为netcluster_elb_lvs_vm_x_x，则会造成ELB流量中断10s。
24. 若断连节点为netcluster_elb_nginx_vm_x_x，则会造成ELB流量中断15s。
##### 可能原因
- 被监控节点故障。
- 被监控节点和ManageOne的MOICAgentMgmtService服务所在节点之间网络故障或者进程异常。
- 被监控节点和ManageOne的MOICAgentMgmtService服务所在节点之间证书不一致。
##### 处理步骤
25. 获取被监控节点和MOICAgentMgmtService服务的IP地址，如图1所示。
- 如果是根源告警，在ManageOne运维面打开告警对应的“告警详情”查看“定位信息”，获取被监控节点的主机IP信息和MOICAgentMgmtService服务所在节点IP地址。
- 如果是汇聚告警，在ManageOne运维面打开告警对应的“被汇聚告警”查看“定位信息”，获取被监控节点的主机IP信息和MOICAgentMgmtService服务所在节点IP地址。
图1 告警详情
26. 判断是否网络故障。
- 使用Putty工具以具有登录权限的系统用户登录MOICAgentMgmtService服务所在节点IP地址。切换到root用户。
- 使用sopuser用户登录MOICAgentMgmtService服务所在节点IP地址。
- 执行如下命令，切换root用户。
sudo su root
- 执行如下命令检查MOICAgentMgmtService服务节点和被监控节点之间的通信是否存在网络阻塞。
ping 被监控节点IP
回显信息如下，表示网络正常，执行3。否则，网络阻塞，执行5。
64 bytes from 被监控节点IP
27. 收集进程信息，检查进程是否正常。
- 使用Putty工具以具有登录权限的系统用户登录上报告警的被监控节点，切换到root用户。
- ManageOne节点登录root用户的方法如下。
- 使用sopuser用户登录上报告警的被监控节点。
- 执行如下命令，切换root用户。
sudo su root
- 非ManageOne节点登录root用户的方法请从《华为云Stack 6.5.1 安全管理指南》“帐户管理 > 帐户一览表”中“A类”页签查找节点对应云服务的root用户登录方法。
- 执行如下命令查询MOICAgent进程是否正常。
ps -ef | grep moicagent | grep python
无回显信息，表示进程异常，否则进程正常。
- 进程异常，执行3.c。
- 进程正常，执行6。
- 执行如下命令拉起MOICAgent进程。
- ManageOne节点：
su ossadm -c ". /opt/oss/manager/agent/bin/engr_profile.sh;ipmc_adm -cmd restartapp -tenant manager -app MOICAgent"
- 非ManageOne节点：
sh /home/<USER>/bin/manual/mstart.sh
- 再次执行3.b查询MOICAgent进程是否正常。
- 正常，等待5~10分钟后查看告警是否自动清除。
- 清除，结束。
- 未清除，执行6。
- 异常，执行4。
28. 安装MOICAgent服务。
详细操作参考《华为云Stack 6.5.1 扩容指南》中“安装MOICAgent至新增物理服务器”章节。
- 安装成功，等待5~10分钟后查看告警是否自动清除。
- 清除，结束。
- 未清除，执行6。
- 安装失败，执行6。
29. 检查被监控节点是否故障，并清除故障。
- 查找被监控节点IP对应的参数名称。
在参数信息汇总文件《xxx_export_all_CN.xlsm》的“2.1 工具生成的IP参数”页签中搜索被监控节点IP对应的“参数名称”。如果参数名称前缀为ManageOne，则执行5.b，否则，请执行6。
- 查看节点连接状态是否正常。
- 使用浏览器登录ManageOne部署面。
登录地址：https://ManageOne部署面浮动IP地址:31945。例如，https://***********:31945。
默认帐号：admin，默认密码：*****
- 在菜单中选择“产品 > 系统监控”。
- 在“节点”页签，查看5.a的参数名称所对应的节点连接状态是否正常。如果正常，执行6，否则，请参考常用故障章节清除故障。
- 清除故障成功，等待5~10分钟后查看告警是否自动清除。
- 清除，结束。
- 未清除，执行6。
- 清除故障失败，执行6。
30. 请联系技术支持工程师协助解决。
告警清除
当MOICAgent上报新的心跳消息时，自动清除此告警。
##### 参考信息
无。
********.1.2 ALM-servicemonitor_heartbeat 服务监控节点心跳异常告警
##### 告警解释
当MOSMAccessService服务所在节点心跳异常（即服务监控AP节点心跳异常）时，上报此告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_heartbeat | 次要 | 业务质量 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 服务名称 | 产生告警的服务名称。 |
| 组件名称 | 产生告警的IP地址或虚拟机名称。 |
| 组件类型 | 产生告警的实例类型。 |
##### 对系统的影响
当MOSMAccessService服务所在节点心跳异常，会影响到AP节点所在区域监控数据的上报，造成监控数据异常，告警数据异常。
##### 可能原因
- MOSMAccessService服务所在节点进程停止。
- MOSMAccessService服务所在节点所在虚拟机电源状态异常。
##### 处理步骤
31. 查看上报告警的主机IP信息和所对应的虚拟机主机名称。
- 打开告警对应的“告警详情”查看“IP地址/URL/域名”，获取上报告警的主机IP信息。
- 通过主机IP确定虚拟机主机名称。
- 在菜单中选择“运维工具箱 > 服务监控”，进入服务监控页面。
- 单击“监控看板 > 节点列表”，进入节点列表页面。
- 在页面右上角搜索框输入上报告警的主机IP进行搜索，节点对应的名称就是虚拟机主机名称。
32. 查看上报告警的IP节点所对应虚拟机“电源状态”是否为“运行中”。
- 使用浏览器登录Service OM。
- 登录ManageOne运维面，在页面上方的导航栏，选择“运维地图”，进入“运维地图”页面。
- 在“运维地图”页面右侧的“快速访问”导航栏中，单击“Service OM”，进入Service OM界面。
- 在主菜单选择“资源 > 计算资源”，进入“计算资源”页面。
- 在“虚拟机”页签搜索虚拟机主机名称，查看虚拟机的“电源状态”是否为“运行中”。
- 是，执行5。
- 否，单击“操作”列中的“更多 > 重启”，使虚拟机的“电源状态”为“运行中”。如果重启不成功，则执行5。
33. 查看上报告警的IP节点进程是否已经停止运行。
- 使用Putty以SSH方式通过sopuser用户登录上报告警的主机IP，执行如下命令切换root用户。
sudo su root
- 执行如下命令查看MOSMAccessService服务进程是否在运行中。
ps -ef | grep "MOSMAccessService"
- 回显信息如下，表示进程是运行中状态，执行5。
java -Dfile.encoding=UTF-8 -Dlog.dir=/opt/log/oss/Product/MOSMService/mosmservice-3-0 -DNFW=mosmservice-3-0 -DTOMCAT_LOG_DIR=/opt/log/oss/Product/MOSMService/mosmservice-3-0/tomcatlog -DTOMCAT_WORK_DIR=/opt/share/Product/MOSMService/mosmservice-3-0/tomcatwork -DNFW=mosmservice-3-0 -Dprocname=mosmservice-3-0 -Xss256k -XX:NativeMemoryTracking=detail -XX:+UseParallelGC -XX:+UseAdaptiveSizePolicy -XX:GCTimeRatio=49 -XX:+UseParallelOldGC -server -XX:MaxDirectMemorySize=128m -Xms256m -Xmx512m -Xmn128m -Xss256K -XX:MaxPermSize=64m -XX:InitialCodeCacheSize=32m -XX:ReservedCodeCacheSize=64m -Djdk.nio.maxCachedBufferSize=128k -XX:MetaspaceSize=32m -XX:MaxMetaspaceSize=128m -XX:+CrashOnOutOfMemoryError -XX:-UseLargePages -XX:+UseFastAccessorMethods -XX:+HeapDumpOnOutOfMemoryError -XX:-UseBiasedLocking -XX:AutoBoxCacheMax=20000 -XX:NativeMemoryTracking=detail -XX:CompileThreshold=100000 -XX:MaxInlineSize=2 -XX:OnStackReplacePercentage=10000 -XX:MaxInlineLevel=1 -XX:InlineSmallCode=20 -XX:-InlineSynchronizedMethods -XX:CICompilerCount=2 -XX:+ExplicitGCInvokesConcurrentAndUnloadsClasses -Dbsp.app.datasource=mosmdb -Xms256m -Xmx256m -XX:InitialCodeCacheSize=8m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m -XX:ReservedCodeCacheSize=256m -XX:MaxDirectMemorySize=256m -DMOSCALE=B2B200VM com.huawei.mo.jetty.JettyBootUp MOSMService,MOSMAccessService,MOSMDataService
- 否则，进程已经停止运行，执行4。
34. 重启对应服务进程。
- 使用Putty以SSH方式通过sopuser用户登录报告警的主机IP，执行如下命令切换root用户。
sudo su root
- 执行如下命令，启动主进程。
su ossadm -c ". /opt/oss/manager/agent/bin/engr_profile.sh;ipmc_adm -cmd startapp -app MOSMService"
- 回显信息如下，表示进程启动成功。否则，进程启动失败，执行5。
Starting process mosmservice-x-x ... success
- 如果进程启动成功，等待10分钟，查看告警是否清除。
- 是，结束。
- 否，执行5。
35. 请联系技术支持工程师协助解决。
告警清除
当服务监控AP节点心跳正常时，自动清除此告警。
##### 参考信息
无。
********.1.3 ALM-servicemonitor_cpu.percent CPU使用率阈值告警
##### 告警解释
当被监控对象的CPU使用率满足用户设定的告警上报阈值条件时，上报此告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_cpu.percent | 次要 | 业务质量 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 资源名称 | 产生告警的监控对象名称（应用、应用实例等）。 |
| 资源类型 | 产生告警的资源类型。 |
| 主机IP | 产生告警的主机IP地址。 |
##### 对系统的影响
被监控对象的CPU使用率过高会导致系统吞吐量下降，系统响应延迟增加。
##### 可能原因
被监控系统的CPU使用率过高。
##### 处理步骤
36. 打开告警对应的“告警详情”查看“IP地址/URL/域名”，获取告警源的主机IP地址。
37. 使用Putty工具以具有登录权限的系统用户登录上报告警的异常节点，切换到root用户。
- ManageOne节点登录root用户的方法如下。
- 使用sopuser用户登录上报告警的异常节点IP地址。
默认密码：*****
- 执行如下命令，切换root用户。
sudo su root
- 非ManageOne节点登录root用户的方法请从《华为云Stack 6.5.1 安全管理指南》“帐户管理 > 帐户一览表”中“A类”页签查找节点对应云服务的root用户登录方法。
38. 查看CPU使用率，如果进程CPU使用率大于85%，执行4，否则联系技术支持协助处理。
top -c
键盘输入shift + p，按CPU使用率降序排序。
39. 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.1.4 ALM-servicemonitor_memory.percent 物理内存使用率阈值告警
##### 告警解释
当被监控对象的物理内存使用率满足用户设定的告警上报阈值条件时，上报此告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_memory.percent | 次要 | 业务质量 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 资源名称 | 产生告警的监控对象名称（应用、应用实例等）。 |
| 资源类型 | 产生告警的资源类型。 |
| 主机IP | 产生告警的主机IP地址。 |
##### 对系统的影响
物理内存使用率过高，可用物理内存变少，将导致系统运行缓慢，系统吞吐量下降，系统响应延迟增高。
##### 可能原因
- 系统中运行过多的进程。
- 某些进程占用过多的物理内存。
- 物理内存容量过小。
##### 处理步骤
40. 打开告警对应的“告警详情”查看“IP地址/URL/域名”，获取告警源的主机IP地址。
41. 使用Putty工具以具有登录权限的系统用户登录上报告警的异常节点，切换到root用户。
- ManageOne节点登录root用户的方法如下。
- 使用sopuser用户登录上报告警的异常节点IP地址。
默认密码：*****
- 执行如下命令，切换root用户。
sudo su root
- 非ManageOne节点登录root用户的方法请从《华为云Stack 6.5.1 安全管理指南》“帐户管理 > 帐户一览表”中“A类”页签查找节点对应云服务的root用户登录方法。
42. 查看内存使用率，如果进程内存使用率大于85%，执行4，否则联系技术支持协助处理。
top -c
键盘输入shift + m，按内存使用率降序排序。
43. 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.1.5 ALM-servicemonitor_os.nic.rx_dropped_ps 网卡流入丢包率阈值告警
##### 告警解释
当被监控对象的网卡流入丢包率满足用户设定的告警上报阈值条件时，上报此告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_os.nic.rx_dropped_ps | 次要 | 业务质量 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 资源名称 | 产生告警的监控对象名称（应用、应用实例等）。 |
| 资源类型 | 产生告警的资源类型。 |
| 主机IP | 产生告警的IP地址。 |
##### 对系统的影响
系统的网卡流入丢包率过高，将导致系统中进程网络通信延迟增高或无法正常通信，业务响应时间增大。
##### 可能原因
- 系统运行了大量的网络通信进程。
- 系统中的某个进程频繁通信。
- 系统设备故障。
##### 处理步骤
44. 打开告警对应的“告警详情”查看“IP地址/URL/域名”，获取告警源的主机IP地址。
45. 使用Putty工具以具有登录权限的系统用户登录上报告警的异常节点，切换到root用户。
- ManageOne节点登录root用户的方法如下。
- 使用sopuser用户登录上报告警的异常节点IP地址。
默认密码：*****
- 执行如下命令，切换root用户。
sudo su root
- 非ManageOne节点登录root用户的方法请从《华为云Stack 6.5.1 安全管理指南》“帐户管理 > 帐户一览表”中“A类”页签查找节点对应云服务的root用户登录方法。
46. 查看网卡信息。
cd /sys/class/net/
ll
47. 查看某一个网卡流入丢包数。例如查看“eth0”网卡流入丢包数。
如果“eth0”网卡流入丢包数大于0，执行5，否则联系技术支持协助处理。
cat /sys/class/net/eth0/statistics/rx_dropped
48. 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.1.6 ALM-servicemonitor_os.nic.rx_errors_ps 网卡流入错包率阈值告警
##### 告警解释
当被监控对象的网卡流入错包率满足用户设定的告警上报阈值条件时，上报此告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_os.nic.rx_errors_ps | 次要 | 业务质量 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 资源名称 | 产生告警的监控对象名称（应用、应用实例等）。 |
| 资源类型 | 产生告警的资源类型。 |
| 主机IP | 产生告警的IP地址。 |
##### 对系统的影响
系统的网卡流入错包率过高，将导致系统中进程网络通信延迟增高或无法正常通信，业务响应时间增大。
##### 可能原因
- 系统运行了大量的网络通信进程。
- 系统中的某个进程频繁通信。
- 系统设备故障。
##### 处理步骤
49. 打开告警对应的“告警详情”查看“IP地址/URL/域名”，获取告警源的主机IP地址。
50. 使用Putty工具以具有登录权限的系统用户登录上报告警的异常节点，切换到root用户。
- ManageOne节点登录root用户的方法如下。
- 使用sopuser用户登录上报告警的异常节点IP地址。
默认密码：*****
- 执行如下命令，切换root用户。
sudo su root
- 非ManageOne节点登录root用户的方法请从《华为云Stack 6.5.1 安全管理指南》“帐户管理 > 帐户一览表”中“A类”页签查找节点对应云服务的root用户登录方法。
51. 查看网卡信息。
cd /sys/class/net/
ll
52. 查看某一个网卡流入错包数。例如查看“eth0”网卡流入错包数。
如果“eth0”网卡流入错包数大于0，执行5，否则联系技术支持协助处理。
cat /sys/class/net/eth0/statistics/rx_errors
53. 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.1.7 ALM-servicemonitor_os.nic.tx_dropped 网卡流出丢包率阈值告警
##### 告警解释
当被监控对象的网卡流出丢包率满足用户设定的告警上报阈值条件时，上报此告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_os.nic.tx_dropped | 次要 | 业务质量 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 资源名称 | 产生告警的监控对象名称（应用、应用实例等）。 |
| 资源类型 | 产生告警的资源类型。 |
| 主机IP | 产生告警的IP地址。 |
##### 对系统的影响
系统的网卡流出丢包率过高，将导致系统中进程网络通信延迟增高或无法正常通信，业务响应时间增大。
##### 可能原因
- 系统运行了大量的网络通信进程。
- 系统中的某个进程频繁通信。
- 系统设备故障。
##### 处理步骤
54. 打开告警对应的“告警详情”查看“IP地址/URL/域名”，获取告警源的主机IP地址。
55. 使用Putty工具以具有登录权限的系统用户登录上报告警的异常节点，切换到root用户。
- ManageOne节点登录root用户的方法如下。
- 使用sopuser用户登录上报告警的异常节点IP地址。
默认密码：*****
- 执行如下命令，切换root用户。
sudo su root
- 非ManageOne节点登录root用户的方法请从《华为云Stack 6.5.1 安全管理指南》“帐户管理 > 帐户一览表”中“A类”页签查找节点对应云服务的root用户登录方法。
56. 查看网卡信息。
cd /sys/class/net/
ll
57. 查看某一个网卡流出丢包数。例如查看“eth0”网卡流出丢包数。
如果“eth0”网卡流出丢包数大于0，执行5，否则联系技术支持协助处理。
cat /sys/class/net/eth0/statistics/tx_dropped
58. 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.1.8 ALM-servicemonitor_os.nic.tx_errors 网卡流出错包率阈值告警
##### 告警解释
当被监控对象的网卡流出错包率满足用户设定的告警上报阈值条件时，上报此告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_os.nic.tx_errors | 次要 | 业务质量 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 资源名称 | 产生告警的监控对象名称（应用、应用实例等）。 |
| 资源类型 | 产生告警的资源类型。 |
| 主机IP | 产生告警的IP地址。 |
##### 对系统的影响
系统的网卡流出错包率过高，将导致系统中进程网络通信延迟增高或无法正常通信，业务响应时间增大。
##### 可能原因
- 系统运行了大量的网络通信进程。
- 系统中的某个进程频繁通信。
- 系统设备故障。
##### 处理步骤
59. 打开告警对应的“告警详情”查看“IP地址/URL/域名”，获取告警源的主机IP地址。
60. 使用Putty工具以具有登录权限的系统用户登录上报告警的异常节点，切换到root用户。
- ManageOne节点登录root用户的方法如下。
- 使用sopuser用户登录上报告警的异常节点IP地址。
默认密码：*****
- 执行如下命令，切换root用户。
sudo su root
- 非ManageOne节点登录root用户的方法请从《华为云Stack 6.5.1 安全管理指南》“帐户管理 > 帐户一览表”中“A类”页签查找节点对应云服务的root用户登录方法。
61. 查看网卡信息。
cd /sys/class/net/
ll
62. 查看某一个网卡流出错包数。例如查看“eth0”网卡流出错包数。
如果“eth0”网卡流出错包数大于0，执行5，否则联系技术支持协助处理。
cat /sys/class/net/eth0/statistics/tx_errors
63. 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.1.9 ALM-servicemonitor_os.disk.io_waite 硬盘IO等待时间阈值告警
##### 告警解释
当被监控对象的硬盘IO等待时间满足用户设定的告警上报阈值条件时，上报此告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_os.disk.io_waite | 次要 | 业务质量 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 资源名称 | 产生告警的监控对象名称（应用、应用实例等）。 |
| 资源类型 | 产生告警的资源类型。 |
| 主机IP | 产生告警的IP地址。 |
##### 对系统的影响
硬盘IO等待时间过长，会导致系统所有业务处理缓慢。
##### 可能原因
业务进程出错，导致业务停止IO操作。
##### 处理步骤
64. 打开告警对应的“告警详情”查看“IP地址/URL/域名”，获取告警源的主机IP地址。
65. 使用Putty工具以具有登录权限的系统用户登录上报告警的异常节点，切换到root用户。
- ManageOne节点登录root用户的方法如下。
- 使用sopuser用户登录上报告警的异常节点IP地址。
默认密码：*****
- 执行如下命令，切换root用户。
sudo su root
- 非ManageOne节点登录root用户的方法请从《华为云Stack 6.5.1 安全管理指南》“帐户管理 > 帐户一览表”中“A类”页签查找节点对应云服务的root用户登录方法。
66. 查看磁盘信息。如果硬盘IO等待时间过高，执行4，否则联系技术支持协助处理。
cat /proc/diskstats
每列信息具体参数意义可参考下图：
67. 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.1.10 ALM-servicemonitor_os.disk.rd_rsp_time 硬盘读响应时间阈值告警
##### 告警解释
当被监控对象的硬盘读响应时间满足用户设定的告警上报阈值条件时，上报此告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_os.disk.rd_rsp_time | 次要 | 业务质量 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 资源名称 | 产生告警的监控对象名称（应用、应用实例等）。 |
| 资源类型 | 产生告警的资源类型。 |
| 主机IP | 产生告警的IP地址。 |
##### 对系统的影响
当被监控对象的磁盘读响应时间过长时，会导致被监控对象运行缓慢，并使得整个系统的吞吐量下降，系统响应延迟增高。
##### 可能原因
业务进程出错导致业务频繁执行IO操作。
##### 处理步骤
68. 打开告警对应的“告警详情”查看“IP地址/URL/域名”，获取告警源的主机IP地址。
69. 使用Putty工具以具有登录权限的系统用户登录上报告警的异常节点，切换到root用户。
- ManageOne节点登录root用户的方法如下。
- 使用sopuser用户登录上报告警的异常节点IP地址。
默认密码：*****
- 执行如下命令，切换root用户。
sudo su root
- 非ManageOne节点登录root用户的方法请从《华为云Stack 6.5.1 安全管理指南》“帐户管理 > 帐户一览表”中“A类”页签查找节点对应云服务的root用户登录方法。
70. 查看磁盘信息。如果硬盘读响应时间过高，执行4，否则联系技术支持协助处理。
cat /proc/diskstats
每列信息具体参数意义可参考下图：
71. 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.1.11 ALM-servicemonitor_os.disk.wt_rsp_time 硬盘写响应时间阈值告警
##### 告警解释
当被监控对象的硬盘写响应时间满足用户设定的告警上报阈值条件时，上报此告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_os.disk.wt_rsp_time | 次要 | 业务质量 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 资源名称 | 产生告警的监控对象名称（应用、应用实例等）。 |
| 资源类型 | 产生告警的资源类型。 |
| 主机IP | 产生告警的IP地址。 |
##### 对系统的影响
当被监控对象的磁盘写响应时间过长时，会导致被监控对象运行缓慢，并使得整个系统的吞吐量下降，系统响应延迟增高。
##### 可能原因
业务进程出错导致业务频繁执行IO操作。
##### 处理步骤
72. 打开告警对应的“告警详情”查看“IP地址/URL/域名”，获取告警源的主机IP地址。
73. 使用Putty工具以具有登录权限的系统用户登录上报告警的异常节点，切换到root用户。
- ManageOne节点登录root用户的方法如下。
- 使用sopuser用户登录上报告警的异常节点IP地址。
默认密码：*****
- 执行如下命令，切换root用户。
sudo su root
- 非ManageOne节点登录root用户的方法请从《华为云Stack 6.5.1 安全管理指南》“帐户管理 > 帐户一览表”中“A类”页签查找节点对应云服务的root用户登录方法。
74. 查看磁盘信息。如果硬盘写响应时间过高，执行4，否则联系技术支持协助处理。
cat /proc/diskstats
每列信息具体参数意义可参考下图：
75. 请联系技术支持工程师协助解决。
##### 参考信息
无。
********.1.12 ALM-servicemonitor_os.fs.inode_free inode空闲率阈值告警
##### 告警解释
当被监控对象的inode空闲率满足用户设定的告警上报阈值条件时，上报此告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_os.fs.inode_free | 次要 | 业务质量 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 资源名称 | 产生告警的监控对象名称（应用、应用实例等）。 |
| 资源类型 | 产生告警的资源类型。 |
| 主机IP | 产生告警的主机IP地址。 |
##### 对系统的影响
当被监控对象的inode空闲率过低时，说明磁盘分区inode即将用满，会导致系统无法继续在该分区下创建新目录或文件，影响系统正常使用。
##### 可能原因
系统磁盘下存在大量的碎文件。
##### 处理步骤
##### 本处理步骤中所呈现的数值、文件、进程等信息仅作为告警消除的示例，可能与实际环境中的信息不一致，具体操作中请以实际环境信息参考如下步骤进行处理。
76. 打开告警对应的“告警详情”查看“IP地址/URL/域名”，获取告警源的主机IP地址。
77. 使用Putty工具以具有登录权限的系统用户登录上报告警的异常节点，切换到root用户。
- ManageOne节点登录root用户的方法如下。
- 使用sopuser用户登录上报告警的异常节点IP地址。
默认密码：*****
- 执行如下命令，切换root用户。
sudo su root
- 非ManageOne节点登录root用户的方法请从《华为云Stack 6.5.1 安全管理指南》“帐户管理 > 帐户一览表”中“A类”页签查找节点对应云服务的root用户登录方法。
78. 查看inode空闲率，如果有如下异常情况，执行4，否则联系技术支持协助处理。
df -i
如下图，“IUse%”列显示占用率为90%或较高值对应的文件夹，相当于inode空闲率为10%或较低值对应的文件夹。
79. 业务正常情况下，联系业务运维人员进行扩容、迁移业务，其他情况请联系技术支持工程师协助解决。
##### 参考信息
无。
********.1.13 ALM-servicemonitor_os.fs.percent 硬盘使用率阈值告警
##### 告警解释
当被监控对象的硬盘使用率满足用户设定的告警上报阈值条件时，上报此告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_os.fs.percent | 次要 | 业务质量 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 资源名称 | 产生告警的监控对象名称（应用、应用实例等）。 |
| 资源类型 | 产生告警的资源类型。 |
| 主机IP | 产生告警的主机IP地址。 |
##### 对系统的影响
当被监控对象的磁盘使用率过高时，会导致被监控对象运行缓慢，并使得整个系统的吞吐量下降，系统响应延迟增高。
##### 可能原因
磁盘被占满。
##### 处理步骤
##### 本处理步骤中所呈现的数值、文件、进程等信息仅作为告警消除的示例，可能与实际环境中的信息不一致，具体操作中请以实际环境信息参考如下步骤进行处理。
80. 打开告警对应的“告警详情”查看“IP地址/URL/域名”，获取告警源的主机IP地址。
81. 使用Putty工具以具有登录权限的系统用户登录上报告警的异常节点，切换到root用户。
- ManageOne节点登录root用户的方法如下。
- 使用sopuser用户登录上报告警的异常节点IP地址。
默认密码：*****
- 执行如下命令，切换root用户。
sudo su root
- 非ManageOne节点登录root用户的方法请从《华为云Stack 6.5.1 安全管理指南》“帐户管理 > 帐户一览表”中“A类”页签查找节点对应云服务的root用户登录方法。
82. 查看硬盘使用率。如果有如下异常情况，执行4，否则联系技术支持协助处理。
- 执行如下命令，查找“Use%”列显示占用率为90%或较高值对应的文件夹。如图1所示。
df -h
图1 文件占用磁盘空间查询
如果发现其他文件夹的“Use%”列显示占用率为90%或者较高，也可以按后续步骤进行处理。
- 执行如下命令，查询/opt目录下大于50M的文件。
find /opt -type f -size +50000k -exec ls -lh {} \; | awk '{ print $9 ": " $5 }'
83. 业务正常情况下，联系业务运维人员进行扩容、迁移业务，其他情况请联系技术支持工程师协助解决。
##### 参考信息
无。
********.1.14 ALM-servicemonitor_redis.dbcopyStatus Redis数据库实例复制状态阈值告警
##### 告警解释
当被监控对象的数据库主备复制状态发生异常时，上报此告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| Servicemonitor_redis.dbcopyStatus | 次要 | 业务质量 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 资源名称 | 产生告警的监控对象名称（应用、应用实例等）。 |
| 资源类型 | 产生告警的资源类型。 |
| 主机IP | 产生告警的IP地址（一般为数据库节点）。 |
##### 对系统的影响
数据库主备状态不同步，会导致数据丢失。
##### 可能原因
数据库主备状态不同步。
##### 处理步骤
84. 打开告警对应的“告警详情”查看“IP地址/URL/域名”，获取告警源的主机IP地址。
85. 使用Putty工具以具有登录权限的系统用户登录上报告警的异常节点，切换到root用户。
- ManageOne节点登录root用户的方法如下。
- 使用sopuser用户登录上报告警的异常节点IP地址。
默认密码：*****
- 执行如下命令，切换root用户。
sudo su root
- 非ManageOne节点登录root用户的方法请从《华为云Stack 6.5.1 安全管理指南》“帐户管理 > 帐户一览表”中“A类”页签查找节点对应云服务的root用户登录方法。
86. 查看Redis实例信息。例如查看“mosmrdb-6-41”实例信息（实例信息名称具体以环境为准），其他实例信息也可按后续步骤操作。
cd /opt/redis/data/mosmrdb-6-41
ll
87. 查看实例的端口信息和IP地址。
cat redis.conf
“port”对应的是端口号，“bind”对应的是IP地址。
88. 登录Redis实例数据库。
/opt/redis/bin/redis-cli -h 主机IP地址 -p 数据库端口 -cipherdir /opt/redis/etc/cipher/ -a mosmrdb@Redis数据库用户@Redis数据库密码
mosmrdb为数据库实例，Redis数据库用户和密码参见帐户一览表。
89. 查询主/从复制信息。
INFO Replication
90. 请联系技术支持工程师根据已获得的信息进行处理。
##### 参考信息
无。
********.1.15 ALM-servicemonitor_redis.dbsvrStatus Redis数据库实例状态阈值告警
##### 告警解释
当数据库实例状态异常时，上报此告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_redis.dbsvrStatus | 次要 | 业务质量 |
参数
| 参数名称 | 参数含义 |
| --- | --- |
| 资源名称 | 产生告警的监控对象名称（应用、应用实例等）。 |
| 资源类型 | 产生告警的资源类型。 |
| 主机IP | 产生告警的IP地址（一般为数据库节点）。 |
##### 对系统的影响
数据库无法正常访问，影响业务正常使用。
##### 可能原因
数据库节点或进程异常。
##### 处理步骤
91. 打开告警对应的“告警详情”查看“IP地址/URL/域名”，获取告警源的主机IP地址。
92. 使用Putty工具以具有登录权限的系统用户登录上报告警的异常节点，切换到root用户。
- ManageOne节点登录root用户的方法如下。
- 使用sopuser用户登录上报告警的异常节点IP地址。
默认密码：*****
- 执行如下命令，切换root用户。
sudo su root
- 非ManageOne节点登录root用户的方法请从《华为云Stack 6.5.1 安全管理指南》“帐户管理 > 帐户一览表”中“A类”页签查找节点对应云服务的root用户登录方法。
93. 查看Redis实例信息。例如查看“mosmrdb-6-41”实例信息（实例信息名称具体以环境为准），其他实例信息也可按后续步骤操作。
cd /opt/redis/data/mosmrdb-6-41
ll
94. 查看实例的端口信息和IP地址。
cat redis.conf
95. 登录Redis实例数据库。
/opt/redis/bin/redis-cli -h 主机IP地址 -p 数据库端口 -cipherdir /opt/redis/etc/cipher/ -a mosmrdb@Redis数据库用户@Redis数据库密码
mosmrdb为数据库实例，Redis数据库用户和密码参见帐户一览表。
96. 查询Redis服务器信息。
INFO server
97. 确认数据库节点或进程是否存在异常。
- 是，恢复数据库节点和异常进程，查看该是否清除。如果未清除告警，请联系技术支持工程师处理。
- 否，请联系技术支持工程师处理。
##### 参考信息
无。
********.1.16 ALM-servicemonitor_redis.connectedClientsRate Redis数据库实例的客户端连接使用率阈值告警
##### 告警解释
当被监控对象的数据库连接率满足用户设定的告警上报阈值条件时，上报此告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_redis.connectedClientsRate | 次要 | 业务质量 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 资源名称 | 产生告警的监控对象名称（应用、应用实例等）。 |
| 资源类型 | 产生告警的资源类型。 |
| 主机IP | 产生告警的IP地址（一般为数据库节点）。 |
##### 对系统的影响
当数据库连接率过高时，当前可用数据库连接数过少，导致后来的数据库操作请求失败，影响业务系统的性能。
##### 可能原因
- 客户端频繁发起数据库连接。
- 单个数据库操作耗时过长。
- 连接池数量设置不合理。
##### 处理步骤
98. 打开告警对应的“告警详情”查看“IP地址/URL/域名”，获取告警源的主机IP地址。
99. 使用Putty工具以具有登录权限的系统用户登录上报告警的异常节点，切换到root用户。
- ManageOne节点登录root用户的方法如下。
- 使用sopuser用户登录上报告警的异常节点IP地址。
默认密码：*****
- 执行如下命令，切换root用户。
sudo su root
- 非ManageOne节点登录root用户的方法请从《华为云Stack 6.5.1 安全管理指南》“帐户管理 > 帐户一览表”中“A类”页签查找节点对应云服务的root用户登录方法。
100. 查看Redis实例信息。例如查看“mosmrdb-6-41”实例信息（实例信息名称具体以环境为准），其他实例信息也可按后续步骤操作。
cd /opt/redis/data/mosmrdb-6-41
ll
101. 查看实例的端口信息和IP地址。
cat redis.conf
102. 登录Redis实例数据库。
/opt/redis/bin/redis-cli -h 主机IP地址 -p 数据库端口 -cipherdir /opt/redis/etc/cipher/ -a mosmrdb@Redis数据库用户@Redis数据库密码
mosmrdb为数据库实例，Redis数据库用户和密码参见帐户一览表。
103. 查询连接客户端信息。
INFO clients
104. 持续观察数据库连接率，如果长期超过阈值且不断增长时，执行如下操作。否则，请联系技术支持处理。
- 检查应用系统中对数据库的访问是否合理，如果存在不合理访问需要修改应用系统。
- 调整数据库连接池容量。
- 优化单次数据库操作，减少单次操作的时间。
105. 如果还无法清除告警，请联系技术支持工程师处理。
##### 参考信息
无。
< 上一节
******** 统一证书
ALM-MOCertMgmt_100101 系统存在即将过期证书告警
********.1 ALM-MOCertMgmt_100101 系统存在即将过期证书告警
##### 告警解释
当ManageOne系统中存在即将过期的证书时（证书过期时间小于30天），上报此告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| MOCertMgmt_100101 | 紧急/重要 | 业务质量 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 证书名称 | 即将过期的证书名称以及距离过期的时间 |
| 所属部件 | 证书所属的部件名称 |
##### 对系统的影响
当系统中证书过期时会对业务产生严重影响，甚至导致业务中断，需要及时更换证书。
##### 可能原因
系统中的证书即将过期，过期时间小于30天为重要告警，小于7天为紧急告警。
##### 处理步骤
106. 打开告警对应的“告警详情”，查看产生告警的证书名称和所属部件。
107. 根据证书过期场景进行处理。
- 如果证书名称为“ManageOne-PKI”且证书已过期，则参考手动恢复FusionSphere-Keystone PKI证书进行手工恢复PKI证书。
- 如果证书名称为“FusionSphere”开头的证书，查看系统中是否有“ALM-9915 证书过期预警”，如果有，则参考ALM-9915 证书过期预警处理；如果没有，则参考3-6。
- 其他场景请参考3-6进行证书更新。
108. 在菜单中选择“运维工具箱 > 统一证书”，进入证书管理页面。
109. 在“地域选择”和“部件选择”中选择需要更新的证书所在的地域和部件。
110. 勾选需要更新的证书名称，单击证书名称对应的“更新”。
111. 查看"更新状态"列显示“成功”则结束操作。否则，请再次尝试更新或联系技术支持协助处理。
告警清除
当即将过期的告警被成功替换后，用户可手动清楚告警或等待系统自动清除告警。
##### 参考信息
无
5.2.4.13 统一日志