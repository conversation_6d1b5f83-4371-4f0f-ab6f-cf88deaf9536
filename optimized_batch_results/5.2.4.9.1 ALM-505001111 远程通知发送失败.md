# 5.2.4.9.1 ALM-505001111 远程通知发送失败

告警解释
当设置了系统与远程通知短消息或邮箱服务器对接参数并启用时，远程通知短消息或邮件发送失败才会产生该告警。该告警需手工清除。
告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 505001111 | 重要 | 通信告警 |
告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 通知方式 | 发送远程通知的方式。 |
| 通知内容 | 发送失败的远程通知内容。 |
| 通知失败接收用户 | 发送远程通知失败的接收用户，取部分用户并做匿名化处理。 |
对系统的影响
指定用户接收不到远程通知短消息或邮件。
可能原因
通讯参数（如短信网关协议参数、邮箱设置参数等）设置有误。
处理步骤
1. 在主菜单中选择“集中告警 > 告警设置”。
2. 在左侧导航树中选择“远程通知规则”。查看是否已设置并启用了远程通知规则。
- 如果已设置且已启用，请执行步骤4。
- 如果没有设置或没有启用，请执行步骤3。
3. 检查远程通知规则中设置的短消息、邮箱的接收号码、接收地址是否正确。
- 如果不正确，修改为正确的短消息接收号码或邮件接收地址，再执行步骤4。
- 如果正确，请执行步骤4。
4. 在主菜单中选择“集中告警 > 当前告警”。
5. 在当前告警页面查找并单击“远程通知发送失败”告警名称，在弹出的基本信息页面查看“定位信息”和“附加信息”。
- 如果定位信息显示为“通知方式=短消息”，则可能是短消息设置、SMN设置或者是短信猫设置出现异常，附加信息显示消息接收人，请执行步骤6、步骤7。
- 如果定位信息显示为“通知方式=邮件”，附加信息显示邮件接收人，请执行步骤6、步骤8。
此告警的定位信息包含远程通知短消息或邮件的内容，最多255个字符。
6. 在主菜单中选择“系统管理 > 系统设置 > 远程通知”。
7. 根据短消息发送方式进行测试。
| 短消息发送方式 | 测试方法 | 测试成功 | 连接失败 |
| --- | --- | --- | --- |
| 短信网关 | 在左侧导航树中选择“短消息设置 > 短信网关设置”、“短消息设置 > 协议参数设置”，确认短信网关设置和协议参数设置是否配置正确，并根据步骤5获取附加信息的短消息接收人，在短信网关设置页面设置接收短消息号码，单击“测试”，测试连接短消息服务器是否正常。 | 操作结束 | 收集上述告警处理过程中的信息，联系华为技术支持工程师协助解决。 |
| SMN | 在左侧导航树中选择“SMN设置”，查看SMN设置是否正常，并根据步骤5获取附加信息的短消息接收人，设置接收短消息号码，单击“测试”，测试连接SMN服务器是否正常。 | 操作结束 | 收集上述告警处理过程中的信息，联系华为技术支持工程师协助解决。 |
| 短信猫 | 在左侧导航树中选择“短信猫设置”，并根据步骤5获取附加信息的短消息接收人，设置接收短消息号码，单击“测试”，测试连接短信猫是否正常。 | 操作结束 | 收集上述告警处理过程中的信息，联系华为技术支持工程师协助解决。 |
8. 在左侧导航树中选择“邮箱设置”，查看邮箱设置是否正常，单击“测试”，查看连接邮箱服务器是否正常。
- 如果测试成功，需根据步骤5获取附加信息的邮件接收人确认邮箱地址是否正确。
- 如果连接失败，请收集上述告警处理过程中的信息，联系华为技术支持工程师协助解决。
告警清除
必须手动清除告警。
参考信息
无。