# 5.2.4.16.1 ALM-10000011 ManageOne管理资源超过部署规格限制

##### 告警解释
MOAutoOps定期执行脚本，计算出等效虚拟网元数量，以此等效虚拟网元数量和Region数量来匹配ManageOne的部署规格，如果匹配出的规格超过ManageOne部署规格限制时，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 10000011 | 紧急 | 环境告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 当前等效网元数 | 当前环境管理设备的等效网元数。 |
| 最大支持网元数 | 最大支持的等效网元数。 |
| 当前Region总数 | 当前环境的Region总数。 |
| 最大支持Region总数 | 最大支持的Region总数。 |
| 当前私有云Region数 | 当前环境完整的私有云Region数量，不包括通过HiCloud、IAAS-E、IAAS-V接入的Region。 |
| 最大支持私有云Region数 | 最大支持的私有云Region数。 |
##### 对系统的影响
可能导致任务失败、接口超时、页面反应变慢，甚至内存溢出、节点重启。
##### 可能原因
ManageOne管理设备数量或Region数量超过ManageOne部署规格限制。
##### 处理步骤
1. 查看当前告警定位信息。
- 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
- 在主菜单中选择“集中告警 > 当前告警”。
- 单击“ManageOne管理资源超过部署规格限制”告警左侧的，在“告警详情”页签的“定位信息”中，查看当前管理资源数目和ManageOne部署规格限制。
包括：当前等效网元数、最大支持网元数、当前Region总数、最大支持Region总数、当前私有云Region数和最大支持私有云Region数。
- 如果只有当前等效网元数大于最大支持网元数，执行2。
- 如果存在当前Region总数或当前私有云Region数大于对应最大支持规格的情况，执行6。
2. 通过“运维自动化”界面的定期执行脚本，下载告警信息收集文件。
- 在主菜单中选择“运维自动化 > 作业 > 作业历史”，进入“作业历史”页面。
- 搜索作业名称为“检查ManageOne管理资源”的作业历史，并在最新执行时间所在行的“操作列”单击“下载结果”。
- 从所下载的Excel中，可查看到当前ManageOne所管理资源的资源名称、资源数量、单位等效网元和等效网元数。
3. 判断是否可通过调整资源数量，使Excel中的等效网元数总和小于最大支持网元数。
- 根据现网环境评估哪些云服务和基础设施资源可释放。
- 根据评估结果，在Excel表格“资源数量”列，调整对应的云服务或基础设施的数量。
- 查看调整后的等效网元数总和，确认等效网元数总和是否小于最大支持网元数。
- 是，执行4。
- 否，执行6。
4. 释放符合要求的闲置资源。
5. 查看告警是否清除。
- 是，处理完毕。
- 否，执行6。
6. 扩容解决方案，使ManageOne部署规格限制满足管理资源要求。
请参考《华为云Stack 6.5.1 扩容指南》中的“扩容管理规模”章节。
7. 查看告警是否清除。
- 是，处理完毕。
- 否，联系技术工程师协助解决。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。
5.2.4.17 华为虚拟化资源池
5.2.4.17.1 ALM-MOVCDRService_100091 话单文件发送到计量中心文件服务器失败告警
##### 告警解释
上传话单文件到计量中心文件服务器失败，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| MOVCDRService_100091 | 重要 | 通信告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 计量中心文件服务器 | 计量中心文件服务器的地址。 |
##### 对系统的影响
资源的话单文件无法正常上传，导致计量系统无法采集到话单，影响资源的计量统计。
##### 可能原因
- 计量中心文件服务器不能正常使用。
- 计量中心文件服务器空间不足。
##### 处理步骤
8. 检查计量中心文件服务器是否可以正常登录。
- 使用浏览器，登录ManageOne运维面。
登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
默认帐号：admin，默认密码：*****
- 在主菜单中选择“系统管理 > 系统设置 > 系统维护”。
- 在左树中选择“配置信息管理”。
- 单击“数据上报配置”页签。
- 在“分组名称”为“METERSFTP”的所在行，单击“修改”。
- 设置“端口号”、“用户名”、“密码”和“地址”对应的“值”并记录。
- 使用FileZilla工具，根据1.f中记录的信息，检查是否可以正常登录计量中心服务器。
- 是：执行2。
- 否：请联系技术支持工程师协助解决。
9. 检查计量中心文件服务器的/opt/meterfiles/uploads目录，是否有权限创建文件。
- 是：删除创建的文件，执行3。
- 否：请联系技术支持工程师协助解决。
10. 检查计量中心文件服务器的/opt/meterfiles/uploads目录，已用空间是否超过5GB。
- 是：请联系技术支持工程师协助解决。
- 否：执行4。
图1 uploads目录已用空间
11. 等待1个小时后，检查计量中心文件服务器的/opt/meterfiles/uploads目录，是否有新的话单文件上传。
- 是：告警处理完毕。
- 否：请联系技术支持工程师协助解决。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无
******** 部署面业务告警
********.1 ManageOne管理
********.1.1 ALM-101209 节点故障倒换
##### 告警解释
当主节点发生故障导致OMMHA倒换，或者手工倒换，倒换完成后，会产生该告警。当倒换完成且OMMHA状态正常时，该告警会自动清除。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 101209 | 紧急 | 保护倒换 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 节点名称 | 倒换后的主节点名称。 |
| 站点名称 | 产生告警的站点名称。 |
##### 对系统的影响
12. 浮动IP地址从主节点切换到备节点。
13. 浮动IP地址切换过程中，部署面无法登录。
14. 原主节点的single进程包括critical进程会被停掉，原备节点升为主节点过程中，该节点的single进程包括critical进程会被启动。
##### 可能原因
- 原主节点虚拟机所在的硬件故障。
- 原主节点异常断电。
- 原主节点critical类型的进程异常。
- 原主节点CPU、内存耗用过高。
- 原主节点所在网络故障。
- 浮动IP地址挂载不成功。
- 用户进行了手工倒换。
##### 处理步骤
15. 检查原主节点虚拟机所在硬件是否故障、原主节点是否发生异常断电。
如果由于原主节点虚拟机所在硬件故障或原主节点异常断电导致OMMHA倒换，请联系管理员修复故障。
16. 检查主节点进程状态是否正常。
- 使用PuTTY工具以sopuser用户通过SSH方式登录产生告警的节点。请在如何查找节点对应的IP地址中查询节点对应的IP地址
sopuser的默认密码为*****。
- 执行以下命令，切换到ossadm用户。
su - ossadm
ossadm的默认密码为*****。
- 执行如下命令，查询主备节点的状态：
> cd /opt/oss/Product/apps/OMMHAService/bin
> bash status.sh
如果是部署节点，/opt/oss/Product的值为/opt/oss/manager。
系统回显类似如下提示信息：
NodeName    HostName    ...    HAAllResOK    HARunPhase
ha2         Service-2   ...    normal        Actived
ha1         Service-1   ...    normal        Deactived
NodeName    ResName     ...    ResHAStatus    ResType
ha2         RMCritical  ...    Normal         Active_standby
ha2         RMFloatIp   ...    Normal         Single_active
ha2         RMIrNic     ...    Normal         Single_active
ha1         RMCritical  ...    Normal         Active_standby
ha1         RMFloatIp   ...    Normal         Single_active
ha1         RMIrNic     ...    Normal         Single_active
- 如果回显信息中的“HAAllResOK”和“ResHAStatus”均为“normal”状态，则表示倒换完成，且状态正常，该告警将自动清除。
- 如果“HAAllResOK”和“ResHAStatus”存在“normal”以外的状态，则表示存在节点故障或倒换未完成。
17. 等待3分钟，检查告警是否清除。
- 是，处理结束。
- 否，请收集上述告警处理过程中的信息，联系华为技术支持工程师协助解决。
##### 参考信息
无。
********.1.2 ALM-51023 NTP服务异常
##### 告警解释
部署面默认间隔5分钟检测一次部署节点和产品节点上NTP服务器的状态，当连续3次检测到NTP服务器存在且状态为不生效时，产生此告警。当连续3次检测到NTP服务器存在且状态为正常时，告警将自动清除。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 51023 | 紧急 | 处理错误告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 节点名称 | 产生告警的节点名称。 |
| 站点名称 | 产生告警的站点名称。 |
| NTP地址 | 出现异常的NTP服务器IP地址。 |
##### 对系统的影响
各ManageOne服务如果无法从上级NTP服务器进行时间同步，将导致网络中各设备时间不准确，在执行备份恢复、操作日志记录等需要记录时间戳的操作时，有可能会出现因备份包恢复错误或日志获取错误等问题而影响业务处理效率。
##### 可能原因
- 节点间的时间同步关系异常。
- 多个NTP服务器之间的时间不一致。
- 节点的NTP服务异常（可能由于NTP配置文件异常或者进程运行异常导致）。
- NTP服务器异常。
##### 处理步骤
##### 查看该告警中的可能原因信息。
##### - 若可能原因为节点间的时间同步关系异常，按照如下方法处理异常。
- 使用浏览器登录ManageOne部署面。
登录地址：https://ManageOne部署面浮动IP地址:31945。例如，https://***********:31945。
默认帐号：admin，默认密码：*****
- 在部署面主菜单中选择“维护 > 时间管理 > 配置NTP”，在“配置NTP”页面单击“重新配置”，修复节点的时间同步关系。检查告警是否清除。
- 是，处理结束。
- 否，请联系华为技术支持工程师处理。
##### - 若可能原因为多个NTP服务器之间的时间不一致，请用户自行调整NTP服务器的系统时间，保证所有NTP服务器的系统时间一致。检查告警是否清除。
- 是，处理结束。
- 否，请联系华为技术支持工程师处理。
##### - 若可能原因为节点的NTP服务异常（可能由于NTP配置文件异常或者进程运行异常导致），则按照如下方法处理异常。
- 使用PuTTY工具以sopuser用户通过SSH方式登录产生告警的节点。获取节点IP地址的方法请参见如何查找节点对应的IP地址。
- 检查NTP服务是否已经启动。
> sudo su root
Password: root用户的密码
# service ntpd status
- 若系统回显信息中包含“active (running)”字样的信息，说明节点的NTP服务已启动。
- 若系统回显信息提示无ntpd service时，请执行以下命令检查NTP服务。
# ps -ef|grep ntp|grep -v grep
若系统回显信息中包含“ntpd”字样的信息，说明节点的NTP服务已启动。否则，说明节点的NTP服务未启动，执行以下命令启动NTP服务。
# service ntp start
如果系统提示以下信息，说明启动NTP服务成功，否则请联系华为技术支持工程师。
Starting network time protocol daemon (NTPD)                          done
- 否则，说明节点的NTP服务未启动，执行以下命令启动NTP服务。
# service ntpd start
- 执行以下命令，退出root用户。
# exit
- 检查告警是否清除。
- 是，处理结束。
- 否，请联系华为技术支持工程师处理。
##### - 若可能原因为NTP服务器异常，则按照如下方法处理异常。
- 检查部署节点与NTP服务器网络连接是否正常。
- 使用PuTTY工具以sopuser用户通过SSH方式登录部署节点。
- 检查NTP服务器是否能正常通信。
> sudo su root
Password: root用户的密码
# ntpdate -q NTP服务器的IP地址
如果回显包含如下显示，且信息中stratum的值为1~16， offset 不为0，delay 不为0，表示NTP能正常通信。否则联系华为技术支持工程师处理。
server NTP服务器的IP地址, stratum 6, offset -0.000010, delay 0.02599
- 执行以下命令，退出root用户。
# exit
- 检查NTP服务器是否正常，如果异常，请自行修复NTP服务器异常。
- 修复以后，建议等待半小时后，再检查告警是否清除。
- 是，处理结束。
- 否，请联系华为技术支持工程师处理。
##### 参考信息
无。
********.1.3 ALM-101211 数据库实例故障倒换
##### 告警解释
当数据库实例故障倒换成功时，产生此告警。此告警修复后需要手工清除。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 101211 | 重要 | 处理错误告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 数据库故障实例的主机名。 |
| 数据库服务 | 产生告警的数据库实例名称。 |
| 数据库类型 | 数据库实例类型。 |
##### 对系统的影响
数据库实例复制状态不正常，可能会有数据丢失。
##### 可能原因
- 数据库主实例所在节点故障或者下电。
- 数据库主实例故障或者停止运行。
##### 处理步骤
18. 在“当前告警”页面，单击本告警名称查看发生故障倒换的数据库实例的主机名、实例名称和类型。
19. 检查是否有“数据库本地主备复制异常”的告警。
- 是，根据该告警对应的修复建议进行处理，具体操作请参见ALM-101210 数据库本地主备复制异常。
- 否，则执行3。
20. 在“当前告警”页面，选择“数据库实例故障倒换”告警，单击“清除”，手工清除此告警。
告警清除
在“告警监控”页面手动清除此告警。
##### 参考信息
无。
********.1.4 ALM-101212 连接ZooKeeper失败
##### 告警解释
当DBHASwitchService连接ZooKeeper失败时，产生此告警，此时数据库倒换功能异常。当DBHASwitchService连接ZooKeeper恢复正常，自动清除。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 101212 | 重要 | 处理错误告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 异常ZooKeeper所在节点的主机名 |
| 服务名 | 服务名称 |
##### 对系统的影响
数据库倒换功能无法使用，数据库访问路由无法刷新。
##### 可能原因
- 超过一半的ZooKeeper服务故障。
- 超过一半的ZooKeeper所在节点故障，或者网络故障。
##### 处理步骤
21. 使用浏览器登录ManageOne部署面。
登录地址：https://ManageOne部署面浮动IP地址:31945。例如，https://***********:31945。
默认帐号：admin，默认密码：*****
22. 查看ZooKeeper服务进程运行状态。
- 在部署面主菜单中选择“产品 > 系统监控”。
- 在“系统监控”页面左上方，光标移至并选择“CloudSOP-UniEP”。
- 选择“服务”页签，单击“UniEPMgr”。
- 在“服务进程”区域，查看以“mczkapp”开头的所有服务进程的“状态”。
- 如果“状态”为“正在运行”，说明ZooKeeper服务进程运行正常，执行3。
- 如果“状态”为“启动中”或“停止中”，单服务的启停时长一般不超过1分钟，如果服务长时间处于该状态，请联系华为技术支持工程师处理。
- 如果“状态”为“故障”、“未知”或“未运行”，说明ZooKeeper服务进程运行异常，请联系华为技术支持工程师处理。
23. 在“服务进程”区域，查看并记录ZooKeeper服务进程名称对应的“节点名称”。
24. 查看ZooKeeper服务所在节点的“连接状态”。
- 在“系统监控”页面左上方，光标移至并选择“CloudSOP-UniEP”。
- 在“节点”页签查看3中记录的节点名称的“连接状态”。
- 如果“连接状态”为“正常”，说明ZooKeeper服务所在节点运行正常，执行5。
- 如果“连接状态”为“断开”，说明ZooKeeper服务所在节点运行异常，请联系华为技术支持工程师处理。
25. 检查告警是否清除。
- 是，处理结束。
- 否，请收集上述告警处理过程中的信息，联系华为技术支持工程师协助解决。
告警清除
当DBHASwitchService连接ZooKeeper恢复正常，自动清除。
##### 参考信息
无。
< 上一节
********.2 系统监控
********.2.1 ALM-151 CPU占用率过高告警
##### 告警解释
部署面会对服务器CPU的使用率进行持续采样。在连续的采样周期（过载次数×15秒）内，每次采样的CPU（Central Processing Unit）使用率均大于等于告警产生阈值时，产生该告警。如果采样周期内有一次CPU使用率采样小于告警产生阈值时，该告警会自动清除。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 151 | 重要 | 越限 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 产生告警的节点名称。 |
| 操作系统 | 服务器的操作系统。 |
| 站点名称 | 产生告警的站点名称。 |
##### 对系统的影响
26. 部署面响应变慢，操作出现延时。
27. 实时上报性能、告警等出现延迟，不能及时获取信息。
28. 业务处理缓慢，可能导致消息堆积。
##### 可能原因
- 部署面临时性的系统繁忙。
- 部署面 CPU使用率的告警产生阈值设置不合理。
- 部署面正在执行消耗系统资源的操作。
- 部署面硬件性能较低，不支持部署面的正常运行。
##### 处理步骤
29. 使用浏览器登录ManageOne部署面。
登录地址：https://ManageOne部署面浮动IP地址:31945。例如，https://***********:31945。
默认帐号：admin，默认密码：*****
30. 检查部署面是否存在多个任务正在执行。
在部署面主菜单中选择“系统 > 任务列表”，等待当前所有任务结束后，查看告警是否清除。
- 是，告警处理结束。
- 否，请执行3。
31. 检查CPU使用率的阈值设置是否合理。
- 在部署面主菜单中选择“产品 > 系统监控”。
- 在“系统监控”界面的“节点”页签右侧单击，检查CPU使用率的“产生告警阈值”和“过载次数”是否设置合理。
- 是，执行4。
- 否，重新设置“产生告警阈值”和“过载次数”为合理的值（缺省值分别为85和40）。告警处理结束。
32. 检查是否存在应用程序CPU占用率超过告警产生阈值。
- 在“节点”页签下，查找产生告警的节点名称。
- 查看对应的CPU使用率是否超过设定的告警产生阈值。
- 是，可能由于应用程序导致CPU资源耗尽，待进程对应的业务处理完毕后，执行7。若无法等待业务处理完毕，请收集上述告警处理过程中的信息，联系华为技术支持工程师协助解决。
- 否，执行5。
33. 检查是否存在非应用程序CPU占用率超过告警产生阈值。
##### - 使用PuTTY工具以sopuser用户通过SSH方式登录告警参数中“主机”对应的IP地址，请在如何查找节点对应的IP地址中查询节点对应的IP地址。
sopuser的默认密码为*****。
- 执行以下命令，切换到ossadm用户。
su - ossadm
ossadm的默认密码为*****。
- 通过执行top命令，在“CPU”列查看对应进程的CPU使用率。
- 是，联系华为技术支持工程师协助解决。告警处理结束。
- 否，执行6。
34. 检查服务器硬件性能是否较低。
服务器硬件性能较低的表现为：
- 系统管理规模对应的硬件要求超过服务器硬件实际能力。
- 经常连续或频繁收到此告警。
检查是否符合以上两个表现：
- 是，联系华为技术支持工程师协助解决。告警处理结束。
- 否，执行7。
35. 等待1分钟，查看本告警是否清除。
- 是，告警处理结束。
- 否，请收集上述告警处理过程中的信息，联系华为技术支持工程师协助解决。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
以下情况会导致之前产生的该告警无法自动被清除，需要手工清除。
36. 产生该告警的节点名称发生了变化。
37. 在告警产生后升级了操作系统或者安装了操作系统补丁。
38. 产生该告警的站点名称发生了变化。
39. 产生该告警的服务器不被监控了。
##### 参考信息
无。
********.2.2 ALM-152 网管服务异常退出告警
##### 告警解释
当部署面检测（检测周期为30秒）到某个业务进程异常退出后，连续10次重启进程失败时，产生该告警。当部署面检测到进程启动后，该告警会自动清除。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 152 | 重要 | 处理错误告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 服务器名 | 产生告警的节点名称 |
| 服务代理 | 产生告警的进程名称 |
| 服务名 | 产生告警的服务实例名称 |
| 站点名称 | 产生告警的站点名称。 |
##### 对系统的影响
进程退出后相关业务功能不可用，并且影响依赖相关功能的服务。
##### 可能原因
- 人为操作导致，例如强行终止了某进程。
- 系统资源不足。
##### 处理步骤
##### 40. 查看告警参数中“服务器名”的节点是否属于“CloudSOP-UniEP”。
- 使用浏览器登录ManageOne部署面。
登录地址：https://ManageOne部署面浮动IP地址:31945。例如，https://***********:31945。
默认帐号：admin，默认密码：*****
- 在登录界面输入用户名admin和密码，单击“登录”。
- 在主菜单中选择“产品 > 系统监控”。
- 在“系统监控”页面左上方，光标移至并选择“CloudSOP-UniEP”。
##### - 在“节点”页签中查看是否存在告警参数中“服务器名”对应的节点名称。
- 是，只需执行2。
- 否，只需执行3。
41. 启动部署面服务。
- 使用PuTTY工具以sopuser用户通过SSH方式登录部署节点。
sopuser的默认密码为*****。
- 执行以下命令，切换到ossadm用户。
su - ossadm
ossadm的默认密码为*****。
- 如果部署面的部署模式是集群场景，请按照先Management0节点再Management1节点的顺序分别执行以下命令，启动OMMHA进程。
> cd /opt/oss/manager/agent/bin
> bash ipmc_adm -cmd startapp -app OMMHAService -tenant manager
系统提示如下回显信息，进程提示“success”，则说明OMMHA进程启动成功。否则请联系华为技术支持工程师。
Starting process ommha-0-0 ... success
- 执行以下命令，启动部署面服务。
> cd /opt/oss/manager/agent/bin
> bash ipmc_adm -cmd startapp -tenant manager
系统提示如下类似回显信息，所有进程都提示“success”，则说明部署面服务启动成功。否则请联系华为技术支持工程师。
Starting process backupwebsite-0-0 ... success
Starting process smapp-0-0 ... success
Starting process cron-0-0 ... success
...
- 查看告警是否清除。
- 是，处理完毕。
- 否，请联系华为技术支持工程师处理。
42. 确保服务进程已启动。
- 在“系统监控”页面左上方，光标移至并选择“ManageOne”。
- 在“节点”页签中，单击产生告警的节点名称。
- 在“进程”页签中，查看服务进程是否为“正在运行”状态。
- 是，执行3.d。
- 否，勾选待启动的进程，单击“启动”，启动异常进程。
- 查看告警是否清除。
- 是，处理完毕。
- 否，请联系华为技术支持工程师处理。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
以下情况会导致之前产生的该告警无法自动被清除，需要手工清除。
43. 产生该告警的节点名称发生了变化。
44. 产生该告警的站点名称发生了变化。
45. 产生该告警的服务器不被监控了。
##### 参考信息
无。
********.2.3 ALM-101208 节点状态异常
##### 告警解释
当部署面连续16次检测（检测周期为15秒）到节点脱管时，将发送该告警。当节点恢复正常时，该告警将自动清除。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 101208 | 重要 | 通信告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 产生告警的节点名称。 |
| 站点名称 | 产生告警的站点名称。 |
##### 对系统的影响
无法登录节点，或者在节点上执行操作时可能报错。
##### 可能原因
- 该节点操作系统无法登录或无响应。
- 该节点所属的虚拟机被下电或虚拟机网络连接异常。
- 该节点的ProductMonitorAgent进程异常。
- 该节点的IR证书过期，系统内部通信异常。
- 如果该节点上存在数据库，则可能是数据库实例的数据库复制状态异常，导致节点异常。
##### 处理步骤
46. 使用浏览器登录ManageOne部署面。
登录地址：https://ManageOne部署面浮动IP地址:31945。例如，https://***********:31945。
默认帐号：admin，默认密码：*****
47. 依次执行表1中的检查项及其检查方法，按照对应的故障排除方法修复故障节点。
导致产品节点故障的因素复杂，本节提供该告警基本的排查方法，如果按照以下操作仍然无法清除该告警，请收集告警处理过程中的信息，联系华为技术支持工程师协助解决。
| 表1 产品节点故障排查 | 表1 产品节点故障排查 | 表1 产品节点故障排查 | 表1 产品节点故障排查 |
| --- | --- | --- | --- |
| 序号 | 检查项 | 检查方法 | 故障排除方法 |
| 1 | 网络连接 | 联系管理员检查网络是否异常。 | 请联系管理员修复网络。 |
| 2 | 虚拟机运行状态 | 联系管理员检查虚拟机是否异常，例如虚拟机是否被下电或者被删除。 | 请联系管理员重启并修复虚拟机。 |
| 3 | 操作系统运行状态 | 重启虚拟机，尝试使用PuTTY工具以sopuser用户通过SSH方式是否能登录故障节点。 | 如果不能正常登录或无响应，则说明故障节点的操作系统异常，请联系技术支持工程师处理。 |
| 4 | ProductMonitorAgent进程运行状态 | 使用PuTTY工具以sopuser用户通过SSH方式登录部署节点。<br>sopuser的默认密码为*****。<br>执行以下命令，切换到ossadm用户。<br>su - ossadm<br>ossadm的默认密码为*****。<br>执行如下命令，检查ProductMonitorAgent进程是否运行正常。<br>> ps -ef |grep ProductMonitorAgent<br>系统提示如下类似回显信息表示ProductMonitorAgent进程正在运行：<br>ossadm    21501      1  2 16:47 ?        00:01:18 /opt/oss/envs/ProductMonitorAgent/service/rtsp/python/bin/python /opt/oss/envs/ProductMonitorAgent/service/tools/pyscript/icAgent.pyc -DNFW=productmonitoragent-0-0 | 如果ProductMonitorAgent进程未运行，执行以下命令启动进程。<br>> . /opt/oss/manager/bin/engr_profile.sh<br>> ipmc_adm -cmd startapp -app ProductMonitorAgent -tenant manager<br>系统提示如下回显信息表示ProductMonitorAgent进程启动成功，否则请联系华为技术支持工程师处理。<br>Starting process productmonitoragent-0-0 ... success |
| 5 | IR证书 | 使用PuTTY工具以sopuser用户通过SSH方式登录部署节点。<br>sopuser的默认密码为*****。<br>执行以下命令，切换到ossadm用户。<br>su - ossadm<br>ossadm的默认密码为*****。<br>执行以下命令，查看IR证书有效期。<br>> cd /opt/oss/manager/etc/ssl/internal<br>> openssl x509 -in server.cer -noout -dates<br>回显以下类似信息，“notAfter”后所显示的时间即为IR证书的到期时间。<br>notBefore=Oct 18 00:00:00 2018 GMT<br>notAfter=Oct 13 00:00:00 2038 GMT<br>如果IR证书已过期，请更新CA证书。<br>如果IR证书没有过期，则表示不是证书过期导致该故障。 | 更新CA证书，具体操作请参见《华为云Stack 6.5.1 安全管理指南》中的“更新CA证书”章节。 |
| 6 | 数据库复制状态 | 请参见ALM-101210 数据库本地主备复制异常。 | 请参见ALM-101210 数据库本地主备复制异常。 |
48. 重新登录部署面，查看节点的状态。
- 如果恢复的节点的状态为“正常”，则告警已修复。
- 如果恢复的节点的状态不为正常，请联系华为技术支持工程师。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
以下情况会导致之前产生的该告警无法自动被清除，需要手工清除。
49. 产生该告警的节点名称发生了变化。
50. 产生该告警的站点名称发生了变化。
51. 产生该告警的服务器不被监控了。
##### 参考信息
无。
********.2.4 ALM-154 内存占用率过高告警
##### 告警解释
当部署面检测（检测周期为15秒）到物理内存占用率大于等于告警产生阈值时，产生该告警。当物理内存占用率小于等于告警清除阈值时，该告警会自动清除。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 154 | 重要 | 越限 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 产生告警的节点名称。 |
| 操作系统 | 服务器的操作系统。 |
| 站点名称 | 产生告警的站点名称。 |
##### 对系统的影响
部署面的响应速度变慢。
##### 可能原因
- 部署面物理内存占用率的产生门限值设置不合理。
- 部署面正在执行消耗系统资源或耗时的操作。
- 业务处理繁忙，物理内存占用率增加。
- 程序处理异常。
##### 处理步骤
52. 使用浏览器登录ManageOne部署面。
登录地址：https://ManageOne部署面浮动IP地址:31945。例如，https://***********:31945。
默认帐号：admin，默认密码：*****
53. 在部署面主菜单中选择“产品 > 系统监控”。
##### 54. 在“系统监控”页面左上方，光标移至并选择告警参数中“主机”对应节点所属的产品。
55. 在“节点”页签右上角单击，检查物理内存占用率的“产生告警阈值”和“清除告警阈值”是否设置合理。
- 是，执行5。
- 否，重新设置物理内存占用率的“产生告警阈值”和“清除告警阈值”为合理的值（缺省值分别为85和80）。告警处理结束。
56. 检查应用程序物理内存占用率。
- 在“节点”页签下，查找产生告警的节点名称。
- 查看对应的物理内存占用率是否超过设定的告警产生阈值。
- 是，可能由于应用程序导致物理内存资源耗尽，待进程对应的业务处理完毕后，执行8。若无法等待业务处理完毕，收集上述告警处理过程中的信息，联系华为技术支持工程师协助解决。
- 否，执行6。
57. 检查非应用程序物理内存占用率最高的进程。
##### - 使用PuTTY工具以sopuser用户通过SSH方式登录告警参数中“主机”对应的IP地址，请在如何查找节点对应的IP地址中查询节点对应的IP地址。
sopuser的默认密码为*****。
- 执行以下命令，切换到ossadm用户。
su - ossadm
ossadm的默认密码为*****。
- 执行以下命令，在“MEM”列查看对应进程的物理内存占用率是否超过设定的告警产生阈值。
> top
...
PID USER      PR  NI    VIRT    RES    SHR S   %CPU  %MEM     TIME+ COMMAND
164860 ossadm    20   0  832312 496564  18164 S 20.199 1.539   1480:48 java
- 是，请华为技术支持工程师协助解决。告警处理结束。
- 否，执行7。
58. 如果物理内存足够，即使业务处理完毕后，物理内存也不会被回收，故告警不会被清除。此时只需确认物理内存占用率是否有继续增加。判断方法如下：
- 在“节点”页签下，查找产生告警的节点名称。
- 查看对应的物理内存占用率是否有持续增加。
- 是，执行8。
- 否，告警处理结束。
59. 等待1分钟，查看本告警是否恢复。
- 是，告警处理结束。
- 否，请收集上述告警处理过程中的信息，联系华为技术支持工程师协助解决。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
以下情况会导致之前产生的该告警无法自动被清除，需要手工清除。
60. 产生该告警的节点名称发生了变化。
61. 在告警产生后升级了操作系统或者安装了操作系统补丁。
62. 产生该告警的站点名称发生了变化。
63. 产生该告警的服务器不被监控了。
##### 参考信息
无。
********.2.5 ALM-36 磁盘占用率过高告警
##### 告警解释
当部署面检测（检测周期为15秒）到磁盘或者某一分区的占用率大于等于告警产生阈值时，产生该告警。当磁盘或者某一分区的占用率小于等于告警清除阈值时，该告警会自动清除。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 36 | 重要 | 越限 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 产生告警的节点名称。 |
| 操作系统 | 服务器的操作系统。 |
| 磁盘 | 产生告警的服务器磁盘名称。 |
| 站点名称 | 产生告警的站点名称。 |
##### 对系统的影响
磁盘占用率过高可能导致部署面服务进行磁盘写操作失败，并可能引发数据库异常。
##### 可能原因
- 部署面磁盘占用率的告警产生阈值设置不合理。
- 磁盘中的无用文件过多。
- 回收站未被清空。
- 部署面收到过多的网元告警、事件、日志，短期内大量数据从数据库中导出到磁盘文件中。
- 保留了过多的暂存的数据文件、备份文件。
##### 处理步骤
64. 使用浏览器登录ManageOne部署面。
登录地址：https://ManageOne部署面浮动IP地址:31945。例如，https://***********:31945。
默认帐号：admin，默认密码：*****
65. 在部署面主菜单中选择“产品 > 系统监控”。
66. 在“系统监控”界面的“节点”页签右侧单击，检查“磁盘分区”的“产生告警阈值”和“清除告警阈值”是否设置合理（缺省值分别为80和75）。
- 是，执行4。
- 否，重新设置阈值为合理的值。如果告警清除，处理结束，否则，执行4。
67. 清理磁盘中的无用文件。
- 使用PuTTY工具以sopuser用户通过SSH方式登录产生告警的节点，请在如何查找节点对应的IP地址中查询节点对应的IP地址。
sopuser的默认密码为*****。
- 执行以下命令，切换到ossadm用户。
su - ossadm
ossadm的默认密码为*****。
- 执行以下命令，切换到root用户。
> su - root
Password: root用户的密码
- 执行以下命令，检查和确认哪些位置磁盘使用率过高。
# df -k
##### 如果发现告警参数中的“磁盘”之外的位置也存在磁盘占用率高但还没有达到告警产生阈值，可以一起处理。
- 执行以下命令，进入磁盘使用率高的目录后查询该位置所有文件、目录大小，并排序后写入文件“du_k.txt”。
# cd filepath
# du -k | sort -nr > /tmp/du_k.txt
- 执行以下命令，查看“du_k.txt”文件，找到引起磁盘空间过高的子目录。
# more /tmp/du_k.txt
- 执行以下命令，进入引起磁盘空间过高的子目录后查询该位置所有文件、目录大小，并排序后写入文件“ls_l.txt”。
# cd filepath
# ls -l | sort -nr > /tmp/ls_l.txt
- 执行以下命令，查看“ls_l.txt”文件，找到引起磁盘空间过高的目录或文件。
# more /tmp/ls_l.txt
- 反复执行4.e到4.h，直到找到引起磁盘空间过高的文件，自行判断哪些文件是无用文件并进行清理。
建议优先清理历史备份的安装包、补丁包、适配层安装包，安装过程中的备份文件，core文件等。可清理的系统及数据库无用文件。执行文件清理后，执行5。
68. 如果无法确认文件是否可以删除，请联系华为技术支持工程师。
69. 等待1分钟，查看本告警是否自动清除。
- 是，告警处理结束。
- 否，请收集上述告警处理过程中的信息，联系华为技术支持工程师协助解决。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
以下情况会导致之前产生的该告警无法自动被清除，需要手工清除。
70. 产生该告警的节点名称发生了变化。
71. 在告警产生后升级了操作系统或者安装了操作系统补丁。
72. 产生该告警的站点名称发生了变化。
73. 产生该告警的硬盘的挂接点发生了变化。
74. 产生该告警的服务器不被监控了。
##### 参考信息
无。
********.2.6 ALM-38 数据库进程异常
##### 告警解释
当数据库进程异常停止时，连续10次启动数据库（间隔10秒）；如果数据库进程重新出现，该告警将会被自动清除。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 38 | 重要 | 处理错误告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 产生告警的节点名称。 |
| 操作系统 | 服务器的操作系统。 |
| 数据库服务 | 产生告警的数据库实例名称。 |
| 站点名称 | 产生告警的站点名称。 |
##### 对系统的影响
数据库进程异常会导致业务访问数据库失败，涉及到数据读取的故障、安全菜单操作都无法进行，只能进行拓扑菜单操作。如果长时间异常会导致告警信息丢失或者业务面不可用。
##### 可能原因
数据库进程异常停止。
##### 处理步骤
75. 检查数据库是否异常。并尝试重启异常数据库。
- 使用浏览器登录ManageOne部署面。
登录地址：https://ManageOne部署面浮动IP地址:31945。例如，https://***********:31945。
默认帐号：admin，默认密码：*****
- 在主菜单中选择“产品 > 系统监控”。
- 在“系统监控”页面左上方，光标移至依次选择所有的产品。在界面右上方，检查“关系数据库”和“Redis数据库”是否存在异常资源。
红色数字为异常资源的个数。
- 是，尝试启动异常数据库实例。
- 选择“节点”页签，在异常的数据库实例所在节点所在行，单击右侧的。
- 等待任务结束后，检查数据库状态，如果数据库状态恢复正常，告警清除，则处理完毕。否则，继续排查其他原因。
- 否，表示非数据库运行异常的原因，继续排查其他原因。
76. 尝试登录数据库，检查数据库密码是否正确。具体操作请参见《华为云Stack 6.5.1 故障处理》中的“数据库常用操作”章节。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
以下情况会导致之前产生的该告警无法自动被清除，需要手工清除。
77. 产生该告警的节点名称发生了变化。
78. 产生该告警的站点名称发生了变化。
79. 产生该告警的服务器不被监控了。
##### 参考信息
无。
********.2.7 ALM-47 服务内存占用过高告警
##### 告警解释
系统每隔15秒检测一次服务占用内存，当系统连续40次检测到服务占用内存大于等于用户设定的告警门限值时，产生该告警；只要有一次服务占用内存小于用户设定的告警门限值时，该告警将会被自动清除。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 47 | 重要 | 越限 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 产生告警的节点名称。 |
| 操作系统 | 服务器的操作系统。 |
| 服务名 | 产生告警的服务进程名称。 |
| 站点名称 | 产生告警的站点名称。 |
| 门限值 | 告警产生门限和清除门限。 |
| 占用值 | 服务使用内存的数值。 |
##### 对系统的影响
业务面服务器的响应速度变慢。
##### 可能原因
程序处理异常。
##### 处理步骤
80. 登录部署面。
- 打开浏览器，在地址栏中输入“https://部署面的客户端登录IP地址:31945”，按“Enter”。
如果管理面的部署模式是集群模式，请使用管理节点浮动IP地址进行登录。
- 在登录界面输入用户名admin和密码，单击“登录”。
81. 在部署面主菜单中选择“产品 > 系统监控”。
##### 82. 在“系统监控”页面左上方，光标移至并选择告警参数中“主机”对应节点所属的产品。
83. 在“节点”页签，单击产生告警的节点名称。
84. 在“进程”页签中查找告警产生的进程名称，选中产生告警的进程名称，先单击“停止”，待状态转为“未运行”后再单击“启动”。
从告警定位信息中查找服务名来确定进程名称。
85. 待进程启动成功后，等待5分钟，检查告警是否清除。
- 是，处理结束。
- 否，请联系华为技术支持工程师处理。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
以下情况会导致之前产生的该告警无法自动被清除，需要手工清除。
86. 产生该告警的节点名称发生了变化。
87. 产生该告警的站点名称发生了变化。
88. 产生该告警的服务器不被监控了。
##### 参考信息
无。
********.2.8 ALM-101206 SSH管理通道故障
##### 告警解释
当部署面连续4次检测（检测周期为60秒）到部署节点和产品节点之间的SSH连接异常时，将产生此告警。当部署节点和产品节点之间的SSH连接恢复正常，此告警将自动清除。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 101206 | 紧急 | 处理出错告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 产生告警的节点名称。 |
| 站点名称 | 产生告警的站点名称。 |
##### 对系统的影响
无法通过部署面对相应节点进行管理，影响相应节点的系统监控、备份恢复等功能。
##### 可能原因
- 产品节点状态异常。
- 部署节点和产品节点之间的网络连接异常。
- 产品节点的ossadm用户密码失效。
- 部署节点和产品节点之间的SSH信任关系损坏。
##### 处理步骤
##### 89. 查看是否存在告警“101208 节点状态异常”，并且该告警和本告警的告警参数“主机”对应的节点名称相同。
- 是，处理异常告警，具体操作请参见ALM-101208 节点状态异常。
- 否，执行2。
90. 使用PuTTY工具以sopuser用户通过SSH方式登录部署节点。
sopuser的默认密码为*****。
91. 执行以下命令，切换到ossadm用户。登录产生告警的节点。
su - ossadm
ossadm的默认密码为*****。
92. 执行以下命令，测试部署节点与产品节点的SSH连通性。
##### > ssh 告警参数中的节点IP地址
##### 如果告警参数中“主机”对应的节点是部署节点，则登录部署节点连接任意一个产品节点测试SSH连通性。
- 如果不输入密码就可以成功登录节点，说明部署节点和产品节点的SSH互信关系正常，执行4.f。
- 如果显示以下回显信息，说明节点ossadm用户密码失效，请更新密码后再次测试部署节点与产品节点的SSH连通性。
WARNING: Your password has expired.
产品节点ossadm用户密码需要和部署节点ossadm用户密码保持一致。若产品节点与部署节点ossadm用户密码不一致，请参见《华为云Stack 6.5.1 安全管理指南》附件中的《华为云Stack 6.5.1 帐户一览表》帐户一览表进行修改。
- 如果要求输入ossadm用户密码，说明部署节点和产品节点的SSH互信关系异常，请执行以下操作恢复SSH互信关系。
- 按“Ctrl+c”结束当前操作。
- 执行以下命令，打开部署节点的“id_rsa.pub”文件。
> vi <ossadm用户的家目录>/.ssh/id_rsa.pub
- 将“id_rsa.pub”文件中的内容复制到本地，复制完成后，按“Esc”并输入“:q!”关闭“id_rsa.pub”文件。
- 使用PuTTY工具以sopuser用户通过SSH方式登录部署节点。
sopuser的默认密码为*****。
- 执行以下命令，切换到ossadm用户。登录待恢复SSH信任关系的节点。
su - ossadm
ossadm的默认密码为*****。
- 执行以下命令，打开待恢复SSH信任关系的节点的“authorized_keys”文件。
> vi <ossadm用户的家目录>/.ssh/authorized_keys
以上命令会打开vi编辑器，打开vi编辑器后，按“i”并将3.c中获取的内容复制并添加到“authorized_keys”文件的末端。
- 配置完成后，按“Esc”并输入“:wq!”保存“authorized_keys”文件。操作完成后，并再次测试部署节点与产品节点的SSH连通性。
93. 查看告警是否清除。
- 是，处理完毕。
- 否，请联系华为技术支持工程师处理。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
以下情况会导致之前产生的该告警无法自动被清除，需要手工清除。
94. 产生该告警的节点名称发生了变化。
95. 产生该告警的站点名称发生了变化。
96. 产生该告警的服务器不被监控了。
##### 参考信息
无。
********.2.9 ALM-53080 部署面服务进程资源占用异常
##### 告警解释
当部署面连续20次检测（检测周期30秒）到部署面服务进程资源的CPU使用率大于等于告警门限值（默认值为90%）时，产生该告警。只要有一次检测到部署面服务进程资源的CPU使用率小于告警门限值时，该告警将会被自动清除。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 53080 | 重要 | 越限 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 产生告警的节点名称。 |
| 操作系统 | 服务器的操作系统。 |
| 进程名称 | 产生告警的进程名称。 |
| 站点名称 | 产生告警的站点名称。 |
##### 对系统的影响
业务处理缓慢，可能导致消息堆积或系统崩溃。
##### 可能原因
- 非重要进程未及时释放。
- 有占用系统资源大的进程正在运行。
##### 处理步骤
97. 使用浏览器登录ManageOne部署面。
登录地址：https://ManageOne部署面浮动IP地址:31945。例如，https://***********:31945。
默认帐号：admin，默认密码：*****
98. 在主菜单中选择“系统 > 任务列表”，查看当前是否有任务正在执行。
- 有正在执行的任务。待任务完成后等待半个小时，检查告警是否清除。
- 是，告警处理完毕。
- 否，请执行3。
- 没有正在执行的任务，请执行3。
99. 判断该告警出现频率是否过高。
- 如果该告警仅偶尔出现且会自动清除，可直接忽略，无需处理。
- 如果该告警出现频繁（一个小时内出现5次以上），且不会自动清除，请联系华为技术支持工程师协助解决。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
以下情况会导致之前产生的该告警无法自动被清除，需要手工清除。
100. 产生该告警的节点名称发生了变化。
101. 在告警产生后升级了操作系统或者安装了操作系统补丁。
102. 产生该告警的站点名称发生了变化。
103. 产生该告警的服务器不被监控了。
##### 参考信息
无。
********.2.10 ALM-101210 数据库本地主备复制异常
##### 告警解释
当部署面连续两次检测（检测周期为100秒）到数据库实例的复制状态异常或者延迟时，产生该告警；当数据库实例的复制状态恢复正常时，该告警会自动清除。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 101210 | 重要 | 处理错误告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 产生告警的节点名称。 |
| 操作系统 | 服务器的操作系统。 |
| 数据库服务 | 产生告警的数据库实例名称。 |
| 数据库类型 | 产生告警的数据库类型。 |
| 站点名称 | 产生告警的站点名称。 |
##### 对系统的影响
数据库主备复制状态异常会导致主备数据库数据不同步，如果长时间异常会导致业务服务不可用。
如果是异地容灾场景，可能会影响容灾操作。
##### 可能原因
- 主数据库实例运行异常。
- 备数据库实例运行异常。
- 主备数据库实例所在的节点异常。
- 主备数据库实例所在的节点之间通讯异常。
##### 处理步骤
104. 使用PuTTY工具以sopuser用户通过SSH方式登录部署节点。
sopuser的默认密码为*****。
105. 执行以下命令，切换到ossadm用户。登录产生告警的节点。
su - ossadm
ossadm的默认密码为*****。
106. 执行以下命令，查询数据库实例复制状态。
> cd /opt/oss/manager/apps/DBAgent/bin/
> bash dbsvc_adm -cmd query-db-instance
系统提示如下类似回显信息：
DBInstanceId                             ... IP           Port  ... Role  Rpl Status     ...
apmdbsvr-10_90_73_163-3@10_90_73_164-3   ... ************ 32082 ... Slave Normal         ...
apmdbsvr-10_90_73_178-21@10_90_73_179-21 ... ************ 32080 ... Slave Abnormal (101) ...
apmdbsvr-10_90_73_178-21@10_90_73_179-21 ... ************ 32080 ... Slave Abnormal (103)    ...
...
- 如果“Rpl Status”的值为“--”，表示该数据库实例为单实例，执行5。
- 如果“Rpl Status”的值为“Normal”，表示该数据库实例的复制状态正常，执行5。
- 如果“Rpl Status”的值为“Building”，表示正在重建备数据库实例，并将主数据库实例的数据强制全量同步至备数据库实例。待“Rpl Status”的值查询到成为“Normal”后，执行5。
- 如果“Rpl Status”的值为“Delay”，表示备数据库实例正在同步主数据库实例上的数据。待“Rpl Status”的值查询到成为“Normal”后，执行5。
- 如果“Rpl Status”的值为“Abnormal”，表示数据库复制状态异常，后面括号中数字为错误码，执行4。
107. 根据3中的错误码。参考下表排查原因并处理。
| 表1 数据库复制状态错误码参考 | 表1 数据库复制状态错误码参考 | 表1 数据库复制状态错误码参考 | 表1 数据库复制状态错误码参考 |
| --- | --- | --- | --- |
##### | 错误码 | 说明 | 可能原因 | 处理建议 |
| 101 | 数据库实例所在节点停止，或该数据库实例停止。 | 该数据库实例所在节点未启动。<br>该数据库实例未启动。<br>该数据库实例所在节点磁盘存储空间已满。<br>主备数据库实例所在节点的网络通信异常。 | 使用浏览器登录ManageOne部署面。<br>登录地址：https://ManageOne部署面浮动IP地址:31945。例如，https://***********:31945。<br>默认帐号：admin，默认密码：*****<br>在部署面主菜单中选择“产品 > 系统监控”。<br>在“节点”页签中查看所有节点的“连接状态”和“数据库状态”是否正常。<br>是，观察2分钟，如果故障仍未恢复，请联系华为技术支持工程师。<br>否，选择停止的节点，单击界面右侧的“启动”，启动停止的节点。 |
| 102 | 数据库实例角色错误，出现双主。 | 人为将该数据库实例所在节点设置为忽略节点。 | 使用PuTTY工具以sopuser用户通过SSH方式登录部署节点。<br>sopuser的默认密码为*****。<br>执行以下命令，切换到ossadm用户。登录产生告警的节点。<br>su - ossadm<br>ossadm的默认密码为*****。<br>执行以下命令确认是否设置了节点为忽略节点：<br>> cd /opt/oss/manager/apps/DBHASwitchService/bin<br>> ./switchtool.sh -cmd get-ignore-nodes<br>回显类似如下信息：<br>ignore-nodes:2; dbtype:Gauss<br>其中，“ignore-node”的值，表示忽略的节点ID，为“None”时表示没有设置忽略节点，“dbtype”的值表示忽略的数据库类型。<br>如果是人为对主备实例所在节点设置忽略节点，请执行以下命令取消：<br>> cd /opt/oss/manager/apps/DBHASwitchService/bin<br>> ./switchtool.sh -cmd del-ignore-nodes<br>如果未显示Successful表示执行失败，请联系华为技术支持工程师处理。 |
| 103 | 数据库实例角色错误，出现双备。 | 人为将该数据库实例所在节点设置为忽略节点。 | 使用PuTTY工具以sopuser用户通过SSH方式登录部署节点。<br>sopuser的默认密码为*****。<br>执行以下命令，切换到ossadm用户。登录产生告警的节点。<br>su - ossadm<br>ossadm的默认密码为*****。<br>执行以下命令确认是否设置了节点为忽略节点：<br>> cd /opt/oss/manager/apps/DBHASwitchService/bin<br>> ./switchtool.sh -cmd get-ignore-nodes<br>回显类似如下信息：<br>ignore-nodes:2; dbtype:Gauss<br>其中，“ignore-node”的值，表示忽略的节点ID，为“None”时表示没有设置忽略节点，“dbtype”的值表示忽略的数据库类型。<br>如果是人为对主备实例所在节点设置忽略节点，请执行以下命令取消：<br>> cd /opt/oss/manager/apps/DBHASwitchService/bin<br>> ./switchtool.sh -cmd del-ignore-nodes<br>如果未显示Successful表示执行失败，请联系华为技术支持工程师处理。 |
| 104 | 数据库实例角色错误，角色与分布式管理服务上的记录不一致。 | 人为将该数据库实例所在节点设置为忽略节点。 | 使用PuTTY工具以sopuser用户通过SSH方式登录部署节点。<br>sopuser的默认密码为*****。<br>执行以下命令，切换到ossadm用户。登录产生告警的节点。<br>su - ossadm<br>ossadm的默认密码为*****。<br>执行以下命令确认是否设置了节点为忽略节点：<br>> cd /opt/oss/manager/apps/DBHASwitchService/bin<br>> ./switchtool.sh -cmd get-ignore-nodes<br>回显类似如下信息：<br>ignore-nodes:2; dbtype:Gauss<br>其中，“ignore-node”的值，表示忽略的节点ID，为“None”时表示没有设置忽略节点，“dbtype”的值表示忽略的数据库类型。<br>如果是人为对主备实例所在节点设置忽略节点，请执行以下命令取消：<br>> cd /opt/oss/manager/apps/DBHASwitchService/bin<br>> ./switchtool.sh -cmd del-ignore-nodes<br>如果未显示Successful表示执行失败，请联系华为技术支持工程师处理。 |
| 200 | 主备数据库实例网络通信异常。 | 备从数据库实例和主数据库实例的IO通信异常，IO线程“Slave_IO_Running”异常。 | 使用浏览器登录ManageOne部署面。<br>登录地址：https://ManageOne部署面浮动IP地址:31945。例如，https://***********:31945。<br>默认帐号：admin，默认密码：*****<br>在部署面主菜单中选择“产品 > 系统监控”。<br>查看所有数据库页签中，数据库实例的“复制状态”是否正常。<br>是，观察2分钟，如果故障仍未恢复，请联系华为技术支持工程师。<br>否，选择“节点”页签，选择异常节点。在界面右侧单击“停止”，等节点停止成功后，再单击“启动”，重启故障节点。 |
| 201 | 备数据库实例GTID复制延迟。<br>说明： <br>每一个GTID代表一个数据库事务。 | 短时间有大量数据库写操作，导致主备数据库复制处理延迟。<br>备数据库GTID落后主数据库实例1000及以上。 | 观察2分钟，如果该告警还未恢复，或经常出现复制延迟，请联系华为技术支持工程师。 |
| 210 | 备数据库实例的数据库线程异常。 | 备数据库数据库进程“Slave_SQL_Running”异常。<br>人为使用dbuser用户对备实例违规进行写操作。 | 使用PuTTY工具以sopuser用户通过SSH方式登录部署节点。<br>sopuser的默认密码为*****。<br>执行以下命令，切换到ossadm用户。登录产生告警的节点。<br>su - ossadm<br>ossadm的默认密码为*****。<br>执行以下命令，恢复异常数据库实例。<br>> cd /opt/oss/manager/apps/UniEPService/tools/DB_Recovery<br>> bash DBSlaveInstance_Recovery.sh -instid 数据库实例名称 -tenant 产品名称<br>系统提示如下类似回显信息时，表示该数据库实例恢复成功；否则请联系华为技术支持工程师。<br>Recovery DB-Instance Success. |
| 211 | 备数据库实例的GTID比主数据库实例多。 | 人为使用dbuser用户对备实例违规进行写操作。 | 使用PuTTY工具以sopuser用户通过SSH方式登录部署节点。<br>sopuser的默认密码为*****。<br>执行以下命令，切换到ossadm用户。登录产生告警的节点。<br>su - ossadm<br>ossadm的默认密码为*****。<br>执行以下命令，恢复异常数据库实例。<br>> cd /opt/oss/manager/apps/UniEPService/tools/DB_Recovery<br>> bash DBSlaveInstance_Recovery.sh -instid 数据库实例名称 -tenant 产品名称<br>系统提示如下类似回显信息时，表示该数据库实例恢复成功；否则请联系华为技术支持工程师。<br>Recovery DB-Instance Success. |
| 212 | 双主模式，GTID有数据冲突。 | 故障倒换前有部分数据未复制到备数据库实例，倒换后原来主数据库实例出现数据冲突。 | 使用PuTTY工具以sopuser用户通过SSH方式登录部署节点。<br>sopuser的默认密码为*****。<br>执行以下命令，切换到ossadm用户。登录产生告警的节点。<br>su - ossadm<br>ossadm的默认密码为*****。<br>执行以下命令，恢复异常数据库实例。<br>> cd /opt/oss/manager/apps/UniEPService/tools/DB_Recovery<br>> bash DBSlaveInstance_Recovery.sh -instid 数据库实例名称 -tenant 产品名称<br>系统提示如下类似回显信息时，表示该数据库实例恢复成功；否则请联系华为技术支持工程师。<br>Recovery DB-Instance Success. |
| 213 | 主备倒换时出现异常，导致数据冲突。 | 故障倒换前数据还没有完全写入主数据库即发生主备倒换，导致数据冲突。 | 使用PuTTY工具以sopuser用户通过SSH方式登录部署节点。<br>sopuser的默认密码为*****。<br>执行以下命令，切换到ossadm用户。登录产生告警的节点。<br>su - ossadm<br>ossadm的默认密码为*****。<br>执行以下命令，恢复异常数据库实例。<br>> cd /opt/oss/manager/apps/UniEPService/tools/DB_Recovery<br>> bash DBSlaveInstance_Recovery.sh -instid 数据库实例名称 -tenant 产品名称<br>系统提示如下类似回显信息时，表示该数据库实例恢复成功；否则请联系华为技术支持工程师。<br>Recovery DB-Instance Success. |
| 301 | 备实例复制延迟。 | 短时间有大量数据库写操作，导致主备数据库复制处理延迟。 | 观察2分钟，如果该告警还未恢复，请联系华为技术支持工程师。 |
| 302 | 备实例正在启动。 | 中间状态。 | 观察2分钟，如果该告警还未恢复，请联系华为技术支持工程师。 |
| 303 | 备数据库需要手工重建。 | 该数据库实例所在节点未启动。<br>该数据库实例未启动。<br>主备节点网络通信异常。<br>主备数据库版本不一致。<br>主备实例连接异常。 | 观察2分钟，如果该告警还未恢复，请联系华为技术支持工程师。 |
| 304 | 主实例正在降备。 | 中间状态。 | 观察2分钟，如果该告警还未恢复，请联系华为技术支持工程师。 |
| 305 | 备实例正在降为级联备实例。 | 中间状态。 | 观察2分钟，如果该告警还未恢复，请联系华为技术支持工程师。 |
| 306 | 备实例正在升主。 | 中间状态。 | 观察2分钟，如果该告警还未恢复，请联系华为技术支持工程师。 |
| 307 | 未知错误。 | 未知错误。 | 观察2分钟，如果该告警还未恢复，请联系华为技术支持工程师。 |
| 310 | 备数据库需要重建，系统自动修复。 | 备数据库实例待同步的数据在主数据库实例上不存在。<br>主备数据库实例的数据库目录不是同一个数据库创建。<br>主备数据库实例中数据的时间不匹配。 | 观察2分钟，如果该告警还未恢复，请联系华为技术支持工程师。 |
| 401 | 备实例复制延迟。 | 短时间有大量数据库写操作，导致主备数据库复制处理延迟。 | 等待2-3分钟会自动修复，如果过一段时间还未修复成功，请联系华为技术支持工程师。 |
| 402 | 备实例正在启动。 | 中间状态。 | 等待2-3分钟会自动修复，如果过一段时间还未修复成功，请联系华为技术支持工程师。 |
| 403 | 备数据库需要重建，系统自动修复。 | 备数据库实例待同步的数据在主机上不存在。<br>主备数据库实例的数据库目录不是同一个数据库创建。<br>主备数据库实例中数据的时间不匹配。 | 等待2-3分钟会自动修复，如果过一段时间还未修复成功，请联系华为技术支持工程师。 |
| 404 | 主实例正在降备。 | 中间状态。 | 等待2-3分钟会自动修复，如果过一段时间还未修复成功，请联系华为技术支持工程师。 |
| 405 | 备实例正在降备。 | 中间状态。 | 等待2-3分钟会自动修复，如果过一段时间还未修复成功，请联系华为技术支持工程师。 |
| 406 | 手工倒换后，备实例正在升主。 | 手工倒换后，备实例正在升主。 | 等待2-3分钟会自动修复，如果过一段时间还未修复成功，请联系华为技术支持工程师。 |
| 407 | 故障倒换后，备实例正在升主。 | 故障倒换后，备实例正在升主。 | 等待2-3分钟会自动修复，如果过一段时间还未修复成功，请联系华为技术支持工程师。 |
| 408 | 备数据库正在重建。 | 中间状态。 | 等待2-3分钟会自动修复，如果过一段时间还未修复成功，请联系华为技术支持工程师。 |
| 409 | 无处理。 | 无处理。 | 等待2-3分钟会自动修复，如果过一段时间还未修复成功，请联系华为技术支持工程师。 |
| 410 | 备实例与主实例断开。 | 该数据库主实例所在节点未启动。<br>该数据库主实例未启动。<br>主备节点网络通信异常。 | 使用浏览器登录ManageOne部署面。<br>登录地址：https://ManageOne部署面浮动IP地址:31945。例如，https://***********:31945。<br>默认帐号：admin，默认密码：*****<br>在部署面主菜单中选择“产品 > 系统监控”。<br>在“节点”页签中查看所有节点的“连接状态”和“数据库状态”是否正常。<br>是，观察2分钟，如果故障仍未恢复，请联系华为技术支持工程师。<br>否，选择停止的节点，单击界面右侧的“启动”，启动停止的节点。 |
| 411 | 未知错误。 | 未知错误。 | 等待2-3分钟会自动修复，如果过一段时间还未修复成功，请联系DB管理员定位。 |
108. 等待2分钟，查看该告警是否恢复。
- 是，告警处理结束。
- 否，请收集上述告警处理过程中的信息，联系华为技术支持工程师协助解决。
告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
以下情况会导致之前产生的该告警无法自动被清除，需要手工清除。
109. 产生该告警的节点名称发生了变化。
110. 在告警产生后升级了操作系统或者安装了操作系统补丁。
111. 产生该告警的站点名称发生了变化。
112. 产生该告警的服务器不被监控了。
##### 参考信息
无。
< 上一节
********.3 安全管理
********.3.1 ALM-51020 证书即将过期
##### 告警解释
系统每天对部署面和业务面的ER证书、CA证书和IR证书的有效期进行一次检查，当任意一个证书的有效期不足90天时，产生该告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 51020 | 重要 | 处理错误告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 产生告警的节点IP地址。 |
| 证书类型 | ER证书、CA证书或者IR证书。 |
| 站点名称 | 产生告警的站点名称。 |
##### 对系统的影响
113. 如果ER证书失效，将无法登录部署面或业务面界面。
114. 如果CA证书失效，将无法对IR证书及其密钥进行安全管理。
115. 如果IR证书失效，将影响系统内部通信。
##### 可能原因
证书有效期已不足90天。
##### 处理步骤
手工更新对应的证书，具体操作请参见《华为云Stack 6.5.1 安全管理指南》手册中“证书管理”对应证书类型的更新步骤。
告警清除
系统在更新证书后的00:00:00检查证书的有效性，证书有效则系统会自动清除此告警，无需手工清除。
##### 参考信息
无。
********.3.2 ALM-51021 证书已经过期
##### 告警解释
系统每天对部署面和业务面的ER证书的有效期进行一次检查，若证书的有效期过期或者证书无效时，产生该告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 51021 | 紧急 | 处理错误告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 产生告警的节点IP地址。 |
| 证书类型 | ER证书。 |
| 站点名称 | 产生告警的站点名称。 |
##### 对系统的影响
如果ER证书失效，将无法登录部署面或业务面界面。
##### 可能原因
证书已超过有效期。
##### 处理步骤
根据已经过期的证书类型，处理证书过期问题。
116. 如果部署面或运营面的ER证书已经过期，则登录运维面更换部署面或运营面的ER证书。具体操作请参见《华为云Stack 6.5.1 安全管理指南》手册中“证书管理”对应证书类型的更新步骤。
117. 如果运维面的ER证书已经过期，此时运维面无法登录，则需要更新运维面的ER证书后，再执行其他证书的更新操作。具体操作请参见《华为云Stack 6.5.1 故障处理》中的“ER证书过期导致运维、运营面无法登录”。
告警清除
系统在更新证书后的00:00:00检查证书的有效性，证书有效则系统会自动清除此告警，无需手工清除。
##### 参考信息
无。
********.3.3 ALM-51022 证书更新失败
##### 告警解释
系统在更新证书失败时上报该告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 51022 | 紧急 | 处理错误告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 证书类型 | ER证书或者IR证书。 |
| 站点名称 | 产生告警的站点名称。 |
##### 对系统的影响
系统无法登录和使用。
##### 可能原因
新证书不符合要求。
##### 处理步骤
118. 手工恢复证书。具体操作请参见《华为云Stack 6.5.1 安全管理指南》中的“恢复更新失败的ER\IR证书”章节。
119. 检查该告警是否清除。
- 是，处理结束。
- 否，请联系华为技术支持工程师处理。
告警清除
恢复证书后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。
< 上一节
##### ********.4 参考信息
以下介绍查找节点对应的管理IP地址的方法。
< 上一节
********.4.1 如何查找节点对应的IP地址
以下介绍查找节点对应的管理IP地址的方法。
操作步骤
120. 启动浏览器，在地址栏中输入https://部署面的客户端登录IP地址:31945，按“Enter”。
121. 输入用户名、密码，单击“登录”。
122. 在主菜单中，选择“产品 > 系统监控”，在“系统监控”页面左上方，光标移至并选择对应的产品。然后进入“系统监控”页面的“节点”页签。
123. 在“节点名称”列查找待查询管理IP地址的节点。
124. 单击节点名称，在节点详情页面上方的IP地址即为该节点的管理IP地址。
******** IAM 告警参考
< 上一节