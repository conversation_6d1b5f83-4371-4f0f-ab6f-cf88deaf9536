# 5.2.4.18.1 ALM-101209 节点故障倒换

5.2.4.18.1.1 ALM-101209 节点故障倒换
告警解释
当主节点发生故障导致OMMHA倒换，或者手工倒换，倒换完成后，会产生该告警。当倒换完成且OMMHA状态正常时，该告警会自动清除。
告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 101209 | 紧急 | 保护倒换 |
告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 节点名称 | 倒换后的主节点名称。 |
| 站点名称 | 产生告警的站点名称。 |
对系统的影响
- 浮动IP地址从主节点切换到备节点。
- 浮动IP地址切换过程中，部署面无法登录。
- 原主节点的single进程包括critical进程会被停掉，原备节点升为主节点过程中，该节点的single进程包括critical进程会被启动。
可能原因
- 原主节点虚拟机所在的硬件故障。
- 原主节点异常断电。
- 原主节点critical类型的进程异常。
- 原主节点CPU、内存耗用过高。
- 原主节点所在网络故障。
- 浮动IP地址挂载不成功。
- 用户进行了手工倒换。
处理步骤
1. 检查原主节点虚拟机所在硬件是否故障、原主节点是否发生异常断电。
如果由于原主节点虚拟机所在硬件故障或原主节点异常断电导致OMMHA倒换，请联系管理员修复故障。
2. 检查主节点进程状态是否正常。
- 使用PuTTY工具以sopuser用户通过SSH方式登录产生告警的节点。请在如何查找节点对应的IP地址中查询节点对应的IP地址
sopuser的默认密码为*****。
- 执行以下命令，切换到ossadm用户。
su - ossadm
ossadm的默认密码为*****。
- 执行如下命令，查询主备节点的状态：
> cd /opt/oss/Product/apps/OMMHAService/bin
> bash status.sh
如果是部署节点，/opt/oss/Product的值为/opt/oss/manager。
系统回显类似如下提示信息：
NodeName    HostName    ...    HAAllResOK    HARunPhase
ha2         Service-2   ...    normal        Actived
ha1         Service-1   ...    normal        Deactived
NodeName    ResName     ...    ResHAStatus    ResType
ha2         RMCritical  ...    Normal         Active_standby
ha2         RMFloatIp   ...    Normal         Single_active
ha2         RMIrNic     ...    Normal         Single_active
ha1         RMCritical  ...    Normal         Active_standby
ha1         RMFloatIp   ...    Normal         Single_active
ha1         RMIrNic     ...    Normal         Single_active
- 如果回显信息中的“HAAllResOK”和“ResHAStatus”均为“normal”状态，则表示倒换完成，且状态正常，该告警将自动清除。
- 如果“HAAllResOK”和“ResHAStatus”存在“normal”以外的状态，则表示存在节点故障或倒换未完成。
3. 等待3分钟，检查告警是否清除。
- 是，处理结束。
- 否，请收集上述告警处理过程中的信息，联系华为技术支持工程师协助解决。
参考信息
无。