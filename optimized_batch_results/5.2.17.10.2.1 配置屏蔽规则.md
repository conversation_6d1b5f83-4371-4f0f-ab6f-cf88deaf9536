# 5.2.17.10.2 5.2.17.10.2.1 配置屏蔽规则

5.2.17.10.2.1 配置屏蔽规则
对于设备上报而不需要关注的告警/事件，可以通过创建屏蔽规则，使后续上报且符合屏蔽规则的告警/事件不会显示在当前告警和事件列表中。
前提条件
- 已具备“告警设置”的操作权限。
- 用户只能对管理对象的规则进行启停、增删改和导入，对非管理对象的规则只能查看和导出。
背景信息
- 屏蔽规则只会屏蔽建立规则之后上报的告警/事件，对于已上报的告警/事件不会有影响。
- 停用、删除或修改屏蔽规则后，不会重复上报停用、删除或修改之前已经被屏蔽的告警/事件。
- 屏蔽规则是以告警的原始属性（重定义前的属性）来进行屏蔽的。如果针对某条告警既设置了级别重定义规则，又设置了屏蔽规则，则屏蔽规则先起作用，然后重定义规则才起作用。
- 最多支持创建200条屏蔽规则。
- 同时满足以下条件的屏蔽规则会屏蔽所有告警/事件，将导致告警/事件无法上报。
- 屏蔽规则已启用。
- 屏蔽规则选择了所有告警/事件源。
- 屏蔽规则选择了所有告警/事件级别。
- 屏蔽规则没有选择生效开始时间、生效结束时间。
- 创建事件屏蔽规则与创建告警屏蔽规则的操作类似，本操作以创建告警屏蔽规则为例，如需创建事件屏蔽规则请在“屏蔽规则”页面中单击“创建” ，选择“ 事件屏蔽规则”。
操作步骤
1. 在主菜单中选择“集中告警 > 集中告警 > 告警设置”。
2. 在左侧导航树中选择“屏蔽规则”。
3. 在“屏蔽规则”页面中，单击“创建” ，选择“ 告警屏蔽规则”。
4. 设置规则的名称、告警源、规则生效的告警级别，用户还可在“指定告警”中选择需要屏蔽的告警。
- 只有管理对象为所有资源的用户可以选择“所有告警源”。
- 在设置“告警源”时，选择“所有告警源”将对系统自身和所有管理对象产生的符合条件的告警进行屏蔽，请谨慎使用。
5. 在“条件”区域中，设置告警参数条件来筛选需要屏蔽的告警。
例如需要根据告警的定位信息进行筛选，则选择“定位信息”、“包含”，并输入对应的目标告警的定位信息关键字段。
6. 设置规则执行的时间，用户可根据需要选择规则的生效时间和生效周期。
“时间条件”中所有条件缺省为去勾选，表示规则在所有时间下生效。
7. 屏蔽后的告警可选择“丢弃”或显示在“被屏蔽告警”列表中。
创建事件屏蔽规则时，被屏蔽的事件只能被丢弃。
8. 设置规则的优先级，当两条屏蔽规则屏蔽同一条告警时，优先级高的规则生效。
9. 选择是否启用规则，单击“确定”完成配置。
相关任务
10. 若需要重新接收已屏蔽的告警/事件，在主菜单中选择“集中告警 > 集中告警 > 告警设置”。在左侧导航树中选择“屏蔽规则”，在屏蔽规则列表中选择要停止的屏蔽规则，单击“删除”或“停止”。“删除”将永久删除该屏蔽规则；“停止”将停止执行该屏蔽规则，在需要重新启用屏蔽规则时可单击“启用”。
11. 用户可导入/导出屏蔽规则，以便统计、审核、迁移规则。在“屏蔽规则”页面中，单击“导入规则”或“导出规则”进行操作。
- 导入的文件格式要求是XLS，大小不能超过1M。最多导入200条规则，若导入的规则名称已存在，则替代原有规则。
- 导出文件已隐藏第一行，请不要修改第一行的文字，否则会导致导入失败。
任务示例
场景描述
运维人员计划于20XX年4月的每周一凌晨1点对设备A进行调测操作，每次调测在2个小时以内完成，设备A承载的业务届时会中断。这个期间产生的告警是可预测到的，且不需要被处理，需要配置屏蔽规则屏蔽这些告警，以避免这些告警被关注。
配置步骤
12. 在“屏蔽规则”页面中，单击“创建”，选择“告警屏蔽规则”。
13. 设置“规则名称”。
14. 选择“自定义告警源”，在自定义告警源中添加设备A。
15. 在“时间条件”区域中，设置“生效时间”为“20XX-04-01 00:00:00到20XX-04-30 23:59:59”，设置“生效周期”的“按天”为“星期一”，“按时段”为“01:00到03:00”。
16. 在“动作”区域中，选择“屏蔽后的告警”为“显示在“被屏蔽告警”中”。
17. 其他参数“条件”、“告警级别”、“启用”保持缺省设置。
18. 单击“确定”。
操作结果
设备A在20XX年4月的每周一凌晨1~3点产生的告警在“当前告警”、“告警日志”、“历史告警”中均不可见。在“被屏蔽告警”页面可查看到这些被屏蔽的告警。