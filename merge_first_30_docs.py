#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合并前30个MD文档
"""

import os
import glob

def merge_first_30_docs():
    """合并前30个MD文档"""
    
    # 查找所有MD文件
    directory = "optimized_batch_results"
    if not os.path.exists(directory):
        print(f"❌ 目录不存在: {directory}")
        return
    
    md_files = glob.glob(os.path.join(directory, "*.md"))
    md_files.sort()  # 按文件名排序
    
    if not md_files:
        print(f"⚠️  在目录 {directory} 中未找到MD文件")
        return
    
    print(f"🔍 找到 {len(md_files)} 个MD文件")
    
    # 取前30个文件
    first_30_files = md_files[:30]
    
    print(f"📋 将合并前30个文件:")
    for i, file_path in enumerate(first_30_files, 1):
        filename = os.path.basename(file_path)
        print(f"  {i:2d}. {filename}")
    
    # 合并文档
    merged_content = []
    
    for i, file_path in enumerate(first_30_files):
        filename = os.path.basename(file_path)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            
            # 添加文档内容
            merged_content.append(content)
            
            # 如果不是最后一个文件，添加分隔符
            if i < len(first_30_files) - 1:
                merged_content.append("\n===\n")
            
            print(f"  ✅ 已添加: {filename}")
            
        except Exception as e:
            print(f"  ❌ 读取文件 {filename} 时出错: {e}")
    
    # 写入合并后的文件
    output_file = "merged_first_30_docs.md"
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(merged_content))
        
        print(f"\n🎉 合并完成！")
        print(f"📁 输出文件: {output_file}")
        print(f"📊 合并了 {len(first_30_files)} 个文档")
        
        # 统计信息
        total_content = '\n'.join(merged_content)
        lines_count = len(total_content.split('\n'))
        chars_count = len(total_content)
        
        print(f"📈 统计信息:")
        print(f"   - 总行数: {lines_count:,}")
        print(f"   - 总字符数: {chars_count:,}")
        print(f"   - 文件大小: {chars_count/1024:.1f} KB")
        
    except Exception as e:
        print(f"❌ 写入合并文件时出错: {e}")

if __name__ == "__main__":
    merge_first_30_docs()
