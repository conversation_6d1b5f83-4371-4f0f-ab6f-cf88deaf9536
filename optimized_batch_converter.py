#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版批量告警文档转换器
使用ChapterConverter进行高效转换
"""

import os
import re
import time
from datetime import datetime
from docx import Document
from chapter_converter import ChapterConverter
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('optimized_batch_conversion.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class OptimizedBatchConverter:
    def __init__(self, source_docx, output_dir="optimized_batch_results"):
        self.source_docx = source_docx
        self.output_dir = output_dir

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 创建章节转换器
        self.chapter_converter = ChapterConverter(output_dir)

        # 加载文档（只加载一次）
        self.doc = Document(source_docx)

        logger.info(f"初始化优化版批量转换器")
        logger.info(f"源文档: {source_docx}")
        logger.info(f"输出目录: {output_dir}")

    def find_all_alarm_chapters(self):
        """查找所有告警章节"""
        logger.info("🔍 开始查找所有告警章节...")

        chapters = []

        # 定义支持的章节格式
        patterns = [
            (r'^5\.2\.3\.1\.\d+\s+ALM-\d+', '*******', 'ALM告警'),
            (r'^5\.2\.3\.2\.\d+\s+ALM-\d+', '*******', 'Service OM告警'),
            (r'^5\.2\.4\.1\.\d+\s+ALM-\d+', '*******', '其他告警'),
            (r'^5\.2\.4\.\d+\.\d+\s+ALM-\d+', '5.2.4.x', '其他告警'),
            (r'^5\.\d+\.\d+\.\d+\.\d+\s+ALM-\d+', '5.x.x.x', '其他告警')
        ]

        for i, paragraph in enumerate(self.doc.paragraphs):
            text = paragraph.text.strip()

            # 检查每种模式
            for pattern, section_prefix, section_type in patterns:
                if re.match(pattern, text):
                    # 提取告警代码
                    alarm_code = self.extract_alarm_code(text, section_type)

                    if alarm_code:
                        # 提取告警名称
                        alarm_name = self.extract_alarm_name(text, alarm_code)

                        chapters.append({
                            'title': text,
                            'alarm_code': alarm_code,
                            'alarm_name': alarm_name,
                            'section_type': section_type,
                            'section_prefix': section_prefix,
                            'start_para': i,
                            'style': paragraph.style.name
                        })

                        if len(chapters) % 50 == 0:
                            logger.info(f"  已找到 {len(chapters)} 个告警章节...")
                    break

            # 如果遇到下一个主要章节，停止搜索
            if (text.startswith('5.3') or
                text.startswith('6.') or
                text.startswith('7.')):
                logger.info(f"  遇到非告警章节: {text[:50]}...")
                logger.info(f"  停止搜索，所有告警章节已全部找到")
                break

        # 设置结束位置
        for i, chapter in enumerate(chapters):
            if i + 1 < len(chapters):
                chapter['end_para'] = chapters[i + 1]['start_para']
            else:
                # 最后一个章节的结束位置
                end_para = len(self.doc.paragraphs)
                for j in range(chapter['start_para'] + 1, len(self.doc.paragraphs)):
                    para_text = self.doc.paragraphs[j].text.strip()
                    if (para_text.startswith('5.3') or
                        para_text.startswith('6.') or
                        para_text.startswith('7.')):
                        end_para = j
                        break
                chapter['end_para'] = end_para

        # 统计
        stats = {}
        for chapter in chapters:
            section_type = chapter['section_type']
            stats[section_type] = stats.get(section_type, 0) + 1

        logger.info(f"✅ 找到告警章节总计: {len(chapters)} 个")
        for section_type, count in stats.items():
            logger.info(f"   - {section_type}: {count} 个")

        return chapters

    def extract_alarm_code(self, text, section_type):
        """提取告警代码（保持原文档格式）"""
        # 统一提取ALM代码，不添加自定义前缀
        match = re.search(r'ALM-\d+', text)
        return match.group() if match else None

    def extract_alarm_name(self, text, alarm_code):
        """提取告警名称"""
        if alarm_code and alarm_code.startswith('ALM-'):
            # 提取ALM代码后面的名称
            match = re.search(r'ALM-\d+\s+(.+)', text)
            return match.group(1) if match else "未知告警"
        else:
            # 如果没有ALM代码，尝试提取章节号后面的内容
            match = re.search(r'^5\.\d+\.\d+\.\d+\.\d+\s+(.+)', text)
            return match.group(1) if match else "未知告警"

    def convert_chapter_optimized(self, chapter):
        """使用ChapterConverter转换章节"""
        try:
            start_para = chapter['start_para']
            end_para = chapter['end_para']

            logger.info(f"     提取段落范围: {start_para} - {end_para} (共{end_para - start_para}个段落)")

            # 使用ChapterConverter进行转换
            output_path = self.chapter_converter.convert_chapter(
                doc=self.doc,
                start_para_idx=start_para,
                end_para_idx=end_para,
                chapter_info=chapter
            )

            return output_path

        except Exception as e:
            logger.error(f"转换章节时出错: {e}")
            return None

    def process_chapters_batch(self, chapters, batch_size=10):
        """批量处理章节"""
        logger.info(f"🚀 开始优化批量处理 {len(chapters)} 个告警章节...")

        successful_conversions = []
        failed_conversions = []

        # 分批处理
        for batch_start in range(0, len(chapters), batch_size):
            batch_end = min(batch_start + batch_size, len(chapters))
            batch_chapters = chapters[batch_start:batch_end]

            logger.info(f"\n📦 处理批次 {batch_start//batch_size + 1}: 章节 {batch_start+1}-{batch_end}")

            for i, chapter in enumerate(batch_chapters):
                chapter_idx = batch_start + i + 1
                chapter_start_time = time.time()

                logger.info(f"\n📄 处理第 {chapter_idx}/{len(chapters)} 章: {chapter['alarm_code']}")
                logger.info(f"   类型: {chapter['section_type']}")
                logger.info(f"   标题: {chapter['alarm_name']}")

                try:
                    # 使用优化的转换方法
                    logger.info("   🔄 使用ChapterConverter转换...")
                    md_path = self.convert_chapter_optimized(chapter)

                    if md_path:
                        chapter_time = time.time() - chapter_start_time
                        logger.info(f"   ✅ 转换成功! 耗时: {chapter_time:.2f}秒")
                        logger.info(f"   📁 输出: {md_path}")

                        successful_conversions.append({
                            'chapter': chapter,
                            'md_path': md_path,
                            'time': chapter_time
                        })
                    else:
                        logger.error(f"   ❌ 转换失败")
                        failed_conversions.append({
                            'chapter': chapter,
                            'error': '转换失败'
                        })

                except Exception as e:
                    logger.error(f"   ❌ 处理章节时出错: {e}")
                    failed_conversions.append({
                        'chapter': chapter,
                        'error': str(e)
                    })
                    continue

        return successful_conversions, failed_conversions

    def process_all_chapters(self):
        """处理所有章节"""
        start_time = time.time()

        # 查找章节
        chapters = self.find_all_alarm_chapters()

        if not chapters:
            logger.error("❌ 未找到告警章节")
            return []

        # 批量处理
        successful_conversions, failed_conversions = self.process_chapters_batch(chapters)

        # 输出总结
        total_time = time.time() - start_time
        self.print_summary(successful_conversions, failed_conversions, total_time)

        return successful_conversions

    def print_summary(self, successful, failed, total_time):
        """打印处理总结"""
        logger.info("\n" + "="*80)
        logger.info("📊 优化版批量转换完成总结")
        logger.info("="*80)

        logger.info(f"⏱️  总耗时: {total_time:.2f}秒 ({total_time/60:.1f}分钟)")
        logger.info(f"✅ 成功转换: {len(successful)} 个章节")
        logger.info(f"❌ 转换失败: {len(failed)} 个章节")

        if len(successful) + len(failed) > 0:
            logger.info(f"📈 成功率: {len(successful)/(len(successful)+len(failed))*100:.1f}%")

        # 按类型统计
        if successful:
            type_stats = {}
            for item in successful:
                section_type = item['chapter']['section_type']
                type_stats[section_type] = type_stats.get(section_type, 0) + 1

            logger.info(f"\n📁 成功转换的章节（按类型）:")
            for section_type, count in type_stats.items():
                logger.info(f"   - {section_type}: {count} 个")

            # 计算平均转换时间
            avg_time = sum(item['time'] for item in successful) / len(successful)
            logger.info(f"\n⚡ 平均转换时间: {avg_time:.2f}秒/章节")

        logger.info(f"\n📂 输出目录: {self.output_dir}")
        logger.info("="*80)


def main():
    """主函数"""
    source_file = "华为云Stack告警处理参考.docx"
    output_dir = "optimized_batch_results"

    if not os.path.exists(source_file):
        logger.error(f"❌ 错误：找不到源文件 {source_file}")
        return

    # 创建转换器
    converter = OptimizedBatchConverter(source_file, output_dir)

    # 执行批量转换
    try:
        successful_conversions = converter.process_all_chapters()

        if successful_conversions:
            logger.info(f"\n🎉 优化版批量转换完成!")
            logger.info(f"📄 成功转换 {len(successful_conversions)} 个告警章节")
            logger.info(f"📁 输出目录: {output_dir}")
            logger.info(f"⚡ 使用ChapterConverter优化转换性能")
        else:
            logger.error("❌ 没有成功转换任何章节")

    except Exception as e:
        logger.error(f"❌ 优化版批量转换失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
