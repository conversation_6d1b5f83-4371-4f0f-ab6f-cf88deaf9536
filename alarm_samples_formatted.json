[{"主题": "[提示信息]\"弹性负载均衡\"", "告警ID": "", "告警级别": "提示", "告警源": "ELB", "来源系统": "ManageOne10.200.219.204", "定位信息": "区域=SH_CSVW，云服务=ELB，节点类型=management", "附加信息": "云服务=ELB，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.10 弹性负载均衡.md", "section_number": "5.2.10", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[次要告警]\"Database发生数据库连接异常\"", "告警ID": "1223005", "告警级别": "次要", "告警源": "Database", "来源系统": "ServiceOM10.200.155.31", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.124.239)，对端地址=(other_database=10.200.59.71)", "可能原因": "未知", "metadata": {"filename": "5.2.10.1 ALM-1223005 数据库连接异常.md", "section_number": "5.2.10.1", "alarm_type": "ALM", "original_alarm_code": "ALM-1223005"}}, {"主题": "[提示告警]\"ETCD发生ETCD集群健康检查告警\"", "告警ID": "1223006", "告警级别": "提示", "告警源": "ETCD", "来源系统": "ServiceOM10.200.243.183", "定位信息": "区域=SH_CSVW，云服务=ETCD，节点类型=mgt", "附加信息": "云服务=ETCD，服务=mgt，本端地址=(10.200.34.175)，对端地址=(other_etcd=10.200.4.154)", "可能原因": "未知", "metadata": {"filename": "5.2.10.2 ALM-1223006 ETCD集群健康检查告警.md", "section_number": "5.2.10.2", "alarm_type": "ALM", "original_alarm_code": "ALM-1223006"}}, {"主题": "[次要告警]\"Network发生集群中存在主机连接异常\"", "告警ID": "1223013", "告警级别": "次要", "告警源": "Network", "来源系统": "ServiceOM10.200.74.48", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.98.199)，对端地址=(other_network=10.200.241.165)", "可能原因": "未知", "metadata": {"filename": "5.2.10.3 ALM-1223013 集群中存在主机连接异常.md", "section_number": "5.2.10.3", "alarm_type": "ALM", "original_alarm_code": "ALM-1223013"}}, {"主题": "[严重告警]\"Process发生僵尸进程告警\"", "告警ID": "1223014", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.192.149", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.226.20)，对端地址=(other_process=10.200.3.169)", "可能原因": "未知", "metadata": {"filename": "5.2.10.4 ALM-1223014 僵尸进程告警.md", "section_number": "5.2.10.4", "alarm_type": "ALM", "original_alarm_code": "ALM-1223014"}}, {"主题": "[严重告警]\"ELB发生ELB管理节点脑裂告警\"", "告警ID": "1223016", "告警级别": "严重", "告警源": "ELB", "来源系统": "ServiceOM10.200.95.215", "定位信息": "区域=SH_CSVW，云服务=ELB，节点类型=mgt", "附加信息": "云服务=ELB，服务=mgt，本端地址=(10.200.155.64)，对端地址=(other_elb=10.200.15.126)", "可能原因": "未知", "metadata": {"filename": "5.2.10.5 ALM-1223016 ELB管理节点脑裂告警.md", "section_number": "5.2.10.5", "alarm_type": "ALM", "original_alarm_code": "ALM-1223016"}}, {"主题": "[提示信息]\"灾备服务\"", "告警ID": "", "告警级别": "提示", "告警源": "Process", "来源系统": "ManageOne10.200.213.184", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=management", "附加信息": "云服务=Process，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.11 灾备服务.md", "section_number": "5.2.11", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示信息]\"eBackup\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.197.241", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "******** eBackup.md", "section_number": "********", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[次要告警]\"License发生License文件无效\"", "告警ID": "0x1000F40000", "告警级别": "次要", "告警源": "License", "来源系统": "eBackup10.200.152.222", "定位信息": "区域=SH_CSVW，云服务=License，节点类型=backup", "附加信息": "云服务=License，服务=backup，节点地址=(10.200.223.53)，错误代码=0x1000F40000", "可能原因": "未知", "metadata": {"filename": "********.1 0x1000F40000 License文件无效.md", "section_number": "********.1", "alarm_type": "HEX", "original_alarm_code": "0x1000F40000"}}, {"主题": "[严重告警]\"License发生License授权容量即将耗尽\"", "告警ID": "0x201000F40008", "告警级别": "严重", "告警源": "License", "来源系统": "eBackup10.200.117.65", "定位信息": "区域=SH_CSVW，云服务=License，节点类型=backup", "附加信息": "云服务=License，服务=backup，节点地址=(10.200.52.131)，错误代码=0x201000F40008", "可能原因": "未知", "metadata": {"filename": "********.10 0x201000F40008 License授权容量即将耗尽.md", "section_number": "********.10", "alarm_type": "HEX", "original_alarm_code": "0x201000F40008"}}, {"主题": "[严重告警]\"License发生存在License不支持的特性\"", "告警ID": "0x201000F4000C", "告警级别": "严重", "告警源": "License", "来源系统": "eBackup10.200.150.153", "定位信息": "区域=SH_CSVW，云服务=License，节点类型=backup", "附加信息": "云服务=License，服务=backup，节点地址=(10.200.140.102)，错误代码=0x201000F4000C", "可能原因": "未知", "metadata": {"filename": "********.11 0x201000F4000C 存在License不支持的特性.md", "section_number": "********.11", "alarm_type": "HEX", "original_alarm_code": "0x201000F4000C"}}, {"主题": "[严重告警]\"License发生存在License不支持的特性\"", "告警ID": "0x201000F40013", "告警级别": "严重", "告警源": "License", "来源系统": "eBackup10.200.63.76", "定位信息": "区域=SH_CSVW，云服务=License，节点类型=backup", "附加信息": "云服务=License，服务=backup，节点地址=(10.200.68.83)，错误代码=0x201000F40013", "可能原因": "未知", "metadata": {"filename": "********.12 0x201000F40013 存在License不支持的特性.md", "section_number": "********.12", "alarm_type": "HEX", "original_alarm_code": "0x201000F40013"}}, {"主题": "[严重告警]\"License发生存在License不支持的特性\"", "告警ID": "0x201000F40014", "告警级别": "严重", "告警源": "License", "来源系统": "eBackup10.200.117.179", "定位信息": "区域=SH_CSVW，云服务=License，节点类型=backup", "附加信息": "云服务=License，服务=backup，节点地址=(10.200.3.33)，错误代码=0x201000F40014", "可能原因": "未知", "metadata": {"filename": "********.13 0x201000F40014 存在License不支持的特性.md", "section_number": "********.13", "alarm_type": "HEX", "original_alarm_code": "0x201000F40014"}}, {"主题": "[严重告警]\"License发生存在License不支持的特性\"", "告警ID": "0x201000F40016", "告警级别": "严重", "告警源": "License", "来源系统": "eBackup10.200.8.217", "定位信息": "区域=SH_CSVW，云服务=License，节点类型=backup", "附加信息": "云服务=License，服务=backup，节点地址=(10.200.65.113)，错误代码=0x201000F40016", "可能原因": "未知", "metadata": {"filename": "********.14 0x201000F40016 存在License不支持的特性.md", "section_number": "********.14", "alarm_type": "HEX", "original_alarm_code": "0x201000F40016"}}, {"主题": "[重要告警]\"License发生存在License不支持的特性\"", "告警ID": "0x201000F40017", "告警级别": "重要", "告警源": "License", "来源系统": "eBackup10.200.111.174", "定位信息": "区域=SH_CSVW，云服务=License，节点类型=backup", "附加信息": "云服务=License，服务=backup，节点地址=(10.200.203.104)，错误代码=0x201000F40017", "可能原因": "未知", "metadata": {"filename": "********.15 0x201000F40017 存在License不支持的特性.md", "section_number": "********.15", "alarm_type": "HEX", "original_alarm_code": "0x201000F40017"}}, {"主题": "[次要告警]\"License发生存在License不支持的特性\"", "告警ID": "0x201000F40018", "告警级别": "次要", "告警源": "License", "来源系统": "eBackup10.200.198.192", "定位信息": "区域=SH_CSVW，云服务=License，节点类型=backup", "附加信息": "云服务=License，服务=backup，节点地址=(10.200.9.117)，错误代码=0x201000F40018", "可能原因": "未知", "metadata": {"filename": "********.16 0x201000F40018 存在License不支持的特性.md", "section_number": "********.16", "alarm_type": "HEX", "original_alarm_code": "0x201000F40018"}}, {"主题": "[次要告警]\"eBackup发生事件转储目录所用空间已超出阈值\"", "告警ID": "0x1000310000", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.17.36", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.53.245)，错误代码=0x1000310000", "可能原因": "未知", "metadata": {"filename": "********.17 0x1000310000 事件转储目录所用空间已超出阈值.md", "section_number": "********.17", "alarm_type": "HEX", "original_alarm_code": "0x1000310000"}}, {"主题": "[重要告警]\"Certificate发生证书校验失败\"", "告警ID": "0x20100031000A", "告警级别": "重要", "告警源": "Certificate", "来源系统": "eBackup10.200.187.247", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.124.146)，错误代码=0x20100031000A", "可能原因": "未知", "metadata": {"filename": "********.18 0x20100031000A 证书校验失败.md", "section_number": "********.18", "alarm_type": "HEX", "original_alarm_code": "0x20100031000A"}}, {"主题": "[严重告警]\"Certificate发生证书校验失败\"", "告警ID": "0x20100031000C", "告警级别": "严重", "告警源": "Certificate", "来源系统": "eBackup10.200.41.78", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.175.191)，错误代码=0x20100031000C", "可能原因": "未知", "metadata": {"filename": "********.19 0x20100031000C 证书校验失败.md", "section_number": "********.19", "alarm_type": "HEX", "original_alarm_code": "0x20100031000C"}}, {"主题": "[严重告警]\"License发生License未配置\"", "告警ID": "0x1000F40001", "告警级别": "严重", "告警源": "License", "来源系统": "eBackup10.200.121.107", "定位信息": "区域=SH_CSVW，云服务=License，节点类型=backup", "附加信息": "云服务=License，服务=backup，节点地址=(10.200.177.71)，错误代码=0x1000F40001", "可能原因": "未知", "metadata": {"filename": "********.2 0x1000F40001 License未配置.md", "section_number": "********.2", "alarm_type": "HEX", "original_alarm_code": "0x1000F40001"}}, {"主题": "[重要告警]\"eBackup发生访问告警服务器失败\"", "告警ID": "0x201000310010", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.60.105", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.236.140)，错误代码=0x201000310010", "可能原因": "未知", "metadata": {"filename": "********.20 0x201000310010 访问告警服务器失败.md", "section_number": "********.20", "alarm_type": "HEX", "original_alarm_code": "0x201000310010"}}, {"主题": "[重要告警]\"Database发生数据库连接失败\"", "告警ID": "0x201000310015", "告警级别": "重要", "告警源": "Database", "来源系统": "eBackup10.200.119.91", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=backup", "附加信息": "云服务=Database，服务=backup，节点地址=(10.200.63.176)，错误代码=0x201000310015", "可能原因": "未知", "metadata": {"filename": "********.21 0x201000310015 数据库连接失败.md", "section_number": "********.21", "alarm_type": "HEX", "original_alarm_code": "0x201000310015"}}, {"主题": "[严重告警]\"Microservice发生微服务注册失败\"", "告警ID": "0x210000000101", "告警级别": "严重", "告警源": "Microservice", "来源系统": "eBackup10.200.188.101", "定位信息": "区域=SH_CSVW，云服务=Microservice，节点类型=backup", "附加信息": "云服务=Microservice，服务=backup，节点地址=(10.200.245.99)，错误代码=0x210000000101", "可能原因": "未知", "metadata": {"filename": "********.22 0x210000000101 微服务注册失败.md", "section_number": "********.22", "alarm_type": "HEX", "original_alarm_code": "0x210000000101"}}, {"主题": "[次要告警]\"Microservice发生微服务已停止\"", "告警ID": "0x210000000100", "告警级别": "次要", "告警源": "Microservice", "来源系统": "eBackup10.200.117.76", "定位信息": "区域=SH_CSVW，云服务=Microservice，节点类型=backup", "附加信息": "云服务=Microservice，服务=backup，节点地址=(10.200.128.21)，错误代码=0x210000000100", "可能原因": "未知", "metadata": {"filename": "********.23 0x210000000100 微服务已停止.md", "section_number": "********.23", "alarm_type": "HEX", "original_alarm_code": "0x210000000100"}}, {"主题": "[次要告警]\"Certificate发生证书已经过期\"", "告警ID": "0x6000840001", "告警级别": "次要", "告警源": "Certificate", "来源系统": "eBackup10.200.104.158", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.37.114)，错误代码=0x6000840001", "可能原因": "未知", "metadata": {"filename": "********.24 0x6000840001 证书已经过期.md", "section_number": "********.24", "alarm_type": "HEX", "original_alarm_code": "0x6000840001"}}, {"主题": "[重要告警]\"Certificate发生证书即将过期\"", "告警ID": "0x6000840002", "告警级别": "重要", "告警源": "Certificate", "来源系统": "eBackup10.200.12.96", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.255.153)，错误代码=0x6000840002", "可能原因": "未知", "metadata": {"filename": "********.25 0x6000840002 证书即将过期.md", "section_number": "********.25", "alarm_type": "HEX", "original_alarm_code": "0x6000840002"}}, {"主题": "[重要告警]\"Certificate发生证书校验失败\"", "告警ID": "0x210000000200", "告警级别": "重要", "告警源": "Certificate", "来源系统": "eBackup10.200.160.104", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.184.174)，错误代码=0x210000000200", "可能原因": "未知", "metadata": {"filename": "********.26 0x210000000200 证书校验失败.md", "section_number": "********.26", "alarm_type": "HEX", "original_alarm_code": "0x210000000200"}}, {"主题": "[次要告警]\"Backup发生清理备份记录失败\"", "告警ID": "0x105800860001", "告警级别": "次要", "告警源": "Backup", "来源系统": "eBackup10.200.9.63", "定位信息": "区域=SH_CSVW，云服务=Backup，节点类型=backup", "附加信息": "云服务=Backup，服务=backup，节点地址=(10.200.192.154)，错误代码=0x105800860001", "可能原因": "未知", "metadata": {"filename": "********.27 0x105800860001 清理备份记录失败.md", "section_number": "********.27", "alarm_type": "HEX", "original_alarm_code": "0x105800860001"}}, {"主题": "[次要告警]\"eBackup发生清理leftover删除快照失败\"", "告警ID": "0x21000000090E", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.201.191", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.206.107)，错误代码=0x21000000090E", "可能原因": "未知", "metadata": {"filename": "********.28 0x21000000090E 清理leftover删除快照失败.md", "section_number": "********.28", "alarm_type": "HEX", "original_alarm_code": "0x21000000090E"}}, {"主题": "[重要告警]\"eBackup发生组件连接异常\"", "告警ID": "0x21000000090F", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.194.145", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.227.224)，错误代码=0x21000000090F", "可能原因": "未知", "metadata": {"filename": "********.29 0x21000000090F 组件连接异常.md", "section_number": "********.29", "alarm_type": "HEX", "original_alarm_code": "0x21000000090F"}}, {"主题": "[重要告警]\"License发生License进入宽限期\"", "告警ID": "0x1000F40002", "告警级别": "重要", "告警源": "License", "来源系统": "eBackup10.200.68.252", "定位信息": "区域=SH_CSVW，云服务=License，节点类型=backup", "附加信息": "云服务=License，服务=backup，节点地址=(10.200.28.78)，错误代码=0x1000F40002", "可能原因": "未知", "metadata": {"filename": "********.3 0x1000F40002 License进入宽限期.md", "section_number": "********.3", "alarm_type": "HEX", "original_alarm_code": "0x1000F40002"}}, {"主题": "[严重告警]\"eBackup发生监控进程启动失败\"", "告警ID": "0x210000000901", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.198.145", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.212.57)，错误代码=0x210000000901", "可能原因": "未知", "metadata": {"filename": "********.30 0x210000000901 监控进程启动失败.md", "section_number": "********.30", "alarm_type": "HEX", "original_alarm_code": "0x210000000901"}}, {"主题": "[次要告警]\"Certificate发生证书校验失败\"", "告警ID": "0x2010E01D0005", "告警级别": "次要", "告警源": "Certificate", "来源系统": "eBackup10.200.236.117", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.24.116)，错误代码=0x2010E01D0005", "可能原因": "未知", "metadata": {"filename": "********.31 0x2010E01D0005 证书校验失败.md", "section_number": "********.31", "alarm_type": "HEX", "original_alarm_code": "0x2010E01D0005"}}, {"主题": "[严重告警]\"Backup发生执行备份时CBT机制未生效\"", "告警ID": "0x1010E01A0018", "告警级别": "严重", "告警源": "Backup", "来源系统": "eBackup10.200.226.92", "定位信息": "区域=SH_CSVW，云服务=Backup，节点类型=backup", "附加信息": "云服务=Backup，服务=backup，节点地址=(10.200.157.89)，错误代码=0x1010E01A0018", "可能原因": "未知", "metadata": {"filename": "********.32 0x1010E01A0018 执行备份时CBT机制未生效.md", "section_number": "********.32", "alarm_type": "HEX", "original_alarm_code": "0x1010E01A0018"}}, {"主题": "[严重告警]\"eBackup发生系统配置数据所占空间已超出最大阈值\"", "告警ID": "0x101000C90003", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.74.184", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.101.6)，错误代码=0x101000C90003", "可能原因": "未知", "metadata": {"filename": "********.33 0x101000C90003 系统配置数据所占空间已超出最大阈值.md", "section_number": "********.33", "alarm_type": "HEX", "original_alarm_code": "0x101000C90003"}}, {"主题": "[重要告警]\"eBackup发生系统配置数据所占空间已超出阈值\"", "告警ID": "0x101000C90004", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.57.212", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.209.154)，错误代码=0x101000C90004", "可能原因": "未知", "metadata": {"filename": "********.34 0x101000C90004 系统配置数据所占空间已超出阈值.md", "section_number": "********.34", "alarm_type": "HEX", "original_alarm_code": "0x101000C90004"}}, {"主题": "[次要告警]\"Database发生访问系统数据库备份共享存储失败\"", "告警ID": "0x1000C90002", "告警级别": "次要", "告警源": "Database", "来源系统": "eBackup10.200.236.26", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=backup", "附加信息": "云服务=Database，服务=backup，节点地址=(10.200.243.117)，错误代码=0x1000C90002", "可能原因": "未知", "metadata": {"filename": "********.35 0x1000C90002 访问系统数据库备份共享存储失败.md", "section_number": "********.35", "alarm_type": "HEX", "original_alarm_code": "0x1000C90002"}}, {"主题": "[重要告警]\"Certificate发生证书校验失败\"", "告警ID": "0x1000C90035", "告警级别": "重要", "告警源": "Certificate", "来源系统": "eBackup10.200.245.228", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.59.155)，错误代码=0x1000C90035", "可能原因": "未知", "metadata": {"filename": "********.36 0x1000C90035 证书校验失败.md", "section_number": "********.36", "alarm_type": "HEX", "original_alarm_code": "0x1000C90035"}}, {"主题": "[次要告警]\"Database发生系统数据库备份共享存储空间不足\"", "告警ID": "0x1000C90003", "告警级别": "次要", "告警源": "Database", "来源系统": "eBackup10.200.198.24", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=backup", "附加信息": "云服务=Database，服务=backup，节点地址=(10.200.222.96)，错误代码=0x1000C90003", "可能原因": "未知", "metadata": {"filename": "********.37 0x1000C90003 系统数据库备份共享存储空间不足.md", "section_number": "********.37", "alarm_type": "HEX", "original_alarm_code": "0x1000C90003"}}, {"主题": "[次要告警]\"Backup发生未配置备份服务器\"", "告警ID": "0x1000C90005", "告警级别": "次要", "告警源": "Backup", "来源系统": "eBackup10.200.68.34", "定位信息": "区域=SH_CSVW，云服务=Backup，节点类型=backup", "附加信息": "云服务=Backup，服务=backup，节点地址=(10.200.214.75)，错误代码=0x1000C90005", "可能原因": "未知", "metadata": {"filename": "********.38 0x1000C90005 未配置备份服务器.md", "section_number": "********.38", "alarm_type": "HEX", "original_alarm_code": "0x1000C90005"}}, {"主题": "[次要告警]\"Certificate发生证书校验失败\"", "告警ID": "0x1000C90006", "告警级别": "次要", "告警源": "Certificate", "来源系统": "eBackup10.200.92.45", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.96.53)，错误代码=0x1000C90006", "可能原因": "未知", "metadata": {"filename": "********.39 0x1000C90006 证书校验失败.md", "section_number": "********.39", "alarm_type": "HEX", "original_alarm_code": "0x1000C90006"}}, {"主题": "[严重告警]\"License发生License已经过期\"", "告警ID": "0x1000F40003", "告警级别": "严重", "告警源": "License", "来源系统": "eBackup10.200.248.211", "定位信息": "区域=SH_CSVW，云服务=License，节点类型=backup", "附加信息": "云服务=License，服务=backup，节点地址=(10.200.219.175)，错误代码=0x1000F40003", "可能原因": "未知", "metadata": {"filename": "********.4 0x1000F40003 License已经过期.md", "section_number": "********.4", "alarm_type": "HEX", "original_alarm_code": "0x1000F40003"}}, {"主题": "[次要告警]\"eBackup发生FTP服务器空间不足\"", "告警ID": "0x1000C90032", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.125.228", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.39.50)，错误代码=0x1000C90032", "可能原因": "未知", "metadata": {"filename": "********.40 0x1000C90032 FTP服务器空间不足.md", "section_number": "********.40", "alarm_type": "HEX", "original_alarm_code": "0x1000C90032"}}, {"主题": "[次要告警]\"eBackup发生登录FTP服务器被拒绝\"", "告警ID": "0x1000C90033", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.55.181", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.203.12)，错误代码=0x1000C90033", "可能原因": "未知", "metadata": {"filename": "********.41 0x1000C90033 登录FTP服务器被拒绝.md", "section_number": "********.41", "alarm_type": "HEX", "original_alarm_code": "0x1000C90033"}}, {"主题": "[严重告警]\"eBackup发生上传管理数据到FTP服务器失败\"", "告警ID": "0x1000C90034", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.150.169", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.205.128)，错误代码=0x1000C90034", "可能原因": "未知", "metadata": {"filename": "********.42 0x1000C90034 上传管理数据到FTP服务器失败.md", "section_number": "********.42", "alarm_type": "HEX", "original_alarm_code": "0x1000C90034"}}, {"主题": "[严重告警]\"Database发生当前挂载的系统数据库备份共享存储类型与预置类型不匹\"", "告警ID": "0x1000C90004", "告警级别": "严重", "告警源": "Database", "来源系统": "eBackup10.200.226.136", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=backup", "附加信息": "云服务=Database，服务=backup，节点地址=(10.200.94.206)，错误代码=0x1000C90004", "可能原因": "未知", "metadata": {"filename": "********.43 0x1000C90004 当前挂载的系统数据库备份共享存储类型与预置类型不匹.md", "section_number": "********.43", "alarm_type": "HEX", "original_alarm_code": "0x1000C90004"}}, {"主题": "[严重告警]\"Backup发生eBackup没有存储单元的写权限\"", "告警ID": "0x2010E00E0007", "告警级别": "严重", "告警源": "Backup", "来源系统": "eBackup10.200.229.195", "定位信息": "区域=SH_CSVW，云服务=Backup，节点类型=backup", "附加信息": "云服务=Backup，服务=backup，节点地址=(10.200.69.158)，错误代码=0x2010E00E0007", "可能原因": "未知", "metadata": {"filename": "********.44 0x2010E00E0007 eBackup没有存储单元的写权限.md", "section_number": "********.44", "alarm_type": "HEX", "original_alarm_code": "0x2010E00E0007"}}, {"主题": "[重要告警]\"Certificate发生证书校验失败\"", "告警ID": "0x2010E00E000A", "告警级别": "重要", "告警源": "Certificate", "来源系统": "eBackup10.200.185.117", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.76.117)，错误代码=0x2010E00E000A", "可能原因": "未知", "metadata": {"filename": "********.45 0x2010E00E000A 证书校验失败.md", "section_number": "********.45", "alarm_type": "HEX", "original_alarm_code": "0x2010E00E000A"}}, {"主题": "[重要告警]\"eBackup发生存储单元没有可用容量\"", "告警ID": "0x2010E00E0006", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.46.209", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.146.141)，错误代码=0x2010E00E0006", "可能原因": "未知", "metadata": {"filename": "********.46 0x2010E00E0006 存储单元没有可用容量.md", "section_number": "********.46", "alarm_type": "HEX", "original_alarm_code": "0x2010E00E0006"}}, {"主题": "[次要告警]\"Certificate发生证书校验失败\"", "告警ID": "0x2010E01D0006", "告警级别": "次要", "告警源": "Certificate", "来源系统": "eBackup10.200.13.234", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.121.31)，错误代码=0x2010E01D0006", "可能原因": "未知", "metadata": {"filename": "********.47 0x2010E01D0006 证书校验失败.md", "section_number": "********.47", "alarm_type": "HEX", "original_alarm_code": "0x2010E01D0006"}}, {"主题": "[重要告警]\"Backup发生eBackup服务器之间失去连接\"", "告警ID": "0x1000C90000", "告警级别": "重要", "告警源": "Backup", "来源系统": "eBackup10.200.236.27", "定位信息": "区域=SH_CSVW，云服务=Backup，节点类型=backup", "附加信息": "云服务=Backup，服务=backup，节点地址=(10.200.12.51)，错误代码=0x1000C90000", "可能原因": "未知", "metadata": {"filename": "********.48 0x1000C90000 eBackup服务器之间失去连接.md", "section_number": "********.48", "alarm_type": "HEX", "original_alarm_code": "0x1000C90000"}}, {"主题": "[重要告警]\"eBackup发生存储库容量不足\"", "告警ID": "0x10E00C0000", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.78.173", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.169.67)，错误代码=0x10E00C0000", "可能原因": "未知", "metadata": {"filename": "********.49 0x10E00C0000 存储库容量不足.md", "section_number": "********.49", "alarm_type": "HEX", "original_alarm_code": "0x10E00C0000"}}, {"主题": "[重要告警]\"License发生License ESN不匹配\"", "告警ID": "0x101000F40000", "告警级别": "重要", "告警源": "License", "来源系统": "eBackup10.200.190.174", "定位信息": "区域=SH_CSVW，云服务=License，节点类型=backup", "附加信息": "云服务=License，服务=backup，节点地址=(10.200.147.221)，错误代码=0x101000F40000", "可能原因": "未知", "metadata": {"filename": "********.5 0x101000F40000 License ESN不匹配.md", "section_number": "********.5", "alarm_type": "HEX", "original_alarm_code": "0x101000F40000"}}, {"主题": "[严重告警]\"eBackup发生添加Workflow或Proxy失败\"", "告警ID": "0x10E01C0000", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.225.156", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.27.141)，错误代码=0x10E01C0000", "可能原因": "未知", "metadata": {"filename": "********.50 0x10E01C0000 添加Workflow或Proxy失败.md", "section_number": "********.50", "alarm_type": "HEX", "original_alarm_code": "0x10E01C0000"}}, {"主题": "[次要告警]\"Certificate发生HA的证书已过期\"", "告警ID": "0x6000760001", "告警级别": "次要", "告警源": "Certificate", "来源系统": "eBackup10.200.239.127", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.58.238)，错误代码=0x6000760001", "可能原因": "未知", "metadata": {"filename": "********.51 0x6000760001 HA的证书已过期.md", "section_number": "********.51", "alarm_type": "HEX", "original_alarm_code": "0x6000760001"}}, {"主题": "[次要告警]\"eBackup发生服务进程异常\"", "告警ID": "0x10E01C0027", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.238.44", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.222.2)，错误代码=0x10E01C0027", "可能原因": "未知", "metadata": {"filename": "********.52 0x10E01C0027 服务进程异常.md", "section_number": "********.52", "alarm_type": "HEX", "original_alarm_code": "0x10E01C0027"}}, {"主题": "[严重告警]\"eBackup发生连接存储单元失败\"", "告警ID": "0x10E00E0000", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.242.180", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(*************)，错误代码=0x10E00E0000", "可能原因": "未知", "metadata": {"filename": "********.53 0x10E00E0000 连接存储单元失败.md", "section_number": "********.53", "alarm_type": "HEX", "original_alarm_code": "0x10E00E0000"}}, {"主题": "[次要告警]\"eBackup发生浮动IP连接异常\"", "告警ID": "0x10E01C0029", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.100.19", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(**************)，错误代码=0x10E01C0029", "可能原因": "未知", "metadata": {"filename": "********.54 0x10E01C0029 浮动IP连接异常.md", "section_number": "********.54", "alarm_type": "HEX", "original_alarm_code": "0x10E01C0029"}}, {"主题": "[重要告警]\"eBackup发生Workflow（Proxy）和Manager（S\"", "告警ID": "0x10E01C0001", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.246.116", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(************)，错误代码=0x10E01C0001", "可能原因": "未知", "metadata": {"filename": "********.55 0x10E01C0001 Workflow（Proxy）和Manager（S.md", "section_number": "********.55", "alarm_type": "HEX", "original_alarm_code": "0x10E01C0001"}}, {"主题": "[严重告警]\"eBackup发生主备参数不一致导致HA功能异常\"", "告警ID": "0x10E01C0002", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.240.184", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.97.1)，错误代码=0x10E01C0002", "可能原因": "未知", "metadata": {"filename": "********.56 0x10E01C0002 主备参数不一致导致HA功能异常.md", "section_number": "********.56", "alarm_type": "HEX", "original_alarm_code": "0x10E01C0002"}}, {"主题": "[次要告警]\"eBackup发生恢复主备倒换功能失败\"", "告警ID": "0x10E01C0003", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.223.1", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.108.120)，错误代码=0x10E01C0003", "可能原因": "未知", "metadata": {"filename": "********.57 0x10E01C0003 恢复主备倒换功能失败.md", "section_number": "********.57", "alarm_type": "HEX", "original_alarm_code": "0x10E01C0003"}}, {"主题": "[严重告警]\"eBackup发生HA主备节点心跳中断\"", "告警ID": "0x10E01C0004", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.102.1", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.75.172)，错误代码=0x10E01C0004", "可能原因": "未知", "metadata": {"filename": "********.58 0x10E01C0004 HA主备节点心跳中断.md", "section_number": "********.58", "alarm_type": "HEX", "original_alarm_code": "0x10E01C0004"}}, {"主题": "[重要告警]\"eBackup发生HA主节点向备节点同步文件失败\"", "告警ID": "0x10E01C0005", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.80.247", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.162.25)，错误代码=0x10E01C0005", "可能原因": "未知", "metadata": {"filename": "********.59 0x10E01C0005 HA主节点向备节点同步文件失败.md", "section_number": "********.59", "alarm_type": "HEX", "original_alarm_code": "0x10E01C0005"}}, {"主题": "[次要告警]\"License发生License文件版本不匹配\"", "告警ID": "0x101000F40001", "告警级别": "次要", "告警源": "License", "来源系统": "eBackup10.200.69.126", "定位信息": "区域=SH_CSVW，云服务=License，节点类型=backup", "附加信息": "云服务=License，服务=backup，节点地址=(10.200.65.27)，错误代码=0x101000F40001", "可能原因": "未知", "metadata": {"filename": "********.6 0x101000F40001 License文件版本不匹配.md", "section_number": "********.6", "alarm_type": "HEX", "original_alarm_code": "0x101000F40001"}}, {"主题": "[重要告警]\"eBackup发生HA仲裁网关不可达\"", "告警ID": "0x10E01C0009", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.239.138", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.184.44)，错误代码=0x10E01C0009", "可能原因": "未知", "metadata": {"filename": "********.60 0x10E01C0009 HA仲裁网关不可达.md", "section_number": "********.60", "alarm_type": "HEX", "original_alarm_code": "0x10E01C0009"}}, {"主题": "[严重告警]\"Database发生数据库升主失败\"", "告警ID": "0x10E01C000B", "告警级别": "严重", "告警源": "Database", "来源系统": "eBackup10.200.171.100", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=backup", "附加信息": "云服务=Database，服务=backup，节点地址=(10.200.210.223)，错误代码=0x10E01C000B", "可能原因": "未知", "metadata": {"filename": "********.61 0x10E01C000B 数据库升主失败.md", "section_number": "********.61", "alarm_type": "HEX", "original_alarm_code": "0x10E01C000B"}}, {"主题": "[严重告警]\"eBackup发生AdminNode服务异常\"", "告警ID": "0x10E01C000E", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.204.22", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(************)，错误代码=0x10E01C000E", "可能原因": "未知", "metadata": {"filename": "********.62 0x10E01C000E AdminNode服务异常.md", "section_number": "********.62", "alarm_type": "HEX", "original_alarm_code": "0x10E01C000E"}}, {"主题": "[严重告警]\"eBackup发生浮动IP服务异常\"", "告警ID": "0x10E01C000F", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.255.207", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(**************)，错误代码=0x10E01C000F", "可能原因": "未知", "metadata": {"filename": "********.63 0x10E01C000F 浮动IP服务异常.md", "section_number": "********.63", "alarm_type": "HEX", "original_alarm_code": "0x10E01C000F"}}, {"主题": "[严重告警]\"Database发生GaussDB服务异常\"", "告警ID": "0x10E01C0010", "告警级别": "严重", "告警源": "Database", "来源系统": "eBackup10.200.232.20", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=backup", "附加信息": "云服务=Database，服务=backup，节点地址=(**************)，错误代码=0x10E01C0010", "可能原因": "未知", "metadata": {"filename": "********.64 0x10E01C0010 GaussDB服务异常.md", "section_number": "********.64", "alarm_type": "HEX", "original_alarm_code": "0x10E01C0010"}}, {"主题": "[次要告警]\"eBackup发生ibase服务异常\"", "告警ID": "0x10E01C0011", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.24.72", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.92.167)，错误代码=0x10E01C0011", "可能原因": "未知", "metadata": {"filename": "********.65 0x10E01C0011 ibase服务异常.md", "section_number": "********.65", "alarm_type": "HEX", "original_alarm_code": "0x10E01C0011"}}, {"主题": "[重要告警]\"Certificate发生证书校验失败\"", "告警ID": "0x10E01C0028", "告警级别": "重要", "告警源": "Certificate", "来源系统": "eBackup10.200.203.14", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.147.108)，错误代码=0x10E01C0028", "可能原因": "未知", "metadata": {"filename": "********.66 0x10E01C0028 证书校验失败.md", "section_number": "********.66", "alarm_type": "HEX", "original_alarm_code": "0x10E01C0028"}}, {"主题": "[次要告警]\"eBackup发生访问存储单元失败\"", "告警ID": "0x10E00E0001", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.131.50", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.203.149)，错误代码=0x10E00E0001", "可能原因": "未知", "metadata": {"filename": "********.67 0x10E00E0001 访问存储单元失败.md", "section_number": "********.67", "alarm_type": "HEX", "original_alarm_code": "0x10E00E0001"}}, {"主题": "[重要告警]\"eBackup发生扫描受保护环境失败\"", "告警ID": "0x10E0140001", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.115.237", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.196.93)，错误代码=0x10E0140001", "可能原因": "未知", "metadata": {"filename": "********.68 0x10E0140001 扫描受保护环境失败.md", "section_number": "********.68", "alarm_type": "HEX", "original_alarm_code": "0x10E0140001"}}, {"主题": "[次要告警]\"eBackup发生连接受保护环境失败\"", "告警ID": "0x10E0140000", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.76.186", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.53.181)，错误代码=0x10E0140000", "可能原因": "未知", "metadata": {"filename": "********.69 0x10E0140000 连接受保护环境失败.md", "section_number": "********.69", "alarm_type": "HEX", "original_alarm_code": "0x10E0140000"}}, {"主题": "[次要告警]\"eBackup发生试用即将到期\"", "告警ID": "0x201000F40004", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.234.30", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.16.69)，错误代码=0x201000F40004", "可能原因": "未知", "metadata": {"filename": "********.7 0x201000F40004 试用即将到期.md", "section_number": "********.7", "alarm_type": "HEX", "original_alarm_code": "0x201000F40004"}}, {"主题": "[次要告警]\"Certificate发生证书校验失败\"", "告警ID": "0x2010E014000C", "告警级别": "次要", "告警源": "Certificate", "来源系统": "eBackup10.200.196.135", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.247.13)，错误代码=0x2010E014000C", "可能原因": "未知", "metadata": {"filename": "********.70 0x2010E014000C 证书校验失败.md", "section_number": "********.70", "alarm_type": "HEX", "original_alarm_code": "0x2010E014000C"}}, {"主题": "[次要告警]\"eBackup发生存储池空间使用率超出临界值\"", "告警ID": "0x10E00D0000", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.212.15", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.187.102)，错误代码=0x10E00D0000", "可能原因": "未知", "metadata": {"filename": "********.71 0x10E00D0000 存储池空间使用率超出临界值.md", "section_number": "********.71", "alarm_type": "HEX", "original_alarm_code": "0x10E00D0000"}}, {"主题": "[严重告警]\"eBackup发生不支持当前时区信息\"", "告警ID": "0x201000C90002", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.173.184", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.44.140)，错误代码=0x201000C90002", "可能原因": "未知", "metadata": {"filename": "********.72 0x201000C90002 不支持当前时区信息.md", "section_number": "********.72", "alarm_type": "HEX", "original_alarm_code": "0x201000C90002"}}, {"主题": "[严重告警]\"eBackup发生NTP时间差异过大\"", "告警ID": "0x201000C90025", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.158.49", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.254.57)，错误代码=0x201000C90025", "可能原因": "未知", "metadata": {"filename": "********.73 0x201000C90025 NTP时间差异过大.md", "section_number": "********.73", "alarm_type": "HEX", "original_alarm_code": "0x201000C90025"}}, {"主题": "[严重告警]\"Backup发生eBackup服务器到NTP服务器连接异常\"", "告警ID": "0x201000C90024", "告警级别": "严重", "告警源": "Backup", "来源系统": "eBackup10.200.20.153", "定位信息": "区域=SH_CSVW，云服务=Backup，节点类型=backup", "附加信息": "云服务=Backup，服务=backup，节点地址=(10.200.227.83)，错误代码=0x201000C90024", "可能原因": "未知", "metadata": {"filename": "********.74 0x201000C90024 eBackup服务器到NTP服务器连接异常.md", "section_number": "********.74", "alarm_type": "HEX", "original_alarm_code": "0x201000C90024"}}, {"主题": "[次要告警]\"Backup发生eBackup服务器未设置NTP时钟源\"", "告警ID": "0x201000C90009", "告警级别": "次要", "告警源": "Backup", "来源系统": "eBackup10.200.43.63", "定位信息": "区域=SH_CSVW，云服务=Backup，节点类型=backup", "附加信息": "云服务=Backup，服务=backup，节点地址=(10.200.57.80)，错误代码=0x201000C90009", "可能原因": "未知", "metadata": {"filename": "********.75 0x201000C90009 eBackup服务器未设置NTP时钟源.md", "section_number": "********.75", "alarm_type": "HEX", "original_alarm_code": "0x201000C90009"}}, {"主题": "[次要告警]\"eBackup发生SFTP服务器空间不足\"", "告警ID": "0x5800790001", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.158.123", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.181.212)，错误代码=0x5800790001", "可能原因": "未知", "metadata": {"filename": "********.76 0x5800790001 SFTP服务器空间不足.md", "section_number": "********.76", "alarm_type": "HEX", "original_alarm_code": "0x5800790001"}}, {"主题": "[次要告警]\"eBackup发生登录SFTP服务器被拒绝\"", "告警ID": "0x5800790002", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.239.100", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.55.166)，错误代码=0x5800790002", "可能原因": "未知", "metadata": {"filename": "********.77 0x5800790002 登录SFTP服务器被拒绝.md", "section_number": "********.77", "alarm_type": "HEX", "original_alarm_code": "0x5800790002"}}, {"主题": "[重要告警]\"eBackup发生上传管理数据到SFTP服务器失败\"", "告警ID": "0x5800790003", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.67.104", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.241.108)，错误代码=0x5800790003", "可能原因": "未知", "metadata": {"filename": "********.78 0x5800790003 上传管理数据到SFTP服务器失败.md", "section_number": "********.78", "alarm_type": "HEX", "original_alarm_code": "0x5800790003"}}, {"主题": "[严重告警]\"Backup发生备份副本中缺失受保护对象元数据\"", "告警ID": "0x10E01A0010", "告警级别": "严重", "告警源": "Backup", "来源系统": "eBackup10.200.6.236", "定位信息": "区域=SH_CSVW，云服务=Backup，节点类型=backup", "附加信息": "云服务=Backup，服务=backup，节点地址=(10.200.68.108)，错误代码=0x10E01A0010", "可能原因": "未知", "metadata": {"filename": "********.79 0x10E01A0010 备份副本中缺失受保护对象元数据.md", "section_number": "********.79", "alarm_type": "HEX", "original_alarm_code": "0x10E01A0010"}}, {"主题": "[严重告警]\"eBackup发生试用期已过\"", "告警ID": "0x201000F40005", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.106.188", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.160.129)，错误代码=0x201000F40005", "可能原因": "未知", "metadata": {"filename": "********.8 0x201000F40005 试用期已过.md", "section_number": "********.8", "alarm_type": "HEX", "original_alarm_code": "0x201000F40005"}}, {"主题": "[次要告警]\"Backup发生备份副本的元数据损坏\"", "告警ID": "0x10E01A0011", "告警级别": "次要", "告警源": "Backup", "来源系统": "eBackup10.200.86.197", "定位信息": "区域=SH_CSVW，云服务=Backup，节点类型=backup", "附加信息": "云服务=Backup，服务=backup，节点地址=(10.200.89.190)，错误代码=0x10E01A0011", "可能原因": "未知", "metadata": {"filename": "********.80 0x10E01A0011 备份副本的元数据损坏.md", "section_number": "********.80", "alarm_type": "HEX", "original_alarm_code": "0x10E01A0011"}}, {"主题": "[严重告警]\"Backup发生删除备份副本失败\"", "告警ID": "0x10E01A0019", "告警级别": "严重", "告警源": "Backup", "来源系统": "eBackup10.200.228.47", "定位信息": "区域=SH_CSVW，云服务=Backup，节点类型=backup", "附加信息": "云服务=Backup，服务=backup，节点地址=(10.200.124.102)，错误代码=0x10E01A0019", "可能原因": "未知", "metadata": {"filename": "********.81 0x10E01A0019 删除备份副本失败.md", "section_number": "********.81", "alarm_type": "HEX", "original_alarm_code": "0x10E01A0019"}}, {"主题": "[重要告警]\"Backup发生检测到备份副本的数据块有损坏\"", "告警ID": "0x2010E01A0008", "告警级别": "重要", "告警源": "Backup", "来源系统": "eBackup10.200.170.108", "定位信息": "区域=SH_CSVW，云服务=Backup，节点类型=backup", "附加信息": "云服务=Backup，服务=backup，节点地址=(10.200.1.107)，错误代码=0x2010E01A0008", "可能原因": "未知", "metadata": {"filename": "********.82 0x2010E01A0008 检测到备份副本的数据块有损坏.md", "section_number": "********.82", "alarm_type": "HEX", "original_alarm_code": "0x2010E01A0008"}}, {"主题": "[次要告警]\"Backup发生验证备份副本的任务失败\"", "告警ID": "0x2010E01A000E", "告警级别": "次要", "告警源": "Backup", "来源系统": "eBackup10.200.174.162", "定位信息": "区域=SH_CSVW，云服务=Backup，节点类型=backup", "附加信息": "云服务=Backup，服务=backup，节点地址=(10.200.169.243)，错误代码=0x2010E01A000E", "可能原因": "未知", "metadata": {"filename": "********.83 0x2010E01A000E 验证备份副本的任务失败.md", "section_number": "********.83", "alarm_type": "HEX", "original_alarm_code": "0x2010E01A000E"}}, {"主题": "[严重告警]\"eBackup发生Proxy上的NTP服务异常\"", "告警ID": "0x201000C9000A", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.77.63", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.152.164)，错误代码=0x201000C9000A", "可能原因": "未知", "metadata": {"filename": "********.84 0x201000C9000A Proxy上的NTP服务异常.md", "section_number": "********.84", "alarm_type": "HEX", "original_alarm_code": "0x201000C9000A"}}, {"主题": "[严重告警]\"eBackup发生连接远端vpp服务器失败\"", "告警ID": "0x210000000D00", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.66.96", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.48.178)，错误代码=0x210000000D00", "可能原因": "未知", "metadata": {"filename": "********.85 0x210000000D00 连接远端vpp服务器失败.md", "section_number": "********.85", "alarm_type": "HEX", "original_alarm_code": "0x210000000D00"}}, {"主题": "[重要告警]\"eBackup发生清理复制任务的残留资源失败\"", "告警ID": "0x201000C90021", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.39.156", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.81.75)，错误代码=0x201000C90021", "可能原因": "未知", "metadata": {"filename": "********.86 0x201000C90021 清理复制任务的残留资源失败.md", "section_number": "********.86", "alarm_type": "HEX", "original_alarm_code": "0x201000C90021"}}, {"主题": "[严重告警]\"eBackup发生复制任务失败\"", "告警ID": "0x2010E01A001D", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.16.40", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.36.148)，错误代码=0x2010E01A001D", "可能原因": "未知", "metadata": {"filename": "********.87 0x2010E01A001D 复制任务失败.md", "section_number": "********.87", "alarm_type": "HEX", "original_alarm_code": "0x2010E01A001D"}}, {"主题": "[严重告警]\"eBackup发生卸载FusionStorage卷失败\"", "告警ID": "0x10E01A0014", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.110.242", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.137.93)，错误代码=0x10E01A0014", "可能原因": "未知", "metadata": {"filename": "********.88 0x10E01A0014 卸载FusionStorage卷失败.md", "section_number": "********.88", "alarm_type": "HEX", "original_alarm_code": "0x10E01A0014"}}, {"主题": "[次要告警]\"eBackup发生删除FusionStorage卷失败\"", "告警ID": "0x10E01A0015", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.98.201", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.235.24)，错误代码=0x10E01A0015", "可能原因": "未知", "metadata": {"filename": "********.89 0x10E01A0015 删除FusionStorage卷失败.md", "section_number": "********.89", "alarm_type": "HEX", "original_alarm_code": "0x10E01A0015"}}, {"主题": "[严重告警]\"License发生License授权容量耗尽\"", "告警ID": "0x201000F40007", "告警级别": "严重", "告警源": "License", "来源系统": "eBackup10.200.117.247", "定位信息": "区域=SH_CSVW，云服务=License，节点类型=backup", "附加信息": "云服务=License，服务=backup，节点地址=(10.200.195.139)，错误代码=0x201000F40007", "可能原因": "未知", "metadata": {"filename": "********.9 0x201000F40007 License授权容量耗尽.md", "section_number": "********.9", "alarm_type": "HEX", "original_alarm_code": "0x201000F40007"}}, {"主题": "[严重告警]\"eBackup发生卸载OceanStor V3_V5卷或者Dorad\"", "告警ID": "0x10E01A0017", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.195.96", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.233.20)，错误代码=0x10E01A0017", "可能原因": "未知", "metadata": {"filename": "********.90 0x10E01A0017 卸载OceanStor V3_V5卷或者Dorad.md", "section_number": "********.90", "alarm_type": "HEX", "original_alarm_code": "0x10E01A0017"}}, {"主题": "[次要告警]\"eBackup发生重删数据有冗余\"", "告警ID": "0x6300740001", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.53.183", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.38.244)，错误代码=0x6300740001", "可能原因": "未知", "metadata": {"filename": "********.91 0x6300740001 重删数据有冗余.md", "section_number": "********.91", "alarm_type": "HEX", "original_alarm_code": "0x6300740001"}}, {"主题": "[重要告警]\"eBackup发生访问ManageOne失败\"", "告警ID": "0x6000840003", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.98.104", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.71.55)，错误代码=0x6000840003", "可能原因": "未知", "metadata": {"filename": "********.92 0x6000840003 访问ManageOne失败.md", "section_number": "********.92", "alarm_type": "HEX", "original_alarm_code": "0x6000840003"}}, {"主题": "[重要告警]\"Backup发生备份代理存在进度长时间未更新任务\"", "告警ID": "0x105800740001", "告警级别": "重要", "告警源": "Backup", "来源系统": "eBackup10.200.73.176", "定位信息": "区域=SH_CSVW，云服务=Backup，节点类型=backup", "附加信息": "云服务=Backup，服务=backup，节点地址=(10.200.170.20)，错误代码=0x105800740001", "可能原因": "未知", "metadata": {"filename": "********.93 0x105800740001 备份代理存在进度长时间未更新任务.md", "section_number": "********.93", "alarm_type": "HEX", "original_alarm_code": "0x105800740001"}}, {"主题": "[提示信息]\"附录\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.87.174", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "********.94 附录.md", "section_number": "********.94", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示信息]\"登录eBackup服务器\"", "告警ID": "", "告警级别": "提示", "告警源": "Process", "来源系统": "ManageOne10.200.91.123", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=management", "附加信息": "云服务=Process，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "********.94.1 登录eBackup服务器.md", "section_number": "********.94.1", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示信息]\"Karbor\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.148.254", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.11.2 Karbor.md", "section_number": "5.2.11.2", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[重要告警]\"Process发生创建云服务器复制副本失败\"", "告警ID": "1020799", "告警级别": "重要", "告警源": "Process", "来源系统": "SystemOM10.200.37.180", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=compute", "附加信息": "云服务=Process，服务=compute，节点地址=(10.200.77.104)，错误代码=1020799", "可能原因": "未知", "metadata": {"filename": "5.2.11.2.1 1020799 创建云服务器复制副本失败.md", "section_number": "5.2.11.2.1", "alarm_type": "NUM", "original_alarm_code": "1020799"}}, {"主题": "[重要告警]\"System发生云硬盘备份策略自动调度失败\"", "告警ID": "1020771", "告警级别": "重要", "告警源": "System", "来源系统": "SystemOM10.200.193.109", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.3.81)，错误代码=1020771", "可能原因": "未知", "metadata": {"filename": "5.2.11.2.10 1020771 云硬盘备份策略自动调度失败.md", "section_number": "5.2.11.2.10", "alarm_type": "NUM", "original_alarm_code": "1020771"}}, {"主题": "[次要告警]\"System发生云硬盘复制策略自动调度失败\"", "告警ID": "1020770", "告警级别": "次要", "告警源": "System", "来源系统": "SystemOM10.200.195.11", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.127.139)，错误代码=1020770", "可能原因": "未知", "metadata": {"filename": "5.2.11.2.11 1020770 云硬盘复制策略自动调度失败.md", "section_number": "5.2.11.2.11", "alarm_type": "NUM", "original_alarm_code": "1020770"}}, {"主题": "[次要告警]\"System发生云硬盘备份失败\"", "告警ID": "1020768", "告警级别": "次要", "告警源": "System", "来源系统": "SystemOM10.200.93.20", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.247.2)，错误代码=1020768", "可能原因": "未知", "metadata": {"filename": "5.2.11.2.12 1020768 云硬盘备份失败.md", "section_number": "5.2.11.2.12", "alarm_type": "NUM", "original_alarm_code": "1020768"}}, {"主题": "[次要告警]\"System发生FSP证书校验失败\"", "告警ID": "1020762", "告警级别": "次要", "告警源": "System", "来源系统": "SystemOM10.200.214.243", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.248.202)，错误代码=1020762", "可能原因": "未知", "metadata": {"filename": "5.2.11.2.13 1020762 FSP证书校验失败.md", "section_number": "5.2.11.2.13", "alarm_type": "NUM", "original_alarm_code": "1020762"}}, {"主题": "[重要告警]\"System发生IAM证书校验失败\"", "告警ID": "1020761", "告警级别": "重要", "告警源": "System", "来源系统": "SystemOM10.200.208.41", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.132.226)，错误代码=1020761", "可能原因": "未知", "metadata": {"filename": "5.2.11.2.14 1020761 IAM证书校验失败.md", "section_number": "5.2.11.2.14", "alarm_type": "NUM", "original_alarm_code": "1020761"}}, {"主题": "[次要告警]\"System发生节点状态异常\"", "告警ID": "1023299", "告警级别": "次要", "告警源": "System", "来源系统": "SystemOM10.200.9.95", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.142.24)，错误代码=1023299", "可能原因": "未知", "metadata": {"filename": "5.2.11.2.15 1023299 节点状态异常.md", "section_number": "5.2.11.2.15", "alarm_type": "NUM", "original_alarm_code": "1023299"}}, {"主题": "[次要告警]\"System发生组件状态异常\"", "告警ID": "1023298", "告警级别": "次要", "告警源": "System", "来源系统": "SystemOM10.200.101.73", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.59.215)，错误代码=1023298", "可能原因": "未知", "metadata": {"filename": "5.2.11.2.16 1023298 组件状态异常.md", "section_number": "5.2.11.2.16", "alarm_type": "NUM", "original_alarm_code": "1023298"}}, {"主题": "[次要告警]\"System发生外部NTP时钟同步异常\"", "告警ID": "1023296", "告警级别": "次要", "告警源": "System", "来源系统": "SystemOM10.200.203.177", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.36.248)，错误代码=1023296", "可能原因": "未知", "metadata": {"filename": "5.2.11.2.17 1023296 外部NTP时钟同步异常.md", "section_number": "5.2.11.2.17", "alarm_type": "NUM", "original_alarm_code": "1023296"}}, {"主题": "[重要告警]\"System发生备份系统数据发生失败\"", "告警ID": "1023295", "告警级别": "重要", "告警源": "System", "来源系统": "SystemOM10.200.170.112", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.9.66)，错误代码=1023295", "可能原因": "未知", "metadata": {"filename": "5.2.11.2.18 1023295 备份系统数据发生失败.md", "section_number": "5.2.11.2.18", "alarm_type": "NUM", "original_alarm_code": "1023295"}}, {"主题": "[重要告警]\"Process发生FTP服务器证书校验失败\"", "告警ID": "1023282", "告警级别": "重要", "告警源": "Process", "来源系统": "SystemOM10.200.125.227", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=compute", "附加信息": "云服务=Process，服务=compute，节点地址=(10.200.211.55)，错误代码=1023282", "可能原因": "未知", "metadata": {"filename": "5.2.11.2.19 1023282 FTP服务器证书校验失败.md", "section_number": "5.2.11.2.19", "alarm_type": "NUM", "original_alarm_code": "1023282"}}, {"主题": "[重要告警]\"Process发生云服务器备份配额消耗到配额总量的阈值\"", "告警ID": "1020796", "告警级别": "重要", "告警源": "Process", "来源系统": "SystemOM10.200.31.159", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=compute", "附加信息": "云服务=Process，服务=compute，节点地址=(10.200.229.37)，错误代码=1020796", "可能原因": "未知", "metadata": {"filename": "5.2.11.2.2 1020796 云服务器备份配额消耗到配额总量的阈值.md", "section_number": "5.2.11.2.2", "alarm_type": "NUM", "original_alarm_code": "1020796"}}, {"主题": "[次要告警]\"System发生系统证书即将过期\"", "告警ID": "1023279", "告警级别": "次要", "告警源": "System", "来源系统": "SystemOM10.200.195.83", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.65.91)，错误代码=1023279", "可能原因": "未知", "metadata": {"filename": "5.2.11.2.20 1023279 系统证书即将过期.md", "section_number": "5.2.11.2.20", "alarm_type": "NUM", "original_alarm_code": "1023279"}}, {"主题": "[次要告警]\"System发生系统证书已经过期\"", "告警ID": "1023278", "告警级别": "次要", "告警源": "System", "来源系统": "SystemOM10.200.139.53", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.48.213)，错误代码=1023278", "可能原因": "未知", "metadata": {"filename": "5.2.11.2.21 1023278 系统证书已经过期.md", "section_number": "5.2.11.2.21", "alarm_type": "NUM", "original_alarm_code": "1023278"}}, {"主题": "[重要告警]\"System发生消息队列卡死\"", "告警ID": "1023277", "告警级别": "重要", "告警源": "System", "来源系统": "SystemOM10.200.124.235", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.169.132)，错误代码=1023277", "可能原因": "未知", "metadata": {"filename": "5.2.11.2.22 1023277 消息队列卡死.md", "section_number": "5.2.11.2.22", "alarm_type": "NUM", "original_alarm_code": "1023277"}}, {"主题": "[次要告警]\"Network发生消息队列产生网络分区\"", "告警ID": "1023276", "告警级别": "次要", "告警源": "Network", "来源系统": "SystemOM10.200.28.175", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=compute", "附加信息": "云服务=Network，服务=compute，节点地址=(10.200.161.237)，错误代码=1023276", "可能原因": "未知", "metadata": {"filename": "5.2.11.2.23 1023276 消息队列产生网络分区.md", "section_number": "5.2.11.2.23", "alarm_type": "NUM", "original_alarm_code": "1023276"}}, {"主题": "[重要告警]\"System发生CPU使用率超过阈值\"", "告警ID": "1023099", "告警级别": "重要", "告警源": "System", "来源系统": "SystemOM10.200.159.154", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.236.67)，错误代码=1023099", "可能原因": "未知", "metadata": {"filename": "5.2.11.2.24 1023099 CPU使用率超过阈值.md", "section_number": "5.2.11.2.24", "alarm_type": "NUM", "original_alarm_code": "1023099"}}, {"主题": "[次要告警]\"System发生内存使用率超过阈值\"", "告警ID": "1023098", "告警级别": "次要", "告警源": "System", "来源系统": "SystemOM10.200.172.139", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.217.72)，错误代码=1023098", "可能原因": "未知", "metadata": {"filename": "5.2.11.2.25 1023098 内存使用率超过阈值.md", "section_number": "5.2.11.2.25", "alarm_type": "NUM", "original_alarm_code": "1023098"}}, {"主题": "[重要告警]\"Storage发生磁盘使用率超过阈值\"", "告警ID": "1023097", "告警级别": "重要", "告警源": "Storage", "来源系统": "SystemOM10.200.83.243", "定位信息": "区域=SH_CSVW，云服务=Storage，节点类型=compute", "附加信息": "云服务=Storage，服务=compute，节点地址=(10.200.191.159)，错误代码=1023097", "可能原因": "未知", "metadata": {"filename": "5.2.11.2.26 1023097 磁盘使用率超过阈值.md", "section_number": "5.2.11.2.26", "alarm_type": "NUM", "original_alarm_code": "1023097"}}, {"主题": "[重要告警]\"System发生执行复制策略失败\"", "告警ID": "1020800", "告警级别": "重要", "告警源": "System", "来源系统": "SystemOM10.200.60.64", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.210.100)，错误代码=1020800", "可能原因": "未知", "metadata": {"filename": "5.2.11.2.27 1020800 执行复制策略失败.md", "section_number": "5.2.11.2.27", "alarm_type": "NUM", "original_alarm_code": "1020800"}}, {"主题": "[次要告警]\"System发生跨区域复制策略自动调度失败\"", "告警ID": "1020803", "告警级别": "次要", "告警源": "System", "来源系统": "SystemOM10.200.232.103", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.53.161)，错误代码=1020803", "可能原因": "未知", "metadata": {"filename": "5.2.11.2.28 1020803 跨区域复制策略自动调度失败.md", "section_number": "5.2.11.2.28", "alarm_type": "NUM", "original_alarm_code": "1020803"}}, {"主题": "[次要告警]\"Network发生连接ManageOne运营平台失败\"", "告警ID": "1020759", "告警级别": "次要", "告警源": "Network", "来源系统": "SystemOM10.200.198.133", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=compute", "附加信息": "云服务=Network，服务=compute，节点地址=(10.200.40.48)，错误代码=1020759", "可能原因": "未知", "metadata": {"filename": "5.2.11.2.29 1020759 连接ManageOne运营平台失败.md", "section_number": "5.2.11.2.29", "alarm_type": "NUM", "original_alarm_code": "1020759"}}, {"主题": "[重要告警]\"Process发生云服务器备份策略自动调度失败\"", "告警ID": "1020791", "告警级别": "重要", "告警源": "Process", "来源系统": "SystemOM10.200.72.174", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=compute", "附加信息": "云服务=Process，服务=compute，节点地址=(10.200.228.46)，错误代码=1020791", "可能原因": "未知", "metadata": {"filename": "5.2.11.2.3 1020791 云服务器备份策略自动调度失败.md", "section_number": "5.2.11.2.3", "alarm_type": "NUM", "original_alarm_code": "1020791"}}, {"主题": "[次要告警]\"System发生上报计量数据失败\"", "告警ID": "1020758", "告警级别": "次要", "告警源": "System", "来源系统": "SystemOM10.200.76.88", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.114.223)，错误代码=1020758", "可能原因": "未知", "metadata": {"filename": "5.2.11.2.30 1020758 上报计量数据失败.md", "section_number": "5.2.11.2.30", "alarm_type": "NUM", "original_alarm_code": "1020758"}}, {"主题": "[重要告警]\"Network发生备份服务节点间网络异常\"", "告警ID": "1023093", "告警级别": "重要", "告警源": "Network", "来源系统": "SystemOM10.200.2.60", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=compute", "附加信息": "云服务=Network，服务=compute，节点地址=(10.200.25.129)，错误代码=1023093", "可能原因": "未知", "metadata": {"filename": "5.2.11.2.31 1023093 备份服务节点间网络异常.md", "section_number": "5.2.11.2.31", "alarm_type": "NUM", "original_alarm_code": "1023093"}}, {"主题": "[重要告警]\"Process发生注册CSBS-VBS到统一证书管理服务失败\"", "告警ID": "1020756", "告警级别": "重要", "告警源": "Process", "来源系统": "SystemOM10.200.138.49", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=compute", "附加信息": "云服务=Process，服务=compute，节点地址=(10.200.84.47)，错误代码=1020756", "可能原因": "未知", "metadata": {"filename": "5.2.11.2.32 1020756 注册CSBS-VBS到统一证书管理服务失败.md", "section_number": "5.2.11.2.32", "alarm_type": "NUM", "original_alarm_code": "1020756"}}, {"主题": "[次要告警]\"Process发生云服务器备份复制策略自动调度失败\"", "告警ID": "1020790", "告警级别": "次要", "告警源": "Process", "来源系统": "SystemOM10.200.10.94", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=compute", "附加信息": "云服务=Process，服务=compute，节点地址=(10.200.33.230)，错误代码=1020790", "可能原因": "未知", "metadata": {"filename": "5.2.11.2.4 1020790 云服务器备份复制策略自动调度失败.md", "section_number": "5.2.11.2.4", "alarm_type": "NUM", "original_alarm_code": "1020790"}}, {"主题": "[次要告警]\"Process发生云服务器备份失败\"", "告警ID": "1020788", "告警级别": "次要", "告警源": "Process", "来源系统": "SystemOM10.200.66.155", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=compute", "附加信息": "云服务=Process，服务=compute，节点地址=(10.200.157.248)，错误代码=1020788", "可能原因": "未知", "metadata": {"filename": "5.2.11.2.5 1020788 云服务器备份失败.md", "section_number": "5.2.11.2.5", "alarm_type": "NUM", "original_alarm_code": "1020788"}}, {"主题": "[重要告警]\"Process发生启动消息队列服务失败\"", "告警ID": "1020783", "告警级别": "重要", "告警源": "Process", "来源系统": "SystemOM10.200.57.140", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=compute", "附加信息": "云服务=Process，服务=compute，节点地址=(10.200.2.24)，错误代码=1020783", "可能原因": "未知", "metadata": {"filename": "5.2.11.2.6 1020783 启动消息队列服务失败.md", "section_number": "5.2.11.2.6", "alarm_type": "NUM", "original_alarm_code": "1020783"}}, {"主题": "[重要告警]\"System发生消息队列存在消息响应超时\"", "告警ID": "1020782", "告警级别": "重要", "告警源": "System", "来源系统": "SystemOM10.200.137.26", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.140.142)，错误代码=1020782", "可能原因": "未知", "metadata": {"filename": "5.2.11.2.7 1020782 消息队列存在消息响应超时.md", "section_number": "5.2.11.2.7", "alarm_type": "NUM", "original_alarm_code": "1020782"}}, {"主题": "[次要告警]\"System发生创建云硬盘复制副本失败\"", "告警ID": "1020779", "告警级别": "次要", "告警源": "System", "来源系统": "SystemOM10.200.47.138", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.202.47)，错误代码=1020779", "可能原因": "未知", "metadata": {"filename": "5.2.11.2.8 1020779 创建云硬盘复制副本失败.md", "section_number": "5.2.11.2.8", "alarm_type": "NUM", "original_alarm_code": "1020779"}}, {"主题": "[重要告警]\"System发生云硬盘备份配额消耗到配额总量的阈值\"", "告警ID": "1020776", "告警级别": "重要", "告警源": "System", "来源系统": "SystemOM10.200.181.189", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.126.247)，错误代码=1020776", "可能原因": "未知", "metadata": {"filename": "5.2.11.2.9 1020776 云硬盘备份配额消耗到配额总量的阈值.md", "section_number": "5.2.11.2.9", "alarm_type": "NUM", "original_alarm_code": "1020776"}}, {"主题": "[提示信息]\"eReplication\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.56.9", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.11.3 eReplication.md", "section_number": "5.2.11.3", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[严重告警]\"Backup发生备份失败\"", "告警ID": "0x3230014", "告警级别": "严重", "告警源": "Backup", "来源系统": "eBackup10.200.219.221", "定位信息": "区域=SH_CSVW，云服务=Backup，节点类型=backup", "附加信息": "云服务=Backup，服务=backup，节点地址=(10.200.138.115)，错误代码=0x3230014", "可能原因": "未知", "metadata": {"filename": "5.2.11.3.1 0x3230014 备份失败.md", "section_number": "5.2.11.3.1", "alarm_type": "HEX", "original_alarm_code": "0x3230014"}}, {"主题": "[重要告警]\"eBackup发生HA网关不通\"", "告警ID": "0x3230034", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.81.191", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.183.242)，错误代码=0x3230034", "可能原因": "未知", "metadata": {"filename": "5.2.11.3.10 0x3230034 HA网关不通.md", "section_number": "5.2.11.3.10", "alarm_type": "HEX", "original_alarm_code": "0x3230034"}}, {"主题": "[次要告警]\"eBackup发生仲裁服务异常\"", "告警ID": "0x3230036", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.107.29", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.60.81)，错误代码=0x3230036", "可能原因": "未知", "metadata": {"filename": "5.2.11.3.11 0x3230036 仲裁服务异常.md", "section_number": "5.2.11.3.11", "alarm_type": "HEX", "original_alarm_code": "0x3230036"}}, {"主题": "[严重告警]\"eBackup发生服务实例故障恢复失败\"", "告警ID": "0x3230037", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.55.163", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.21.103)，错误代码=0x3230037", "可能原因": "未知", "metadata": {"filename": "5.2.11.3.12 0x3230037 服务实例故障恢复失败.md", "section_number": "5.2.11.3.12", "alarm_type": "HEX", "original_alarm_code": "0x3230037"}}, {"主题": "[重要告警]\"eBackup发生服务实例重保护失败\"", "告警ID": "0x3230038", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.134.80", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.154.15)，错误代码=0x3230038", "可能原因": "未知", "metadata": {"filename": "5.2.11.3.13 0x3230038 服务实例重保护失败.md", "section_number": "5.2.11.3.13", "alarm_type": "HEX", "original_alarm_code": "0x3230038"}}, {"主题": "[重要告警]\"eBackup发生虚拟机不满足保护要求\"", "告警ID": "0x323003A", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.19.32", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.61.44)，错误代码=0x323003A", "可能原因": "未知", "metadata": {"filename": "5.2.11.3.14 0x323003A 虚拟机不满足保护要求.md", "section_number": "5.2.11.3.14", "alarm_type": "HEX", "original_alarm_code": "0x323003A"}}, {"主题": "[次要告警]\"eBackup发生虚拟机中已卸载的卷未从服务实例中清理\"", "告警ID": "0x323003B", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.211.178", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.90.160)，错误代码=0x323003B", "可能原因": "未知", "metadata": {"filename": "5.2.11.3.15 0x323003B 虚拟机中已卸载的卷未从服务实例中清理.md", "section_number": "5.2.11.3.15", "alarm_type": "HEX", "original_alarm_code": "0x323003B"}}, {"主题": "[次要告警]\"eBackup发生虚拟机的卷未创建容灾保护\"", "告警ID": "0x323003C", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.176.127", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.18.31)，错误代码=0x323003C", "可能原因": "未知", "metadata": {"filename": "5.2.11.3.16 0x323003C 虚拟机的卷未创建容灾保护.md", "section_number": "5.2.11.3.16", "alarm_type": "HEX", "original_alarm_code": "0x323003C"}}, {"主题": "[次要告警]\"eBackup发生受保护虚拟机从服务实例中被移除\"", "告警ID": "0x323003D", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.232.139", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.35.191)，错误代码=0x323003D", "可能原因": "未知", "metadata": {"filename": "5.2.11.3.17 0x323003D 受保护虚拟机从服务实例中被移除.md", "section_number": "5.2.11.3.17", "alarm_type": "HEX", "original_alarm_code": "0x323003D"}}, {"主题": "[次要告警]\"eBackup发生服务实例不满足故障恢复要求\"", "告警ID": "0x323003E", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.140.42", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.34.193)，错误代码=0x323003E", "可能原因": "未知", "metadata": {"filename": "5.2.11.3.18 0x323003E 服务实例不满足故障恢复要求.md", "section_number": "5.2.11.3.18", "alarm_type": "HEX", "original_alarm_code": "0x323003E"}}, {"主题": "[次要告警]\"Certificate发生IAM证书更新失败\"", "告警ID": "0x323003F", "告警级别": "次要", "告警源": "Certificate", "来源系统": "eBackup10.200.245.148", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.100.22)，错误代码=0x323003F", "可能原因": "未知", "metadata": {"filename": "5.2.11.3.19 0x323003F IAM证书更新失败.md", "section_number": "5.2.11.3.19", "alarm_type": "HEX", "original_alarm_code": "0x323003F"}}, {"主题": "[次要告警]\"Certificate发生证书已过期\"", "告警ID": "0x3230024", "告警级别": "次要", "告警源": "Certificate", "来源系统": "eBackup10.200.200.43", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.130.35)，错误代码=0x3230024", "可能原因": "未知", "metadata": {"filename": "5.2.11.3.2 0x3230024 证书已过期.md", "section_number": "5.2.11.3.2", "alarm_type": "HEX", "original_alarm_code": "0x3230024"}}, {"主题": "[次要告警]\"eBackup发生上报计量信息失败\"", "告警ID": "0x3230041", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.112.53", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.83.46)，错误代码=0x3230041", "可能原因": "未知", "metadata": {"filename": "5.2.11.3.20 0x3230041 上报计量信息失败.md", "section_number": "5.2.11.3.20", "alarm_type": "HEX", "original_alarm_code": "0x3230041"}}, {"主题": "[严重告警]\"eBackup发生连接日志服务器异常\"", "告警ID": "0x3230042", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.154.92", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.103.173)，错误代码=0x3230042", "可能原因": "未知", "metadata": {"filename": "5.2.11.3.21 0x3230042 连接日志服务器异常.md", "section_number": "5.2.11.3.21", "alarm_type": "HEX", "original_alarm_code": "0x3230042"}}, {"主题": "[重要告警]\"Database发生高可用集群中高斯数据库主备间复制中断\"", "告警ID": "0x3230043", "告警级别": "重要", "告警源": "Database", "来源系统": "eBackup10.200.119.52", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=backup", "附加信息": "云服务=Database，服务=backup，节点地址=(10.200.82.137)，错误代码=0x3230043", "可能原因": "未知", "metadata": {"filename": "5.2.11.3.22 0x3230043 高可用集群中高斯数据库主备间复制中断.md", "section_number": "5.2.11.3.22", "alarm_type": "HEX", "original_alarm_code": "0x3230043"}}, {"主题": "[重要告警]\"eBackup发生共享卷关联的虚拟机不在同一个服务实例中\"", "告警ID": "0x3230046", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.134.156", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.138.54)，错误代码=0x3230046", "可能原因": "未知", "metadata": {"filename": "5.2.11.3.23 0x3230046 共享卷关联的虚拟机不在同一个服务实例中.md", "section_number": "5.2.11.3.23", "alarm_type": "HEX", "original_alarm_code": "0x3230046"}}, {"主题": "[严重告警]\"eBackup发生订单实施结果通知失败\"", "告警ID": "0x3230047", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.205.47", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.243.52)，错误代码=0x3230047", "可能原因": "未知", "metadata": {"filename": "5.2.11.3.24 0x3230047 订单实施结果通知失败.md", "section_number": "5.2.11.3.24", "alarm_type": "HEX", "original_alarm_code": "0x3230047"}}, {"主题": "[重要告警]\"License发生License授权容量耗尽\"", "告警ID": "0x3230048", "告警级别": "重要", "告警源": "License", "来源系统": "eBackup10.200.205.210", "定位信息": "区域=SH_CSVW，云服务=License，节点类型=backup", "附加信息": "云服务=License，服务=backup，节点地址=(10.200.2.166)，错误代码=0x3230048", "可能原因": "未知", "metadata": {"filename": "5.2.11.3.25 0x3230048 License授权容量耗尽.md", "section_number": "5.2.11.3.25", "alarm_type": "HEX", "original_alarm_code": "0x3230048"}}, {"主题": "[次要告警]\"eBackup发生系统90天试用期到期\"", "告警ID": "0x3230049", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.229.55", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.69.102)，错误代码=0x3230049", "可能原因": "未知", "metadata": {"filename": "5.2.11.3.26 0x3230049 系统90天试用期到期.md", "section_number": "5.2.11.3.26", "alarm_type": "HEX", "original_alarm_code": "0x3230049"}}, {"主题": "[重要告警]\"Certificate发生证书即将过期\"", "告警ID": "0x323005C", "告警级别": "重要", "告警源": "Certificate", "来源系统": "eBackup10.200.216.95", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.66.229)，错误代码=0x323005C", "可能原因": "未知", "metadata": {"filename": "5.2.11.3.27 0x323005C 证书即将过期.md", "section_number": "5.2.11.3.27", "alarm_type": "HEX", "original_alarm_code": "0x323005C"}}, {"主题": "[次要告警]\"Certificate发生证书已经过期\"", "告警ID": "0x323005D", "告警级别": "次要", "告警源": "Certificate", "来源系统": "eBackup10.200.85.126", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.4.230)，错误代码=0x323005D", "可能原因": "未知", "metadata": {"filename": "5.2.11.3.28 0x323005D 证书已经过期.md", "section_number": "5.2.11.3.28", "alarm_type": "HEX", "original_alarm_code": "0x323005D"}}, {"主题": "[严重告警]\"eBackup发生对接管理平台失败\"", "告警ID": "0x3230064", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.247.197", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.77.33)，错误代码=0x3230064", "可能原因": "未知", "metadata": {"filename": "5.2.11.3.29 0x3230064 对接管理平台失败.md", "section_number": "5.2.11.3.29", "alarm_type": "HEX", "original_alarm_code": "0x3230064"}}, {"主题": "[严重告警]\"Certificate发生证书校验失败\"", "告警ID": "0x3230025", "告警级别": "严重", "告警源": "Certificate", "来源系统": "eBackup10.200.145.30", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.225.13)，错误代码=0x3230025", "可能原因": "未知", "metadata": {"filename": "5.2.11.3.3 0x3230025 证书校验失败.md", "section_number": "5.2.11.3.3", "alarm_type": "HEX", "original_alarm_code": "0x3230025"}}, {"主题": "[重要告警]\"eBackup发生占位虚拟机不存在\"", "告警ID": "0x323002C", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.70.36", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.222.11)，错误代码=0x323002C", "可能原因": "未知", "metadata": {"filename": "5.2.11.3.4 0x323002C 占位虚拟机不存在.md", "section_number": "5.2.11.3.4", "alarm_type": "HEX", "original_alarm_code": "0x323002C"}}, {"主题": "[严重告警]\"eBackup发生占位虚拟机未配置\"", "告警ID": "0x323002D", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.163.78", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.37.38)，错误代码=0x323002D", "可能原因": "未知", "metadata": {"filename": "5.2.11.3.5 0x323002D 占位虚拟机未配置.md", "section_number": "5.2.11.3.5", "alarm_type": "HEX", "original_alarm_code": "0x323002D"}}, {"主题": "[次要告警]\"eBackup发生一致性组状态异常\"", "告警ID": "0x323002F", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.132.110", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.11.183)，错误代码=0x323002F", "可能原因": "未知", "metadata": {"filename": "5.2.11.3.6 0x323002F 一致性组状态异常.md", "section_number": "5.2.11.3.6", "alarm_type": "HEX", "original_alarm_code": "0x323002F"}}, {"主题": "[次要告警]\"eBackup发生HA心跳中断\"", "告警ID": "0x3230030", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.18.126", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.14.149)，错误代码=0x3230030", "可能原因": "未知", "metadata": {"filename": "5.2.11.3.7 0x3230030 HA心跳中断.md", "section_number": "5.2.11.3.7", "alarm_type": "HEX", "original_alarm_code": "0x3230030"}}, {"主题": "[次要告警]\"eBackup发生HA同步失败\"", "告警ID": "0x3230031", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.254.75", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.42.49)，错误代码=0x3230031", "可能原因": "未知", "metadata": {"filename": "5.2.11.3.8 0x3230031 HA同步失败.md", "section_number": "5.2.11.3.8", "alarm_type": "HEX", "original_alarm_code": "0x3230031"}}, {"主题": "[重要告警]\"eBackup发生HA链路中断\"", "告警ID": "0x3230033", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.180.109", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.145.118)，错误代码=0x3230033", "可能原因": "未知", "metadata": {"filename": "5.2.11.3.9 0x3230033 HA链路中断.md", "section_number": "5.2.11.3.9", "alarm_type": "HEX", "original_alarm_code": "0x3230033"}}, {"主题": "[提示信息]\"消息通知服务\"", "告警ID": "", "告警级别": "提示", "告警源": "Process", "来源系统": "ManageOne10.200.16.183", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=management", "附加信息": "云服务=Process，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.15 消息通知服务.md", "section_number": "5.2.15", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示告警]\"Process发生tomcat进程不存在\"", "告警ID": "2000401", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.166.145", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.167.56)，对端地址=(other_process=10.200.192.193)", "可能原因": "未知", "metadata": {"filename": "5.2.15.1 ALM-2000401 tomcat进程不存在.md", "section_number": "5.2.15.1", "alarm_type": "ALM", "original_alarm_code": "ALM-2000401"}}, {"主题": "[严重告警]\"Database发生GaussdbHA上传远端备份服务器失败\"", "告警ID": "2000463", "告警级别": "严重", "告警源": "Database", "来源系统": "ServiceOM10.200.66.57", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.98.187)，对端地址=(other_database=10.200.27.78)", "可能原因": "未知", "metadata": {"filename": "5.2.15.10 ALM-2000463 GaussdbHA上传远端备份服务器失败.md", "section_number": "5.2.15.10", "alarm_type": "ALM", "original_alarm_code": "ALM-2000463"}}, {"主题": "[严重告警]\"Process发生zookeeper进程不存在\"", "告警ID": "2000471", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.54.246", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.147.116)，对端地址=(other_process=10.200.84.223)", "可能原因": "未知", "metadata": {"filename": "5.2.15.11 ALM-2000471 zookeeper进程不存在.md", "section_number": "5.2.15.11", "alarm_type": "ALM", "original_alarm_code": "ALM-2000471"}}, {"主题": "[重要告警]\"System发生证书异常\"", "告警ID": "2000493", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.32.103", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.141.106)，对端地址=(other_system=10.200.10.58)", "可能原因": "未知", "metadata": {"filename": "5.2.15.12 ALM-2000493 证书异常.md", "section_number": "5.2.15.12", "alarm_type": "ALM", "original_alarm_code": "ALM-2000493"}}, {"主题": "[次要告警]\"Process发生ns进程不存在\"", "告警ID": "2000420", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.51.232", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.245.138)，对端地址=(other_process=10.200.180.58)", "可能原因": "未知", "metadata": {"filename": "5.2.15.2 ALM-2000420 ns进程不存在.md", "section_number": "5.2.15.2", "alarm_type": "ALM", "original_alarm_code": "ALM-2000420"}}, {"主题": "[严重告警]\"Process发生ps进程不存在\"", "告警ID": "2000421", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.214.193", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.195.36)，对端地址=(other_process=10.200.23.255)", "可能原因": "未知", "metadata": {"filename": "5.2.15.3 ALM-2000421 ps进程不存在.md", "section_number": "5.2.15.3", "alarm_type": "ALM", "original_alarm_code": "ALM-2000421"}}, {"主题": "[严重告警]\"Process发生memcached进程不存在\"", "告警ID": "2000456", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.200.206", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.247.199)，对端地址=(other_process=10.200.176.111)", "可能原因": "未知", "metadata": {"filename": "5.2.15.4 ALM-2000456 memcached进程不存在.md", "section_number": "5.2.15.4", "alarm_type": "ALM", "original_alarm_code": "ALM-2000456"}}, {"主题": "[提示告警]\"Process发生kafka进程不存在\"", "告警ID": "2000469", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.192.254", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.71.17)，对端地址=(other_process=10.200.224.158)", "可能原因": "未知", "metadata": {"filename": "5.2.15.5 ALM-2000469 kafka进程不存在.md", "section_number": "5.2.15.5", "alarm_type": "ALM", "original_alarm_code": "ALM-2000469"}}, {"主题": "[严重告警]\"Process发生smn_haproxy进程不存在\"", "告警ID": "2000488", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.159.208", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.21.90)，对端地址=(other_process=10.200.99.5)", "可能原因": "未知", "metadata": {"filename": "5.2.15.6 ALM-2000488 smn_haproxy进程不存在.md", "section_number": "5.2.15.6", "alarm_type": "ALM", "original_alarm_code": "ALM-2000488"}}, {"主题": "[次要告警]\"Process发生smn_keepalived进程不存在\"", "告警ID": "2000489", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.124.121", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.150.35)，对端地址=(other_process=10.200.114.158)", "可能原因": "未知", "metadata": {"filename": "5.2.15.7 ALM-2000489 smn_keepalived进程不存在.md", "section_number": "5.2.15.7", "alarm_type": "ALM", "original_alarm_code": "ALM-2000489"}}, {"主题": "[提示告警]\"Database发生GaussdbHA服务进程异常\"", "告警ID": "2000460", "告警级别": "提示", "告警源": "Database", "来源系统": "ServiceOM10.200.149.13", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.62.72)，对端地址=(other_database=10.200.234.108)", "可能原因": "未知", "metadata": {"filename": "5.2.15.8 ALM-2000460 GaussdbHA服务进程异常.md", "section_number": "5.2.15.8", "alarm_type": "ALM", "original_alarm_code": "ALM-2000460"}}, {"主题": "[次要告警]\"Database发生GaussdbHA备份失败\"", "告警ID": "2000462", "告警级别": "次要", "告警源": "Database", "来源系统": "ServiceOM10.200.235.211", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.189.42)，对端地址=(other_database=10.200.226.98)", "可能原因": "未知", "metadata": {"filename": "5.2.15.9 ALM-2000462 GaussdbHA备份失败.md", "section_number": "5.2.15.9", "alarm_type": "ALM", "original_alarm_code": "ALM-2000462"}}, {"主题": "[提示信息]\"公共组件\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.110.9", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.17 公共组件.md", "section_number": "5.2.17", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示信息]\"ECS UI\"", "告警ID": "", "告警级别": "提示", "告警源": "Compute", "来源系统": "ManageOne10.200.120.51", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=management", "附加信息": "云服务=Compute，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.17.1 ECS UI.md", "section_number": "5.2.17.1", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示告警]\"Process发生ECS_UI Tomcat进程不存在\"", "告警ID": "1160001", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.32.121", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.246.8)，对端地址=(other_process=10.200.11.67)", "可能原因": "未知", "metadata": {"filename": "5.2.17.1.1 ALM-1160001 ECS_UI Tomcat进程不存在.md", "section_number": "5.2.17.1.1", "alarm_type": "ALM", "original_alarm_code": "ALM-1160001"}}, {"主题": "[严重告警]\"Process发生ECS_UI中ntp进程故障\"", "告警ID": "1160003", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.193.243", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.155.36)，对端地址=(other_process=10.200.87.205)", "可能原因": "未知", "metadata": {"filename": "5.2.17.1.2 ALM-1160003 ECS_UI中ntp进程故障.md", "section_number": "5.2.17.1.2", "alarm_type": "ALM", "original_alarm_code": "ALM-1160003"}}, {"主题": "[提示信息]\"SDR\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.211.220", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.17.10 SDR.md", "section_number": "5.2.17.10", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示信息]\"计量话单告警参考\"", "告警ID": "", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ManageOne10.200.175.3", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=management", "附加信息": "云服务=Monitor，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.17.10.1 计量话单告警参考.md", "section_number": "5.2.17.10.1", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[次要告警]\"System发生计量话单生成话单失败\"", "告警ID": "2000301", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.58.82", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.223.252)，对端地址=(other_system=10.200.37.183)", "可能原因": "未知", "metadata": {"filename": "5.2.17.10.1.1 ALM-2000301 计量话单生成话单失败.md", "section_number": "5.2.17.10.1.1", "alarm_type": "ALM", "original_alarm_code": "ALM-2000301"}}, {"主题": "[严重告警]\"Process发生计量话单服务异常\"", "告警ID": "2000317", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.61.167", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.55.16)，对端地址=(other_process=10.200.104.10)", "可能原因": "未知", "metadata": {"filename": "5.2.17.10.1.2 ALM-2000317 计量话单服务异常.md", "section_number": "5.2.17.10.1.2", "alarm_type": "ALM", "original_alarm_code": "ALM-2000317"}}, {"主题": "[次要告警]\"Monitor发生计量话单证书告警\"", "告警ID": "2000327", "告警级别": "次要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.129.246", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.43.12)，对端地址=(other_monitor=10.200.195.146)", "可能原因": "未知", "metadata": {"filename": "5.2.17.10.1.3 ALM-2000327 计量话单证书告警.md", "section_number": "5.2.17.10.1.3", "alarm_type": "ALM", "original_alarm_code": "ALM-2000327"}}, {"主题": "[提示告警]\"Monitor发生计量话单证书告警\"", "告警ID": "2000328", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.179.27", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.152.26)，对端地址=(other_monitor=10.200.154.52)", "可能原因": "未知", "metadata": {"filename": "5.2.17.10.1.4 ALM-2000328 计量话单证书告警.md", "section_number": "5.2.17.10.1.4", "alarm_type": "ALM", "original_alarm_code": "ALM-2000328"}}, {"主题": "[提示信息]\"参考信息\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.112.63", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.17.10.2 参考信息.md", "section_number": "5.2.17.10.2", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示信息]\"配置屏蔽规则\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.248.229", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.17.10.2.1 配置屏蔽规则.md", "section_number": "5.2.17.10.2.1", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示信息]\"CCS\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.147.5", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.17.11 CCS.md", "section_number": "5.2.17.11", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[次要告警]\"Process发生ccs进程异常\"", "告警ID": "1320004", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.132.27", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.227.30)，对端地址=(other_process=10.200.113.229)", "可能原因": "未知", "metadata": {"filename": "5.2.17.11.1 ALM-1320004 ccs进程异常.md", "section_number": "5.2.17.11.1", "alarm_type": "ALM", "original_alarm_code": "ALM-1320004"}}, {"主题": "[重要告警]\"System发生证书异常\"", "告警ID": "1320019", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.239.189", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.219.99)，对端地址=(other_system=10.200.165.133)", "可能原因": "未知", "metadata": {"filename": "5.2.17.11.2 ALM-1320019 证书异常.md", "section_number": "5.2.17.11.2", "alarm_type": "ALM", "original_alarm_code": "ALM-1320019"}}, {"主题": "[提示信息]\"云平台仲裁服务\"", "告警ID": "", "告警级别": "提示", "告警源": "Process", "来源系统": "ManageOne10.200.226.157", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=management", "附加信息": "云服务=Process，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.17.12 云平台仲裁服务.md", "section_number": "5.2.17.12", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示告警]\"System发生节点系统时钟跳变超过一分钟\"", "告警ID": "2000266", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.200.128", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.233.44)，对端地址=(other_system=10.200.233.110)", "可能原因": "未知", "metadata": {"filename": "5.2.17.12.1 ALM-2000266 节点系统时钟跳变超过一分钟.md", "section_number": "5.2.17.12.1", "alarm_type": "ALM", "original_alarm_code": "ALM-2000266"}}, {"主题": "[提示告警]\"ETCD发生Etcd服务状态异常\"", "告警ID": "2001106", "告警级别": "提示", "告警源": "ETCD", "来源系统": "ServiceOM10.200.31.217", "定位信息": "区域=SH_CSVW，云服务=ETCD，节点类型=mgt", "附加信息": "云服务=ETCD，服务=mgt，本端地址=(10.200.235.64)，对端地址=(other_etcd=10.200.66.7)", "可能原因": "未知", "metadata": {"filename": "5.2.17.12.2 ALM-2001106 Etcd服务状态异常.md", "section_number": "5.2.17.12.2", "alarm_type": "ALM", "original_alarm_code": "ALM-2001106"}}, {"主题": "[提示告警]\"Process发生Monitor进程异常\"", "告警ID": "2002101", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.161.89", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.154.212)，对端地址=(other_process=10.200.126.215)", "可能原因": "未知", "metadata": {"filename": "5.2.17.12.3 ALM-2002101 Monitor进程异常.md", "section_number": "5.2.17.12.3", "alarm_type": "ALM", "original_alarm_code": "ALM-2002101"}}, {"主题": "[重要告警]\"Monitor发生Monitor与对端站点通信异常\"", "告警ID": "2002302", "告警级别": "重要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.199.196", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.41.73)，对端地址=(other_monitor=10.200.203.131)", "可能原因": "未知", "metadata": {"filename": "5.2.17.12.4 ALM-2002302 Monitor与对端站点通信异常.md", "section_number": "5.2.17.12.4", "alarm_type": "ALM", "original_alarm_code": "ALM-2002302"}}, {"主题": "[严重告警]\"Network发生站点网络状态异常\"", "告警ID": "2002501", "告警级别": "严重", "告警源": "Network", "来源系统": "ServiceOM10.200.52.206", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.90.240)，对端地址=(other_network=10.200.86.117)", "可能原因": "未知", "metadata": {"filename": "5.2.17.12.5 ALM-2002501 站点网络状态异常.md", "section_number": "5.2.17.12.5", "alarm_type": "ALM", "original_alarm_code": "ALM-2002501"}}, {"主题": "[提示告警]\"System发生第三方站点异常\"", "告警ID": "2001107", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.230.27", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.234.52)，对端地址=(other_system=10.200.179.71)", "可能原因": "未知", "metadata": {"filename": "5.2.17.12.6 ALM-2001107 第三方站点异常.md", "section_number": "5.2.17.12.6", "alarm_type": "ALM", "original_alarm_code": "ALM-2001107"}}, {"主题": "[提示信息]\"DMK\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.173.180", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.17.2 DMK.md", "section_number": "5.2.17.2", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[次要告警]\"Process发生dmk服务异常\"", "告警ID": "8000888", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.79.77", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.51.29)，对端地址=(other_process=10.200.25.185)", "可能原因": "未知", "metadata": {"filename": "5.2.17.2.1 ALM-8000888 dmk服务异常.md", "section_number": "5.2.17.2.1", "alarm_type": "ALM", "original_alarm_code": "ALM-8000888"}}, {"主题": "[提示信息]\"GaussDB\"", "告警ID": "", "告警级别": "提示", "告警源": "Database", "来源系统": "ManageOne10.200.19.153", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=management", "附加信息": "云服务=Database，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.17.3 GaussDB.md", "section_number": "5.2.17.3", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示告警]\"Database发生GaussdbHA服务进程异常\"", "告警ID": "1510000", "告警级别": "提示", "告警源": "Database", "来源系统": "ServiceOM10.200.240.218", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.40.115)，对端地址=(other_database=10.200.247.45)", "可能原因": "未知", "metadata": {"filename": "5.2.17.3.1 ALM-1510000 GaussdbHA服务进程异常.md", "section_number": "5.2.17.3.1", "alarm_type": "ALM", "original_alarm_code": "ALM-1510000"}}, {"主题": "[重要告警]\"Database发生GaussdbHA备份失败\"", "告警ID": "1510002", "告警级别": "重要", "告警源": "Database", "来源系统": "ServiceOM10.200.78.10", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.198.142)，对端地址=(other_database=10.200.6.175)", "可能原因": "未知", "metadata": {"filename": "5.2.17.3.2 ALM-1510002 GaussdbHA备份失败.md", "section_number": "5.2.17.3.2", "alarm_type": "ALM", "original_alarm_code": "ALM-1510002"}}, {"主题": "[严重告警]\"Database发生GaussdbHA证书异常\"", "告警ID": "1510003", "告警级别": "严重", "告警源": "Database", "来源系统": "ServiceOM10.200.248.170", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.106.143)，对端地址=(other_database=10.200.28.243)", "可能原因": "未知", "metadata": {"filename": "5.2.17.3.3 ALM-1510003 GaussdbHA证书异常.md", "section_number": "5.2.17.3.3", "alarm_type": "ALM", "original_alarm_code": "ALM-1510003"}}, {"主题": "[次要告警]\"Database发生GaussdbHA主备同步异常\"", "告警ID": "1510005", "告警级别": "次要", "告警源": "Database", "来源系统": "ServiceOM10.200.54.114", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.8.160)，对端地址=(other_database=10.200.214.41)", "可能原因": "未知", "metadata": {"filename": "5.2.17.3.4 ALM-1510005 GaussdbHA主备同步异常.md", "section_number": "5.2.17.3.4", "alarm_type": "ALM", "original_alarm_code": "ALM-1510005"}}, {"主题": "[次要告警]\"Database发生GaussdbHA上传远端备份服务器失败\"", "告警ID": "1510006", "告警级别": "次要", "告警源": "Database", "来源系统": "ServiceOM10.200.219.210", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.98.169)，对端地址=(other_database=10.200.251.58)", "可能原因": "未知", "metadata": {"filename": "5.2.17.3.5 ALM-1510006 GaussdbHA上传远端备份服务器失败.md", "section_number": "5.2.17.3.5", "alarm_type": "ALM", "original_alarm_code": "ALM-1510006"}}, {"主题": "[提示信息]\"LVS\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.62.108", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.17.4 LVS.md", "section_number": "5.2.17.4", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[重要告警]\"Process发生lvs_keepalived进程不存在\"", "告警ID": "2001101", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.255.154", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.240.104)，对端地址=(other_process=10.200.92.163)", "可能原因": "未知", "metadata": {"filename": "5.2.17.4.1 ALM-2001101 lvs_keepalived进程不存在.md", "section_number": "5.2.17.4.1", "alarm_type": "ALM", "original_alarm_code": "ALM-2001101"}}, {"主题": "[提示信息]\"Nginx\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.168.159", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.17.5 Nginx.md", "section_number": "5.2.17.5", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[次要告警]\"Process发生nginx服务异常\"", "告警ID": "2001002", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.193.66", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.226.184)，对端地址=(other_process=10.200.221.248)", "可能原因": "未知", "metadata": {"filename": "5.2.17.5.1 ALM-2001002 nginx服务异常.md", "section_number": "5.2.17.5.1", "alarm_type": "ALM", "original_alarm_code": "ALM-2001002"}}, {"主题": "[严重告警]\"System发生证书异常\"", "告警ID": "2001006", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.181.101", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.246.115)，对端地址=(other_system=10.200.182.115)", "可能原因": "未知", "metadata": {"filename": "5.2.17.5.2 ALM-2001006 证书异常.md", "section_number": "5.2.17.5.2", "alarm_type": "ALM", "original_alarm_code": "ALM-2001006"}}, {"主题": "[提示信息]\"DNS\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.168.30", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.17.6 DNS.md", "section_number": "5.2.17.6", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[重要告警]\"Process发生DNS named进程异常\"", "告警ID": "8000021", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.221.217", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.142.88)，对端地址=(other_process=10.200.135.232)", "可能原因": "未知", "metadata": {"filename": "5.2.17.6.1 ALM-8000021 DNS named进程异常.md", "section_number": "5.2.17.6.1", "alarm_type": "ALM", "original_alarm_code": "ALM-8000021"}}, {"主题": "[提示信息]\"NTP\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.90.115", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.17.7 NTP.md", "section_number": "5.2.17.7", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[重要告警]\"Monitor发生NTP时钟源偏移告警\"", "告警ID": "7700073", "告警级别": "重要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.53.170", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.90.166)，对端地址=(other_monitor=10.200.157.77)", "可能原因": "未知", "metadata": {"filename": "5.2.17.7.1 ALM-7700073 NTP时钟源偏移告警.md", "section_number": "5.2.17.7.1", "alarm_type": "ALM", "original_alarm_code": "ALM-7700073"}}, {"主题": "[提示告警]\"Process发生NTP进程异常\"", "告警ID": "7700071", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.17.217", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.172.17)，对端地址=(other_process=10.200.15.111)", "可能原因": "未知", "metadata": {"filename": "5.2.17.7.2 ALM-7700071 NTP进程异常.md", "section_number": "5.2.17.7.2", "alarm_type": "ALM", "original_alarm_code": "ALM-7700071"}}, {"主题": "[提示信息]\"HAProxy\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.125.50", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "******** HAProxy.md", "section_number": "********", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示告警]\"Process发生haproxy服务异常\"", "告警ID": "2000904", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.216.186", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(************)，对端地址=(other_process=**************)", "可能原因": "未知", "metadata": {"filename": "********.1 ALM-2000904 haproxy服务异常.md", "section_number": "********.1", "alarm_type": "ALM", "original_alarm_code": "ALM-2000904"}}, {"主题": "[严重告警]\"System发生haproxy的浮动IP不可达\"", "告警ID": "2000906", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.10.84", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(*************)，对端地址=(other_system=**************)", "可能原因": "未知", "metadata": {"filename": "********.2 ALM-2000906 haproxy的浮动IP不可达.md", "section_number": "********.2", "alarm_type": "ALM", "original_alarm_code": "ALM-2000906"}}, {"主题": "[次要告警]\"Process发生keepalived进程不存在\"", "告警ID": "2000908", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.82.193", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(**************)，对端地址=(other_process=**************)", "可能原因": "未知", "metadata": {"filename": "********.3 ALM-2000908 keepalived进程不存在.md", "section_number": "********.3", "alarm_type": "ALM", "original_alarm_code": "ALM-2000908"}}, {"主题": "[严重告警]\"System发生haproxy浮动IP端口不可达\"", "告警ID": "2000909", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.152.57", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(*************)，对端地址=(other_system=*************)", "可能原因": "未知", "metadata": {"filename": "********.4 ALM-2000909 haproxy浮动IP端口不可达.md", "section_number": "********.4", "alarm_type": "ALM", "original_alarm_code": "ALM-2000909"}}, {"主题": "[提示信息]\"TaskCenter\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.251.9", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "******** TaskCenter.md", "section_number": "********", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[严重告警]\"Process发生taskcenter服务异常\"", "告警ID": "2000722", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.92.200", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(*************)，对端地址=(other_process=**************)", "可能原因": "未知", "metadata": {"filename": "********.1 ALM-2000722 taskcenter服务异常.md", "section_number": "********.1", "alarm_type": "ALM", "original_alarm_code": "ALM-2000722"}}, {"主题": "[次要告警]\"System发生证书异常\"", "告警ID": "2000724", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.253.31", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.68.221)，对端地址=(other_system=10.200.112.41)", "可能原因": "未知", "metadata": {"filename": "********.2 ALM-2000724 证书异常.md", "section_number": "********.2", "alarm_type": "ALM", "original_alarm_code": "ALM-2000724"}}, {"主题": "[提示信息]\"APIGateway\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.201.190", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.19 APIGateway.md", "section_number": "5.2.19", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示信息]\"通信告警\"", "告警ID": "", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ManageOne10.200.118.23", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=management", "附加信息": "云服务=Monitor，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.19.1 通信告警.md", "section_number": "5.2.19.1", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示告警]\"Unknown发生\"", "告警ID": "48101-无法访问shubao", "告警级别": "提示", "告警源": "Unknown", "来源系统": "ServiceOM10.200.72.46", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.129.85)，对端地址=(other_unknown=10.200.146.26)", "可能原因": "未知", "metadata": {"filename": "5.2.19.1.1 ALM-48101-无法访问shubao.md", "section_number": "5.2.19.1.1", "alarm_type": "ALM", "original_alarm_code": "ALM-48101-无法访问shubao"}}, {"主题": "[严重告警]\"Unknown发生\"", "告警ID": "48118-无法访问redis-mgr", "告警级别": "严重", "告警源": "Unknown", "来源系统": "ServiceOM10.200.204.151", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.47.92)，对端地址=(other_unknown=10.200.23.167)", "可能原因": "未知", "metadata": {"filename": "5.2.19.1.10 ALM-48118-无法访问redis-mgr.md", "section_number": "5.2.19.1.10", "alarm_type": "ALM", "original_alarm_code": "ALM-48118-无法访问redis-mgr"}}, {"主题": "[次要告警]\"Unknown发生\"", "告警ID": "48102-无法访问etcd", "告警级别": "次要", "告警源": "Unknown", "来源系统": "ServiceOM10.200.134.99", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.135.104)，对端地址=(other_unknown=10.200.133.11)", "可能原因": "未知", "metadata": {"filename": "5.2.19.1.2 ALM-48102-无法访问etcd.md", "section_number": "5.2.19.1.2", "alarm_type": "ALM", "original_alarm_code": "ALM-48102-无法访问etcd"}}, {"主题": "[提示告警]\"Unknown发生\"", "告警ID": "48103-无法访问orchestration", "告警级别": "提示", "告警源": "Unknown", "来源系统": "ServiceOM10.200.109.188", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.248.32)，对端地址=(other_unknown=10.200.97.132)", "可能原因": "未知", "metadata": {"filename": "5.2.19.1.3 ALM-48103-无法访问orchestration.md", "section_number": "5.2.19.1.3", "alarm_type": "ALM", "original_alarm_code": "ALM-48103-无法访问orchestration"}}, {"主题": "[重要告警]\"Unknown发生\"", "告警ID": "48104-无法访问authadv", "告警级别": "重要", "告警源": "Unknown", "来源系统": "ServiceOM10.200.108.246", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.61.104)，对端地址=(other_unknown=10.200.62.102)", "可能原因": "未知", "metadata": {"filename": "5.2.19.1.4 ALM-48104-无法访问authadv.md", "section_number": "5.2.19.1.4", "alarm_type": "ALM", "original_alarm_code": "ALM-48104-无法访问authadv"}}, {"主题": "[严重告警]\"Unknown发生\"", "告警ID": "48107-无法访问FTP", "告警级别": "严重", "告警源": "Unknown", "来源系统": "ServiceOM10.200.100.253", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.61.227)，对端地址=(other_unknown=10.200.141.193)", "可能原因": "未知", "metadata": {"filename": "5.2.19.1.5 ALM-48107-无法访问FTP.md", "section_number": "5.2.19.1.5", "alarm_type": "ALM", "original_alarm_code": "ALM-48107-无法访问FTP"}}, {"主题": "[提示告警]\"Unknown发生\"", "告警ID": "48108-无法访问shubao-orchestration", "告警级别": "提示", "告警源": "Unknown", "来源系统": "ServiceOM10.200.65.249", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.87.207)，对端地址=(other_unknown=10.200.30.37)", "可能原因": "未知", "metadata": {"filename": "5.2.19.1.6 ALM-48108-无法访问shubao-orchestration.md", "section_number": "5.2.19.1.6", "alarm_type": "ALM", "original_alarm_code": "ALM-48108-无法访问shubao-orchestration"}}, {"主题": "[严重告警]\"Unknown发生\"", "告警ID": "48110-无法访问DB", "告警级别": "严重", "告警源": "Unknown", "来源系统": "ServiceOM10.200.4.228", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.114.28)，对端地址=(other_unknown=10.200.159.242)", "可能原因": "未知", "metadata": {"filename": "5.2.19.1.7 ALM-48110-无法访问DB.md", "section_number": "5.2.19.1.7", "alarm_type": "ALM", "original_alarm_code": "ALM-48110-无法访问DB"}}, {"主题": "[次要告警]\"Unknown发生\"", "告警ID": "48111-无法访问apimgr", "告警级别": "次要", "告警源": "Unknown", "来源系统": "ServiceOM10.200.72.246", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.191.138)，对端地址=(other_unknown=10.200.23.69)", "可能原因": "未知", "metadata": {"filename": "5.2.19.1.8 ALM-48111-无法访问apimgr.md", "section_number": "5.2.19.1.8", "alarm_type": "ALM", "original_alarm_code": "ALM-48111-无法访问apimgr"}}, {"主题": "[重要告警]\"Unknown发生\"", "告警ID": "48117-无法正常接收opsagent心跳", "告警级别": "重要", "告警源": "Unknown", "来源系统": "ServiceOM10.200.1.245", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.64.59)，对端地址=(other_unknown=10.200.191.52)", "可能原因": "未知", "metadata": {"filename": "5.2.19.1.9 ALM-48117-无法正常接收opsagent心跳.md", "section_number": "5.2.19.1.9", "alarm_type": "ALM", "original_alarm_code": "ALM-48117-无法正常接收opsagent心跳"}}, {"主题": "[提示信息]\"处理错误告警\"", "告警ID": "", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ManageOne10.200.59.243", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=management", "附加信息": "云服务=Monitor，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.19.2 处理错误告警.md", "section_number": "5.2.19.2", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[严重告警]\"Unknown发生\"", "告警ID": "48303-无法读写FTP文件", "告警级别": "严重", "告警源": "Unknown", "来源系统": "ServiceOM10.200.185.20", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.226.85)，对端地址=(other_unknown=10.200.220.23)", "可能原因": "未知", "metadata": {"filename": "5.2.19.2.1 ALM-48303-无法读写FTP文件.md", "section_number": "5.2.19.2.1", "alarm_type": "ALM", "original_alarm_code": "ALM-48303-无法读写FTP文件"}}, {"主题": "[次要告警]\"Unknown发生\"", "告警ID": "48304-证书即将过期", "告警级别": "次要", "告警源": "Unknown", "来源系统": "ServiceOM10.200.147.172", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.60.178)，对端地址=(other_unknown=10.200.13.199)", "可能原因": "未知", "metadata": {"filename": "5.2.19.2.2 ALM-48304-证书即将过期.md", "section_number": "5.2.19.2.2", "alarm_type": "ALM", "original_alarm_code": "ALM-48304-证书即将过期"}}, {"主题": "[次要告警]\"Unknown发生\"", "告警ID": "48305-证书已经过期", "告警级别": "次要", "告警源": "Unknown", "来源系统": "ServiceOM10.200.162.186", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.5.141)，对端地址=(other_unknown=10.200.47.113)", "可能原因": "未知", "metadata": {"filename": "5.2.19.2.3 ALM-48305-证书已经过期.md", "section_number": "5.2.19.2.3", "alarm_type": "ALM", "original_alarm_code": "ALM-48305-证书已经过期"}}, {"主题": "[次要告警]\"Unknown发生\"", "告警ID": "48306-证书校验失败", "告警级别": "次要", "告警源": "Unknown", "来源系统": "ServiceOM10.200.175.168", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.236.24)，对端地址=(other_unknown=10.200.171.104)", "可能原因": "未知", "metadata": {"filename": "5.2.19.2.4 ALM-48306-证书校验失败.md", "section_number": "5.2.19.2.4", "alarm_type": "ALM", "original_alarm_code": "ALM-48306-证书校验失败"}}, {"主题": "[重要告警]\"Unknown发生\"", "告警ID": "48316-请求超出单实例流控阈值", "告警级别": "重要", "告警源": "Unknown", "来源系统": "ServiceOM10.200.117.42", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.229.95)，对端地址=(other_unknown=10.200.94.93)", "可能原因": "未知", "metadata": {"filename": "5.2.19.2.5 ALM-48316-请求超出单实例流控阈值.md", "section_number": "5.2.19.2.5", "alarm_type": "ALM", "original_alarm_code": "ALM-48316-请求超出单实例流控阈值"}}, {"主题": "[重要告警]\"Unknown发生\"", "告警ID": "48317-重新加载LB失败", "告警级别": "重要", "告警源": "Unknown", "来源系统": "ServiceOM10.200.197.54", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.103.15)，对端地址=(other_unknown=10.200.91.242)", "可能原因": "未知", "metadata": {"filename": "5.2.19.2.6 ALM-48317-重新加载LB失败.md", "section_number": "5.2.19.2.6", "alarm_type": "ALM", "original_alarm_code": "ALM-48317-重新加载LB失败"}}, {"主题": "[提示告警]\"Unknown发生\"", "告警ID": "48318-证书回退失败", "告警级别": "提示", "告警源": "Unknown", "来源系统": "ServiceOM10.200.178.177", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.85.113)，对端地址=(other_unknown=10.200.14.194)", "可能原因": "未知", "metadata": {"filename": "5.2.19.2.7 ALM-48318-证书回退失败.md", "section_number": "5.2.19.2.7", "alarm_type": "ALM", "original_alarm_code": "ALM-48318-证书回退失败"}}, {"主题": "[提示告警]\"Unknown发生\"", "告警ID": "48319-证书无法生效", "告警级别": "提示", "告警源": "Unknown", "来源系统": "ServiceOM10.200.21.43", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.78.37)，对端地址=(other_unknown=10.200.122.31)", "可能原因": "未知", "metadata": {"filename": "5.2.19.2.8 ALM-48319-证书无法生效.md", "section_number": "5.2.19.2.8", "alarm_type": "ALM", "original_alarm_code": "ALM-48319-证书无法生效"}}, {"主题": "[次要告警]\"Unknown发生\"", "告警ID": "48320-证书即将过期", "告警级别": "次要", "告警源": "Unknown", "来源系统": "ServiceOM10.200.41.37", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.252.53)，对端地址=(other_unknown=10.200.194.237)", "可能原因": "未知", "metadata": {"filename": "5.2.19.2.9 ALM-48320-证书即将过期.md", "section_number": "5.2.19.2.9", "alarm_type": "ALM", "original_alarm_code": "ALM-48320-证书即将过期"}}, {"主题": "[提示信息]\"设备告警\"", "告警ID": "", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ManageOne10.200.128.216", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=management", "附加信息": "云服务=Monitor，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.19.3 设备告警.md", "section_number": "5.2.19.3", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示告警]\"Unknown发生\"", "告警ID": "48401-进程未启动", "告警级别": "提示", "告警源": "Unknown", "来源系统": "ServiceOM10.200.150.50", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.246.6)，对端地址=(other_unknown=10.200.194.214)", "可能原因": "未知", "metadata": {"filename": "5.2.19.3.1 ALM-48401-进程未启动.md", "section_number": "5.2.19.3.1", "alarm_type": "ALM", "original_alarm_code": "ALM-48401-进程未启动"}}, {"主题": "[提示告警]\"Unknown发生\"", "告警ID": "48402-端口冲突", "告警级别": "提示", "告警源": "Unknown", "来源系统": "ServiceOM10.200.214.15", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.167.78)，对端地址=(other_unknown=10.200.152.154)", "可能原因": "未知", "metadata": {"filename": "5.2.19.3.2 ALM-48402-端口冲突.md", "section_number": "5.2.19.3.2", "alarm_type": "ALM", "original_alarm_code": "ALM-48402-端口冲突"}}, {"主题": "[提示告警]\"Unknown发生\"", "告警ID": "48409-文件句柄使用率超过门限值", "告警级别": "提示", "告警源": "Unknown", "来源系统": "ServiceOM10.200.78.154", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.210.98)，对端地址=(other_unknown=10.200.55.2)", "可能原因": "未知", "metadata": {"filename": "5.2.19.3.3 ALM-48409-文件句柄使用率超过门限值.md", "section_number": "5.2.19.3.3", "alarm_type": "ALM", "original_alarm_code": "ALM-48409-文件句柄使用率超过门限值"}}, {"主题": "[提示信息]\"FusionSphere OpenStack告警参考\"", "告警ID": "", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ManageOne10.200.147.177", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=management", "附加信息": "云服务=Monitor，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "******* FusionSphere OpenStack告警参考.md", "section_number": "*******", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示告警]\"Process发生上传日志到OBS服务失败\"", "告警ID": "6008", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.168.136", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.48.111)，对端地址=(other_process=10.200.240.115)", "可能原因": "未知", "metadata": {"filename": "*******.1 ALM-6008 上传日志到OBS服务失败.md", "section_number": "*******.1", "alarm_type": "ALM", "original_alarm_code": "ALM-6008"}}, {"主题": "[重要告警]\"Network发生主机网口状态异常\"", "告警ID": "6021", "告警级别": "重要", "告警源": "Network", "来源系统": "ServiceOM10.200.138.57", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.36.196)，对端地址=(other_network=10.200.125.176)", "可能原因": "未知", "metadata": {"filename": "*******.10 ALM-6021 主机网口状态异常.md", "section_number": "*******.10", "alarm_type": "ALM", "original_alarm_code": "ALM-6021"}}, {"主题": "[重要告警]\"Process发生裸金属服务器审计告警\"", "告警ID": "1126002", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.225.149", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.107.196)，对端地址=(other_process=10.200.42.184)", "可能原因": "未知", "metadata": {"filename": "*******.100 ALM-1126002 裸金属服务器审计告警.md", "section_number": "*******.100", "alarm_type": "ALM", "original_alarm_code": "ALM-1126002"}}, {"主题": "[严重告警]\"Process发生DHCP服务不可用\"", "告警ID": "1200067", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.57.23", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.84.107)，对端地址=(other_process=10.200.204.127)", "可能原因": "未知", "metadata": {"filename": "*******.101 ALM-1200067 DHCP服务不可用.md", "section_number": "*******.101", "alarm_type": "ALM", "original_alarm_code": "ALM-1200067"}}, {"主题": "[重要告警]\"Network发生主机设备端口错包告警\"", "告警ID": "1200075", "告警级别": "重要", "告警源": "Network", "来源系统": "ServiceOM10.200.125.212", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.82.106)，对端地址=(other_network=10.200.143.13)", "可能原因": "未知", "metadata": {"filename": "*******.102 ALM-1200075 主机设备端口错包告警.md", "section_number": "*******.102", "alarm_type": "ALM", "original_alarm_code": "ALM-1200075"}}, {"主题": "[次要告警]\"Network发生主机设备端口丢包告警\"", "告警ID": "1200076", "告警级别": "次要", "告警源": "Network", "来源系统": "ServiceOM10.200.153.36", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.112.236)，对端地址=(other_network=10.200.246.76)", "可能原因": "未知", "metadata": {"filename": "*******.103 ALM-1200076 主机设备端口丢包告警.md", "section_number": "*******.103", "alarm_type": "ALM", "original_alarm_code": "ALM-1200076"}}, {"主题": "[重要告警]\"System发生Dpdk lacp bond聚合失败\"", "告警ID": "1200077", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.58.226", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.139.164)，对端地址=(other_system=10.200.148.193)", "可能原因": "未知", "metadata": {"filename": "*******.104 ALM-1200077 Dpdk lacp bond聚合失败.md", "section_number": "*******.104", "alarm_type": "ALM", "original_alarm_code": "ALM-1200077"}}, {"主题": "[次要告警]\"ELB发生负载均衡器后端实例不在线\"", "告警ID": "1223017", "告警级别": "次要", "告警源": "ELB", "来源系统": "ServiceOM10.200.172.234", "定位信息": "区域=SH_CSVW，云服务=ELB，节点类型=mgt", "附加信息": "云服务=ELB，服务=mgt，本端地址=(10.200.96.79)，对端地址=(other_elb=10.200.103.175)", "可能原因": "未知", "metadata": {"filename": "*******.105 ALM-1223017 负载均衡器后端实例不在线.md", "section_number": "*******.105", "alarm_type": "ALM", "original_alarm_code": "ALM-1223017"}}, {"主题": "[严重告警]\"System发生FusionSphere Neutron和Agile Controller-DCN 进行数据一致性修\"", "告警ID": "1240001", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.151.211", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.87.177)，对端地址=(other_system=10.200.50.52)", "可能原因": "未知", "metadata": {"filename": "*******.106 ALM-1240001 FusionSphere Neutron和Agile Controller-DCN 进行数据一致性修.md", "section_number": "*******.106", "alarm_type": "ALM", "original_alarm_code": "ALM-1240001"}}, {"主题": "[严重告警]\"Network发生FusionSphere Neutron和Agile Controller-DCN HTTP连接故障\"", "告警ID": "1240002", "告警级别": "严重", "告警源": "Network", "来源系统": "ServiceOM10.200.97.189", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.53.197)，对端地址=(other_network=10.200.182.102)", "可能原因": "未知", "metadata": {"filename": "*******.107 ALM-1240002 FusionSphere Neutron和Agile Controller-DCN HTTP连接故障.md", "section_number": "*******.107", "alarm_type": "ALM", "original_alarm_code": "ALM-1240002"}}, {"主题": "[重要告警]\"System发生FusionSphere Neutron和Agile Controller-DCN Websocke\"", "告警ID": "1240003", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.24.227", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.242.12)，对端地址=(other_system=10.200.119.29)", "可能原因": "未知", "metadata": {"filename": "*******.108 ALM-1240003 FusionSphere Neutron和Agile Controller-DCN Websocke.md", "section_number": "*******.108", "alarm_type": "ALM", "original_alarm_code": "ALM-1240003"}}, {"主题": "[严重告警]\"Monitor发生EVS_OVS-DPDK 转发能力达到瓶颈告警\"", "告警ID": "1301021", "告警级别": "严重", "告警源": "Monitor", "来源系统": "ServiceOM10.200.193.219", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.80.23)，对端地址=(other_monitor=10.200.151.175)", "可能原因": "未知", "metadata": {"filename": "*******.109 ALM-1301021 EVS_OVS-DPDK 转发能力达到瓶颈告警.md", "section_number": "*******.109", "alarm_type": "ALM", "original_alarm_code": "ALM-1301021"}}, {"主题": "[次要告警]\"Network发生主机与NTP服务器心跳状态异常\"", "告警ID": "6022", "告警级别": "次要", "告警源": "Network", "来源系统": "ServiceOM10.200.120.12", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.99.105)，对端地址=(other_network=10.200.109.17)", "可能原因": "未知", "metadata": {"filename": "*******.11 ALM-6022 主机与NTP服务器心跳状态异常.md", "section_number": "*******.11", "alarm_type": "ALM", "original_alarm_code": "ALM-6022"}}, {"主题": "[重要告警]\"Process发生OpenStack服务包升级完成后未提交\"", "告警ID": "1316000", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.77.163", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.95.170)，对端地址=(other_process=10.200.219.112)", "可能原因": "未知", "metadata": {"filename": "*******.110 ALM-1316000 OpenStack服务包升级完成后未提交.md", "section_number": "*******.110", "alarm_type": "ALM", "original_alarm_code": "ALM-1316000"}}, {"主题": "[重要告警]\"System发生忽略升级的单板未处理\"", "告警ID": "1316001", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.207.42", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.171.92)，对端地址=(other_system=10.200.207.46)", "可能原因": "未知", "metadata": {"filename": "*******.111 ALM-1316001 忽略升级的单板未处理.md", "section_number": "*******.111", "alarm_type": "ALM", "original_alarm_code": "ALM-1316001"}}, {"主题": "[提示告警]\"Monitor发生热补丁运行异常告警\"", "告警ID": "1316002", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.2.187", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.64.24)，对端地址=(other_monitor=10.200.216.78)", "可能原因": "未知", "metadata": {"filename": "*******.112 ALM-1316002 热补丁运行异常告警.md", "section_number": "*******.112", "alarm_type": "ALM", "original_alarm_code": "ALM-1316002"}}, {"主题": "[重要告警]\"Monitor发生QEMU版本异常告警\"", "告警ID": "1316003", "告警级别": "重要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.132.10", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.191.179)，对端地址=(other_monitor=10.200.129.214)", "可能原因": "未知", "metadata": {"filename": "*******.113 ALM-1316003 QEMU版本异常告警.md", "section_number": "*******.113", "alarm_type": "ALM", "original_alarm_code": "ALM-1316003"}}, {"主题": "[严重告警]\"System发生RabbitMQ资源使用率超过阈值\"", "告警ID": "1507002", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.78.10", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.59.200)，对端地址=(other_system=10.200.204.243)", "可能原因": "未知", "metadata": {"filename": "*******.114 ALM-1507002 RabbitMQ资源使用率超过阈值.md", "section_number": "*******.114", "alarm_type": "ALM", "original_alarm_code": "ALM-1507002"}}, {"主题": "[重要告警]\"Network发生主机存储链路中断\"", "告警ID": "6023", "告警级别": "重要", "告警源": "Network", "来源系统": "ServiceOM10.200.255.108", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.120.21)，对端地址=(other_network=10.200.189.244)", "可能原因": "未知", "metadata": {"filename": "*******.12 ALM-6023 主机存储链路中断.md", "section_number": "*******.12", "alarm_type": "ALM", "original_alarm_code": "ALM-6023"}}, {"主题": "[提示告警]\"Storage发生存储资源管理链路中断或认证失败\"", "告警ID": "6024", "告警级别": "提示", "告警源": "Storage", "来源系统": "ServiceOM10.200.107.28", "定位信息": "区域=SH_CSVW，云服务=Storage，节点类型=mgt", "附加信息": "云服务=Storage，服务=mgt，本端地址=(10.200.133.2)，对端地址=(other_storage=10.200.27.93)", "可能原因": "未知", "metadata": {"filename": "*******.13 ALM-6024 存储资源管理链路中断或认证失败.md", "section_number": "*******.13", "alarm_type": "ALM", "original_alarm_code": "ALM-6024"}}, {"主题": "[次要告警]\"Storage发生存储使用率超过阈值\"", "告警ID": "6025", "告警级别": "次要", "告警源": "Storage", "来源系统": "ServiceOM10.200.203.172", "定位信息": "区域=SH_CSVW，云服务=Storage，节点类型=mgt", "附加信息": "云服务=Storage，服务=mgt，本端地址=(10.200.146.146)，对端地址=(other_storage=10.200.214.33)", "可能原因": "未知", "metadata": {"filename": "*******.14 ALM-6025 存储使用率超过阈值.md", "section_number": "*******.14", "alarm_type": "ALM", "original_alarm_code": "ALM-6025"}}, {"主题": "[重要告警]\"Network发生主机光纤通道中断\"", "告警ID": "6026", "告警级别": "重要", "告警源": "Network", "来源系统": "ServiceOM10.200.10.185", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.109.17)，对端地址=(other_network=10.200.7.78)", "可能原因": "未知", "metadata": {"filename": "*******.15 ALM-6026 主机光纤通道中断.md", "section_number": "*******.15", "alarm_type": "ALM", "original_alarm_code": "ALM-6026"}}, {"主题": "[提示告警]\"Process发生网口自协商速率没有达到服务器网口最大速率的一半\"", "告警ID": "6027", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.244.171", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.6.221)，对端地址=(other_process=10.200.225.73)", "可能原因": "未知", "metadata": {"filename": "*******.16 ALM-6027 网口自协商速率没有达到服务器网口最大速率的一半.md", "section_number": "*******.16", "alarm_type": "ALM", "original_alarm_code": "ALM-6027"}}, {"主题": "[严重告警]\"Process发生本地NTP客户端与本地NTP服务器时间差超过60秒\"", "告警ID": "6028", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.11.163", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.217.13)，对端地址=(other_process=10.200.251.132)", "可能原因": "未知", "metadata": {"filename": "*******.17 ALM-6028 本地NTP客户端与本地NTP服务器时间差超过60秒.md", "section_number": "*******.17", "alarm_type": "ALM", "original_alarm_code": "ALM-6028"}}, {"主题": "[重要告警]\"Process发生服务自动备份失败\"", "告警ID": "6029", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.217.38", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(***********)，对端地址=(other_process=***********)", "可能原因": "未知", "metadata": {"filename": "*******.18 ALM-6029 服务自动备份失败.md", "section_number": "*******.18", "alarm_type": "ALM", "original_alarm_code": "ALM-6029"}}, {"主题": "[严重告警]\"System发生IP冲突故障\"", "告警ID": "6030", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.123.170", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(*************)，对端地址=(other_system=**************)", "可能原因": "未知", "metadata": {"filename": "*******.19 ALM-6030 IP冲突故障.md", "section_number": "*******.19", "alarm_type": "ALM", "original_alarm_code": "ALM-6030"}}, {"主题": "[提示告警]\"Process发生NTP服务器与外部时钟源时间差超过阈值\"", "告警ID": "6010", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.99.130", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(************)，对端地址=(other_process=**************)", "可能原因": "未知", "metadata": {"filename": "*******.2 ALM-6010 NTP服务器与外部时钟源时间差超过阈值.md", "section_number": "*******.2", "alarm_type": "ALM", "original_alarm_code": "ALM-6010"}}, {"主题": "[提示告警]\"Process发生上传日志到FTP服务器失败\"", "告警ID": "6031", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.178.128", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.182.183)，对端地址=(other_process=10.200.220.127)", "可能原因": "未知", "metadata": {"filename": "*******.20 ALM-6031 上传日志到FTP服务器失败.md", "section_number": "*******.20", "alarm_type": "ALM", "original_alarm_code": "ALM-6031"}}, {"主题": "[提示告警]\"Process发生I层服务CPU占用率超过阈值\"", "告警ID": "6033", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.207.191", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.163.173)，对端地址=(other_process=10.200.69.73)", "可能原因": "未知", "metadata": {"filename": "*******.21 ALM-6033 I层服务CPU占用率超过阈值.md", "section_number": "*******.21", "alarm_type": "ALM", "original_alarm_code": "ALM-6033"}}, {"主题": "[重要告警]\"Process发生I层服务内存占用率超过阈值\"", "告警ID": "6034", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.152.38", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.149.115)，对端地址=(other_process=10.200.57.245)", "可能原因": "未知", "metadata": {"filename": "*******.22 ALM-6034 I层服务内存占用率超过阈值.md", "section_number": "*******.22", "alarm_type": "ALM", "original_alarm_code": "ALM-6034"}}, {"主题": "[次要告警]\"Compute发生虚拟机CPU占用率超过阈值\"", "告警ID": "6036", "告警级别": "次要", "告警源": "Compute", "来源系统": "ServiceOM10.200.182.50", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.17.231)，对端地址=(other_compute=10.200.249.73)", "可能原因": "未知", "metadata": {"filename": "*******.23 ALM-6036 虚拟机CPU占用率超过阈值.md", "section_number": "*******.23", "alarm_type": "ALM", "original_alarm_code": "ALM-6036"}}, {"主题": "[次要告警]\"Compute发生虚拟机虚拟内存_Swap分区占用率超过阈值\"", "告警ID": "6037", "告警级别": "次要", "告警源": "Compute", "来源系统": "ServiceOM10.200.245.58", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.226.157)，对端地址=(other_compute=10.200.137.17)", "可能原因": "未知", "metadata": {"filename": "*******.24 ALM-6037 虚拟机虚拟内存_Swap分区占用率超过阈值.md", "section_number": "*******.24", "alarm_type": "ALM", "original_alarm_code": "ALM-6037"}}, {"主题": "[提示告警]\"Network发生主机磁盘占用率超过阈值\"", "告警ID": "6038", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.34.173", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.48.89)，对端地址=(other_network=10.200.100.101)", "可能原因": "未知", "metadata": {"filename": "*******.25 ALM-6038 主机磁盘占用率超过阈值.md", "section_number": "*******.25", "alarm_type": "ALM", "original_alarm_code": "ALM-6038"}}, {"主题": "[提示告警]\"Network发生主机虚拟化域内存占用率超过阈值\"", "告警ID": "6039", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.209.111", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.208.180)，对端地址=(other_network=10.200.53.11)", "可能原因": "未知", "metadata": {"filename": "*******.26 ALM-6039 主机虚拟化域内存占用率超过阈值.md", "section_number": "*******.26", "alarm_type": "ALM", "original_alarm_code": "ALM-6039"}}, {"主题": "[次要告警]\"Compute发生虚拟机审计告警\"", "告警ID": "70100", "告警级别": "次要", "告警源": "Compute", "来源系统": "ServiceOM10.200.26.50", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.210.254)，对端地址=(other_compute=10.200.207.139)", "可能原因": "未知", "metadata": {"filename": "*******.27 ALM-70100 虚拟机审计告警.md", "section_number": "*******.27", "alarm_type": "ALM", "original_alarm_code": "ALM-70100"}}, {"主题": "[重要告警]\"Compute发生虚拟机操作系统故障告警\"", "告警ID": "70101", "告警级别": "重要", "告警源": "Compute", "来源系统": "ServiceOM10.200.232.19", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.86.112)，对端地址=(other_compute=10.200.196.190)", "可能原因": "未知", "metadata": {"filename": "*******.28 ALM-70101 虚拟机操作系统故障告警.md", "section_number": "*******.28", "alarm_type": "ALM", "original_alarm_code": "ALM-70101"}}, {"主题": "[次要告警]\"Compute发生虚拟机ERROR状态告警\"", "告警ID": "70102", "告警级别": "次要", "告警源": "Compute", "来源系统": "ServiceOM10.200.186.19", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.244.186)，对端地址=(other_compute=10.200.184.163)", "可能原因": "未知", "metadata": {"filename": "*******.29 ALM-70102 虚拟机ERROR状态告警.md", "section_number": "*******.29", "alarm_type": "ALM", "original_alarm_code": "ALM-70102"}}, {"主题": "[严重告警]\"Network发生DNS服务器连接中断\"", "告警ID": "6014", "告警级别": "严重", "告警源": "Network", "来源系统": "ServiceOM10.200.36.241", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.76.139)，对端地址=(other_network=10.200.216.90)", "可能原因": "未知", "metadata": {"filename": "*******.3 ALM-6014 DNS服务器连接中断.md", "section_number": "*******.3", "alarm_type": "ALM", "original_alarm_code": "ALM-6014"}}, {"主题": "[次要告警]\"Network发生主机VCPU使用数目超过限制\"", "告警ID": "70103", "告警级别": "次要", "告警源": "Network", "来源系统": "ServiceOM10.200.134.42", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.2.151)，对端地址=(other_network=10.200.47.95)", "可能原因": "未知", "metadata": {"filename": "*******.30 ALM-70103 主机VCPU使用数目超过限制.md", "section_number": "*******.30", "alarm_type": "ALM", "original_alarm_code": "ALM-70103"}}, {"主题": "[重要告警]\"Network发生主机内存使用超过主机总内存\"", "告警ID": "70104", "告警级别": "重要", "告警源": "Network", "来源系统": "ServiceOM10.200.36.229", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.178.43)，对端地址=(other_network=10.200.231.142)", "可能原因": "未知", "metadata": {"filename": "*******.31 ALM-70104 主机内存使用超过主机总内存.md", "section_number": "*******.31", "alarm_type": "ALM", "original_alarm_code": "ALM-70104"}}, {"主题": "[次要告警]\"Compute发生异构VMware虚拟机卷数据残留告警\"", "告警ID": "70105", "告警级别": "次要", "告警源": "Compute", "来源系统": "ServiceOM10.200.10.170", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.184.197)，对端地址=(other_compute=10.200.141.243)", "可能原因": "未知", "metadata": {"filename": "*******.32 ALM-70105 异构VMware虚拟机卷数据残留告警.md", "section_number": "*******.32", "alarm_type": "ALM", "original_alarm_code": "ALM-70105"}}, {"主题": "[重要告警]\"Compute发生虚拟机HA中间态告警\"", "告警ID": "70106", "告警级别": "重要", "告警源": "Compute", "来源系统": "ServiceOM10.200.171.97", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.53.57)，对端地址=(other_compute=10.200.64.11)", "可能原因": "未知", "metadata": {"filename": "*******.33 ALM-70106 虚拟机HA中间态告警.md", "section_number": "*******.33", "alarm_type": "ALM", "original_alarm_code": "ALM-70106"}}, {"主题": "[重要告警]\"Compute发生虚拟机目录文件异常\"", "告警ID": "70108", "告警级别": "重要", "告警源": "Compute", "来源系统": "ServiceOM10.200.65.112", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.255.41)，对端地址=(other_compute=10.200.231.146)", "可能原因": "未知", "metadata": {"filename": "*******.34 ALM-70108 虚拟机目录文件异常.md", "section_number": "*******.34", "alarm_type": "ALM", "original_alarm_code": "ALM-70108"}}, {"主题": "[提示告警]\"Compute发生虚拟机BDM残留审计告警\"", "告警ID": "70109", "告警级别": "提示", "告警源": "Compute", "来源系统": "ServiceOM10.200.119.167", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.184.141)，对端地址=(other_compute=10.200.106.200)", "可能原因": "未知", "metadata": {"filename": "*******.35 ALM-70109 虚拟机BDM残留审计告警.md", "section_number": "*******.35", "alarm_type": "ALM", "original_alarm_code": "ALM-70109"}}, {"主题": "[次要告警]\"Monitor发生本地盘ERROR状态告警\"", "告警ID": "70111", "告警级别": "次要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.222.101", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.115.193)，对端地址=(other_monitor=10.200.187.88)", "可能原因": "未知", "metadata": {"filename": "*******.36 ALM-70111 本地盘ERROR状态告警.md", "section_number": "*******.36", "alarm_type": "ALM", "original_alarm_code": "ALM-70111"}}, {"主题": "[严重告警]\"System发生IB网卡故障\"", "告警ID": "70112", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.18.133", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.35.212)，对端地址=(other_system=10.200.212.235)", "可能原因": "未知", "metadata": {"filename": "*******.37 ALM-70112 IB网卡故障.md", "section_number": "*******.37", "alarm_type": "ALM", "original_alarm_code": "ALM-70112"}}, {"主题": "[严重告警]\"Compute发生NVME SSD盘或卡故障\"", "告警ID": "70113", "告警级别": "严重", "告警源": "Compute", "来源系统": "ServiceOM10.200.27.130", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.190.237)，对端地址=(other_compute=10.200.122.205)", "可能原因": "未知", "metadata": {"filename": "*******.38 ALM-70113 NVME SSD盘或卡故障.md", "section_number": "*******.38", "alarm_type": "ALM", "original_alarm_code": "ALM-70113"}}, {"主题": "[严重告警]\"Compute发生异构VMware野虚拟机残留告警\"", "告警ID": "70126", "告警级别": "严重", "告警源": "Compute", "来源系统": "ServiceOM10.200.12.252", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.249.10)，对端地址=(other_compute=10.200.219.72)", "可能原因": "未知", "metadata": {"filename": "*******.39 ALM-70126 异构VMware野虚拟机残留告警.md", "section_number": "*******.39", "alarm_type": "ALM", "original_alarm_code": "ALM-70126"}}, {"主题": "[重要告警]\"Network发生NTP服务器与外部时钟源网络故障或外部时钟源故障\"", "告警ID": "6015", "告警级别": "重要", "告警源": "Network", "来源系统": "ServiceOM10.200.248.80", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.106.160)，对端地址=(other_network=10.200.236.60)", "可能原因": "未知", "metadata": {"filename": "*******.4 ALM-6015 NTP服务器与外部时钟源网络故障或外部时钟源故障.md", "section_number": "*******.4", "alarm_type": "ALM", "original_alarm_code": "ALM-6015"}}, {"主题": "[严重告警]\"Network发生主机组内NVMe SSD大盘使用率超过阈值\"", "告警ID": "70127", "告警级别": "严重", "告警源": "Network", "来源系统": "ServiceOM10.200.115.226", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.120.251)，对端地址=(other_network=10.200.85.26)", "可能原因": "未知", "metadata": {"filename": "*******.40 ALM-70127 主机组内NVMe SSD大盘使用率超过阈值.md", "section_number": "*******.40", "alarm_type": "ALM", "original_alarm_code": "ALM-70127"}}, {"主题": "[重要告警]\"Network发生主机组内NVMe SSD小盘使用率超过阈值\"", "告警ID": "70128", "告警级别": "重要", "告警源": "Network", "来源系统": "ServiceOM10.200.240.48", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.187.198)，对端地址=(other_network=10.200.144.138)", "可能原因": "未知", "metadata": {"filename": "*******.41 ALM-70128 主机组内NVMe SSD小盘使用率超过阈值.md", "section_number": "*******.41", "alarm_type": "ALM", "original_alarm_code": "ALM-70128"}}, {"主题": "[严重告警]\"Network发生主机组内GPU使用率超过阈值\"", "告警ID": "70129", "告警级别": "严重", "告警源": "Network", "来源系统": "ServiceOM10.200.141.12", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.210.150)，对端地址=(other_network=10.200.144.235)", "可能原因": "未知", "metadata": {"filename": "*******.42 ALM-70129 主机组内GPU使用率超过阈值.md", "section_number": "*******.42", "alarm_type": "ALM", "original_alarm_code": "ALM-70129"}}, {"主题": "[重要告警]\"Compute发生虚拟机HA失败\"", "告警ID": "70130", "告警级别": "重要", "告警源": "Compute", "来源系统": "ServiceOM10.200.63.8", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.48.42)，对端地址=(other_compute=10.200.6.220)", "可能原因": "未知", "metadata": {"filename": "*******.43 ALM-70130 虚拟机HA失败.md", "section_number": "*******.43", "alarm_type": "ALM", "original_alarm_code": "ALM-70130"}}, {"主题": "[次要告警]\"Network发生主机组CPU分配率超过阈值\"", "告警ID": "70131", "告警级别": "次要", "告警源": "Network", "来源系统": "ServiceOM10.200.205.92", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.116.130)，对端地址=(other_network=10.200.217.69)", "可能原因": "未知", "metadata": {"filename": "*******.44 ALM-70131 主机组CPU分配率超过阈值.md", "section_number": "*******.44", "alarm_type": "ALM", "original_alarm_code": "ALM-70131"}}, {"主题": "[严重告警]\"Network发生主机组内存分配率超过阈值\"", "告警ID": "70132", "告警级别": "严重", "告警源": "Network", "来源系统": "ServiceOM10.200.9.24", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.63.245)，对端地址=(other_network=10.200.149.70)", "可能原因": "未知", "metadata": {"filename": "*******.45 ALM-70132 主机组内存分配率超过阈值.md", "section_number": "*******.45", "alarm_type": "ALM", "original_alarm_code": "ALM-70132"}}, {"主题": "[严重告警]\"Monitor发生内存复用率超过阈值告警\"", "告警ID": "70135", "告警级别": "严重", "告警源": "Monitor", "来源系统": "ServiceOM10.200.105.67", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.21.65)，对端地址=(other_monitor=10.200.145.66)", "可能原因": "未知", "metadata": {"filename": "*******.46 ALM-70135 内存复用率超过阈值告警.md", "section_number": "*******.46", "alarm_type": "ALM", "original_alarm_code": "ALM-70135"}}, {"主题": "[严重告警]\"Network发生DHCP-agent管理的network数量超过阈值\"", "告警ID": "70201", "告警级别": "严重", "告警源": "Network", "来源系统": "ServiceOM10.200.248.247", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.154.248)，对端地址=(other_network=10.200.249.174)", "可能原因": "未知", "metadata": {"filename": "*******.47 ALM-70201 DHCP-agent管理的network数量超过阈值.md", "section_number": "*******.47", "alarm_type": "ALM", "original_alarm_code": "ALM-70201"}}, {"主题": "[重要告警]\"Network发生主机上虚拟端口上线处理异常\"", "告警ID": "70203", "告警级别": "重要", "告警源": "Network", "来源系统": "ServiceOM10.200.146.188", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.112.172)，对端地址=(other_network=10.200.109.63)", "可能原因": "未知", "metadata": {"filename": "*******.48 ALM-70203 主机上虚拟端口上线处理异常.md", "section_number": "*******.48", "alarm_type": "ALM", "original_alarm_code": "ALM-70203"}}, {"主题": "[提示告警]\"System发生聚合网口状态异常\"", "告警ID": "70251", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.230.52", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.223.139)，对端地址=(other_system=10.200.31.243)", "可能原因": "未知", "metadata": {"filename": "*******.49 ALM-70251 聚合网口状态异常.md", "section_number": "*******.49", "alarm_type": "ALM", "original_alarm_code": "ALM-70251"}}, {"主题": "[提示告警]\"Network发生日志服务连接OBS失败\"", "告警ID": "6016", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.108.30", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.24.198)，对端地址=(other_network=10.200.142.33)", "可能原因": "未知", "metadata": {"filename": "*******.5 ALM-6016 日志服务连接OBS失败.md", "section_number": "*******.5", "alarm_type": "ALM", "original_alarm_code": "ALM-6016"}}, {"主题": "[严重告警]\"Monitor发生卷审计告警\"", "告警ID": "70300", "告警级别": "严重", "告警源": "Monitor", "来源系统": "ServiceOM10.200.33.120", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.128.16)，对端地址=(other_monitor=10.200.29.70)", "可能原因": "未知", "metadata": {"filename": "*******.50 ALM-70300 卷审计告警.md", "section_number": "*******.50", "alarm_type": "ALM", "original_alarm_code": "ALM-70300"}}, {"主题": "[严重告警]\"Monitor发生快照审计告警\"", "告警ID": "70310", "告警级别": "严重", "告警源": "Monitor", "来源系统": "ServiceOM10.200.97.42", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.54.42)，对端地址=(other_monitor=10.200.231.195)", "可能原因": "未知", "metadata": {"filename": "*******.51 ALM-70310 快照审计告警.md", "section_number": "*******.51", "alarm_type": "ALM", "original_alarm_code": "ALM-70310"}}, {"主题": "[严重告警]\"Monitor发生镜像审计告警\"", "告警ID": "70400", "告警级别": "严重", "告警源": "Monitor", "来源系统": "ServiceOM10.200.4.91", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.86.238)，对端地址=(other_monitor=10.200.73.139)", "可能原因": "未知", "metadata": {"filename": "*******.52 ALM-70400 镜像审计告警.md", "section_number": "*******.52", "alarm_type": "ALM", "original_alarm_code": "ALM-70400"}}, {"主题": "[严重告警]\"System发生镜像完整性校验失败\"", "告警ID": "70401", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.180.115", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.244.212)，对端地址=(other_system=10.200.218.124)", "可能原因": "未知", "metadata": {"filename": "*******.53 ALM-70401 镜像完整性校验失败.md", "section_number": "*******.53", "alarm_type": "ALM", "original_alarm_code": "ALM-70401"}}, {"主题": "[严重告警]\"System发生整机快照残留\"", "告警ID": "70402", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.194.20", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.44.241)，对端地址=(other_system=10.200.163.2)", "可能原因": "未知", "metadata": {"filename": "*******.54 ALM-70402 整机快照残留.md", "section_number": "*******.54", "alarm_type": "ALM", "original_alarm_code": "ALM-70402"}}, {"主题": "[提示告警]\"Network发生虚拟网络资源审计告警\"", "告警ID": "73008", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.45.174", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.86.129)，对端地址=(other_network=10.200.122.213)", "可能原因": "未知", "metadata": {"filename": "*******.55 ALM-73008 虚拟网络资源审计告警.md", "section_number": "*******.55", "alarm_type": "ALM", "original_alarm_code": "ALM-73008"}}, {"主题": "[严重告警]\"Monitor发生文件系统故障告警\"", "告警ID": "73010", "告警级别": "严重", "告警源": "Monitor", "来源系统": "ServiceOM10.200.77.130", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.143.252)，对端地址=(other_monitor=10.200.55.97)", "可能原因": "未知", "metadata": {"filename": "*******.56 ALM-73010 文件系统故障告警.md", "section_number": "*******.56", "alarm_type": "ALM", "original_alarm_code": "ALM-73010"}}, {"主题": "[重要告警]\"Process发生关键进程异常告警\"", "告警ID": "73011", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.84.96", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.72.59)，对端地址=(other_process=10.200.104.185)", "可能原因": "未知", "metadata": {"filename": "*******.57 ALM-73011 关键进程异常告警.md", "section_number": "*******.57", "alarm_type": "ALM", "original_alarm_code": "ALM-73011"}}, {"主题": "[重要告警]\"Storage发生磁盘分区inode资源不足告警\"", "告警ID": "73012", "告警级别": "重要", "告警源": "Storage", "来源系统": "ServiceOM10.200.45.82", "定位信息": "区域=SH_CSVW，云服务=Storage，节点类型=mgt", "附加信息": "云服务=Storage，服务=mgt，本端地址=(10.200.205.216)，对端地址=(other_storage=10.200.100.101)", "可能原因": "未知", "metadata": {"filename": "*******.58 ALM-73012 磁盘分区inode资源不足告警.md", "section_number": "*******.58", "alarm_type": "ALM", "original_alarm_code": "ALM-73012"}}, {"主题": "[次要告警]\"Storage发生存储磁盘I_O时延过大告警\"", "告警ID": "73013", "告警级别": "次要", "告警源": "Storage", "来源系统": "ServiceOM10.200.173.175", "定位信息": "区域=SH_CSVW，云服务=Storage，节点类型=mgt", "附加信息": "云服务=Storage，服务=mgt，本端地址=(10.200.135.255)，对端地址=(other_storage=10.200.184.100)", "可能原因": "未知", "metadata": {"filename": "*******.59 ALM-73013 存储磁盘I_O时延过大告警.md", "section_number": "*******.59", "alarm_type": "ALM", "original_alarm_code": "ALM-73013"}}, {"主题": "[重要告警]\"Network发生主机状态异常\"", "告警ID": "6017", "告警级别": "重要", "告警源": "Network", "来源系统": "ServiceOM10.200.111.182", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.106.65)，对端地址=(other_network=10.200.51.189)", "可能原因": "未知", "metadata": {"filename": "*******.6 ALM-6017 主机状态异常.md", "section_number": "*******.6", "alarm_type": "ALM", "original_alarm_code": "ALM-6017"}}, {"主题": "[重要告警]\"System发生系统文件完整性异常\"", "告警ID": "73014", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.157.54", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.150.205)，对端地址=(other_system=10.200.175.181)", "可能原因": "未知", "metadata": {"filename": "*******.60 ALM-73014 系统文件完整性异常.md", "section_number": "*******.60", "alarm_type": "ALM", "original_alarm_code": "ALM-73014"}}, {"主题": "[严重告警]\"System发生大页内存不足\"", "告警ID": "73015", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.160.147", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.104.127)，对端地址=(other_system=10.200.20.80)", "可能原因": "未知", "metadata": {"filename": "*******.61 ALM-73015 大页内存不足.md", "section_number": "*******.61", "alarm_type": "ALM", "original_alarm_code": "ALM-73015"}}, {"主题": "[重要告警]\"System发生CPU主频异常\"", "告警ID": "73016", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.22.121", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.70.33)，对端地址=(other_system=10.200.161.39)", "可能原因": "未知", "metadata": {"filename": "*******.62 ALM-73016 CPU主频异常.md", "section_number": "*******.62", "alarm_type": "ALM", "original_alarm_code": "ALM-73016"}}, {"主题": "[严重告警]\"Network发生主机系统用户密码过期预警\"", "告警ID": "73017", "告警级别": "严重", "告警源": "Network", "来源系统": "ServiceOM10.200.12.83", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.198.113)，对端地址=(other_network=10.200.169.126)", "可能原因": "未知", "metadata": {"filename": "*******.63 ALM-73017 主机系统用户密码过期预警.md", "section_number": "*******.63", "alarm_type": "ALM", "original_alarm_code": "ALM-73017"}}, {"主题": "[重要告警]\"System发生swap分区I_O时延过大\"", "告警ID": "73018", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.233.142", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.61.154)，对端地址=(other_system=10.200.44.68)", "可能原因": "未知", "metadata": {"filename": "*******.64 ALM-73018 swap分区I_O时延过大.md", "section_number": "*******.64", "alarm_type": "ALM", "original_alarm_code": "ALM-73018"}}, {"主题": "[重要告警]\"Network发生主机进程数异常\"", "告警ID": "73019", "告警级别": "重要", "告警源": "Network", "来源系统": "ServiceOM10.200.197.121", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.156.101)，对端地址=(other_network=10.200.224.225)", "可能原因": "未知", "metadata": {"filename": "*******.65 ALM-73019 主机进程数异常.md", "section_number": "*******.65", "alarm_type": "ALM", "original_alarm_code": "ALM-73019"}}, {"主题": "[重要告警]\"Network发生虚拟网络端口错包率超过告警阈值告警\"", "告警ID": "73102", "告警级别": "重要", "告警源": "Network", "来源系统": "ServiceOM10.200.42.78", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.110.240)，对端地址=(other_network=10.200.88.28)", "可能原因": "未知", "metadata": {"filename": "*******.66 ALM-73102 虚拟网络端口错包率超过告警阈值告警.md", "section_number": "*******.66", "alarm_type": "ALM", "original_alarm_code": "ALM-73102"}}, {"主题": "[严重告警]\"System发生OVS_EVS卡死\"", "告警ID": "73104", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.224.96", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.74.88)，对端地址=(other_system=10.200.248.237)", "可能原因": "未知", "metadata": {"filename": "*******.67 ALM-73104 OVS_EVS卡死.md", "section_number": "*******.67", "alarm_type": "ALM", "original_alarm_code": "ALM-73104"}}, {"主题": "[提示告警]\"Compute发生虚拟机发生反复重启故障\"", "告警ID": "73107", "告警级别": "提示", "告警源": "Compute", "来源系统": "ServiceOM10.200.23.129", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.150.76)，对端地址=(other_compute=10.200.230.134)", "可能原因": "未知", "metadata": {"filename": "*******.68 ALM-73107 虚拟机发生反复重启故障.md", "section_number": "*******.68", "alarm_type": "ALM", "original_alarm_code": "ALM-73107"}}, {"主题": "[严重告警]\"Compute发生虚拟机Watchdog异常告警\"", "告警ID": "73108", "告警级别": "严重", "告警源": "Compute", "来源系统": "ServiceOM10.200.228.4", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.58.236)，对端地址=(other_compute=10.200.43.229)", "可能原因": "未知", "metadata": {"filename": "*******.69 ALM-73108 虚拟机Watchdog异常告警.md", "section_number": "*******.69", "alarm_type": "ALM", "original_alarm_code": "ALM-73108"}}, {"主题": "[重要告警]\"Network发生主机CPU占用率超过阈值\"", "告警ID": "6018", "告警级别": "重要", "告警源": "Network", "来源系统": "ServiceOM10.200.206.102", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.25.175)，对端地址=(other_network=10.200.158.58)", "可能原因": "未知", "metadata": {"filename": "*******.7 ALM-6018 主机CPU占用率超过阈值.md", "section_number": "*******.7", "alarm_type": "ALM", "original_alarm_code": "ALM-6018"}}, {"主题": "[重要告警]\"Process发生UVP关键进程内存占用率超过阈值告警\"", "告警ID": "73109", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.222.242", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.243.229)，对端地址=(other_process=10.200.57.107)", "可能原因": "未知", "metadata": {"filename": "*******.70 ALM-73109 UVP关键进程内存占用率超过阈值告警.md", "section_number": "*******.70", "alarm_type": "ALM", "original_alarm_code": "ALM-73109"}}, {"主题": "[重要告警]\"Network发生虚拟网络关键资源不足\"", "告警ID": "73110", "告警级别": "重要", "告警源": "Network", "来源系统": "ServiceOM10.200.62.96", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.217.7)，对端地址=(other_network=10.200.178.238)", "可能原因": "未知", "metadata": {"filename": "*******.71 ALM-73110 虚拟网络关键资源不足.md", "section_number": "*******.71", "alarm_type": "ALM", "original_alarm_code": "ALM-73110"}}, {"主题": "[次要告警]\"Network发生主机连接跟踪表超过阈值告警\"", "告警ID": "73111", "告警级别": "次要", "告警源": "Network", "来源系统": "ServiceOM10.200.104.75", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.175.224)，对端地址=(other_network=10.200.225.118)", "可能原因": "未知", "metadata": {"filename": "*******.72 ALM-73111 主机连接跟踪表超过阈值告警.md", "section_number": "*******.72", "alarm_type": "ALM", "original_alarm_code": "ALM-73111"}}, {"主题": "[重要告警]\"Compute发生虚拟机网卡异常\"", "告警ID": "73112", "告警级别": "重要", "告警源": "Compute", "来源系统": "ServiceOM10.200.138.215", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.187.199)，对端地址=(other_compute=10.200.4.154)", "可能原因": "未知", "metadata": {"filename": "*******.73 ALM-73112 虚拟机网卡异常.md", "section_number": "*******.73", "alarm_type": "ALM", "original_alarm_code": "ALM-73112"}}, {"主题": "[次要告警]\"Process发生HAProxy代理服务不可用\"", "告警ID": "73201", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.38.7", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.252.127)，对端地址=(other_process=10.200.216.175)", "可能原因": "未知", "metadata": {"filename": "*******.74 ALM-73201 HAProxy代理服务不可用.md", "section_number": "*******.74", "alarm_type": "ALM", "original_alarm_code": "ALM-73201"}}, {"主题": "[重要告警]\"System发生组件故障\"", "告警ID": "73203", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.218.115", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.127.120)，对端地址=(other_system=10.200.162.250)", "可能原因": "未知", "metadata": {"filename": "*******.75 ALM-73203 组件故障.md", "section_number": "*******.75", "alarm_type": "ALM", "original_alarm_code": "ALM-73203"}}, {"主题": "[严重告警]\"Network发生主机版本不一致告警\"", "告警ID": "73204", "告警级别": "严重", "告警源": "Network", "来源系统": "ServiceOM10.200.188.49", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.11.6)，对端地址=(other_network=10.200.113.67)", "可能原因": "未知", "metadata": {"filename": "*******.76 ALM-73204 主机版本不一致告警.md", "section_number": "*******.76", "alarm_type": "ALM", "original_alarm_code": "ALM-73204"}}, {"主题": "[重要告警]\"Monitor发生Iaas层资源配置不一致告警\"", "告警ID": "73205", "告警级别": "重要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.121.195", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.186.46)，对端地址=(other_monitor=10.200.238.52)", "可能原因": "未知", "metadata": {"filename": "*******.77 ALM-73205 Iaas层资源配置不一致告警.md", "section_number": "*******.77", "alarm_type": "ALM", "original_alarm_code": "ALM-73205"}}, {"主题": "[次要告警]\"Network发生主机名重复\"", "告警ID": "73207", "告警级别": "次要", "告警源": "Network", "来源系统": "ServiceOM10.200.191.10", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.220.146)，对端地址=(other_network=10.200.191.157)", "可能原因": "未知", "metadata": {"filename": "*******.78 ALM-73207 主机名重复.md", "section_number": "*******.78", "alarm_type": "ALM", "original_alarm_code": "ALM-73207"}}, {"主题": "[提示告警]\"Process发生对接外部仲裁服务异常\"", "告警ID": "73208", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.231.115", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.119.174)，对端地址=(other_process=10.200.18.10)", "可能原因": "未知", "metadata": {"filename": "*******.79 ALM-73208 对接外部仲裁服务异常.md", "section_number": "*******.79", "alarm_type": "ALM", "original_alarm_code": "ALM-73208"}}, {"主题": "[提示告警]\"Network发生主机内存占用率超过阈值\"", "告警ID": "6019", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.64.226", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.66.3)，对端地址=(other_network=10.200.50.142)", "可能原因": "未知", "metadata": {"filename": "*******.8 ALM-6019 主机内存占用率超过阈值.md", "section_number": "*******.8", "alarm_type": "ALM", "original_alarm_code": "ALM-6019"}}, {"主题": "[提示告警]\"Monitor发生zookeeper健康检查告警\"", "告警ID": "73209", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.182.98", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.115.241)，对端地址=(other_monitor=10.200.126.158)", "可能原因": "未知", "metadata": {"filename": "*******.80 ALM-73209 zookeeper健康检查告警.md", "section_number": "*******.80", "alarm_type": "ALM", "original_alarm_code": "ALM-73209"}}, {"主题": "[提示告警]\"Process发生未配置管理数据备份至第三方服务器\"", "告警ID": "73210", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.236.218", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.68.201)，对端地址=(other_process=10.200.139.131)", "可能原因": "未知", "metadata": {"filename": "*******.81 ALM-73210 未配置管理数据备份至第三方服务器.md", "section_number": "*******.81", "alarm_type": "ALM", "original_alarm_code": "ALM-73210"}}, {"主题": "[严重告警]\"Network发生vCenter连接失败\"", "告警ID": "73301", "告警级别": "严重", "告警源": "Network", "来源系统": "ServiceOM10.200.93.59", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.244.214)，对端地址=(other_network=10.200.37.2)", "可能原因": "未知", "metadata": {"filename": "*******.82 ALM-73301 vCenter连接失败.md", "section_number": "*******.82", "alarm_type": "ALM", "original_alarm_code": "ALM-73301"}}, {"主题": "[提示告警]\"Network发生存在网络未配置VLAN探测平面\"", "告警ID": "73302", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.228.36", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.220.187)，对端地址=(other_network=10.200.132.107)", "可能原因": "未知", "metadata": {"filename": "*******.83 ALM-73302 存在网络未配置VLAN探测平面.md", "section_number": "*******.83", "alarm_type": "ALM", "original_alarm_code": "ALM-73302"}}, {"主题": "[重要告警]\"Network发生VRM服务器连接失败\"", "告警ID": "73303", "告警级别": "重要", "告警源": "Network", "来源系统": "ServiceOM10.200.192.172", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.230.98)，对端地址=(other_network=10.200.90.236)", "可能原因": "未知", "metadata": {"filename": "*******.84 ALM-73303 VRM服务器连接失败.md", "section_number": "*******.84", "alarm_type": "ALM", "original_alarm_code": "ALM-73303"}}, {"主题": "[重要告警]\"Process发生rabbitmq服务故障\"", "告警ID": "73401", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.56.244", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.141.47)，对端地址=(other_process=10.200.141.30)", "可能原因": "未知", "metadata": {"filename": "*******.85 ALM-73401 rabbitmq服务故障.md", "section_number": "*******.85", "alarm_type": "ALM", "original_alarm_code": "ALM-73401"}}, {"主题": "[提示告警]\"Database发生gaussdb主备数据不同步\"", "告警ID": "73403", "告警级别": "提示", "告警源": "Database", "来源系统": "ServiceOM10.200.50.79", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.44.251)，对端地址=(other_database=10.200.51.98)", "可能原因": "未知", "metadata": {"filename": "*******.86 ALM-73403 gaussdb主备数据不同步.md", "section_number": "*******.86", "alarm_type": "ALM", "original_alarm_code": "ALM-73403"}}, {"主题": "[重要告警]\"Database发生检测到gaussdb恶意访问\"", "告警ID": "73404", "告警级别": "重要", "告警源": "Database", "来源系统": "ServiceOM10.200.37.60", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.110.198)，对端地址=(other_database=10.200.76.222)", "可能原因": "未知", "metadata": {"filename": "*******.87 ALM-73404 检测到gaussdb恶意访问.md", "section_number": "*******.87", "alarm_type": "ALM", "original_alarm_code": "ALM-73404"}}, {"主题": "[重要告警]\"Database发生gaussdb连接数超过阈值\"", "告警ID": "73405", "告警级别": "重要", "告警源": "Database", "来源系统": "ServiceOM10.200.175.187", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.231.77)，对端地址=(other_database=10.200.40.108)", "可能原因": "未知", "metadata": {"filename": "*******.88 ALM-73405 gaussdb连接数超过阈值.md", "section_number": "*******.88", "alarm_type": "ALM", "original_alarm_code": "ALM-73405"}}, {"主题": "[次要告警]\"Process发生UVP关键进程CPU占用率超过阈值\"", "告警ID": "73410", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.146.45", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.235.108)，对端地址=(other_process=10.200.70.58)", "可能原因": "未知", "metadata": {"filename": "*******.89 ALM-73410 UVP关键进程CPU占用率超过阈值.md", "section_number": "*******.89", "alarm_type": "ALM", "original_alarm_code": "ALM-73410"}}, {"主题": "[重要告警]\"Network发生主机逻辑磁盘占用率超过阈值\"", "告警ID": "6020", "告警级别": "重要", "告警源": "Network", "来源系统": "ServiceOM10.200.52.235", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.241.131)，对端地址=(other_network=10.200.16.87)", "可能原因": "未知", "metadata": {"filename": "*******.9 ALM-6020 主机逻辑磁盘占用率超过阈值.md", "section_number": "*******.9", "alarm_type": "ALM", "original_alarm_code": "ALM-6020"}}, {"主题": "[重要告警]\"Database发生数据库文件损坏\"", "告警ID": "73411", "告警级别": "重要", "告警源": "Database", "来源系统": "ServiceOM10.200.34.239", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.206.68)，对端地址=(other_database=10.200.14.140)", "可能原因": "未知", "metadata": {"filename": "*******.90 ALM-73411 数据库文件损坏.md", "section_number": "*******.90", "alarm_type": "ALM", "original_alarm_code": "ALM-73411"}}, {"主题": "[重要告警]\"Database发生检测到gaussdb存在长事务\"", "告警ID": "73412", "告警级别": "重要", "告警源": "Database", "来源系统": "ServiceOM10.200.149.184", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.163.114)，对端地址=(other_database=10.200.141.152)", "可能原因": "未知", "metadata": {"filename": "*******.91 ALM-73412 检测到gaussdb存在长事务.md", "section_number": "*******.91", "alarm_type": "ALM", "original_alarm_code": "ALM-73412"}}, {"主题": "[重要告警]\"Storage发生后端存储虚拟容量使用率超过阈值\"", "告警ID": "1060047", "告警级别": "重要", "告警源": "Storage", "来源系统": "ServiceOM10.200.223.34", "定位信息": "区域=SH_CSVW，云服务=Storage，节点类型=mgt", "附加信息": "云服务=Storage，服务=mgt，本端地址=(10.200.172.38)，对端地址=(other_storage=10.200.91.42)", "可能原因": "未知", "metadata": {"filename": "*******.92 ALM-1060047 后端存储虚拟容量使用率超过阈值.md", "section_number": "*******.92", "alarm_type": "ALM", "original_alarm_code": "ALM-1060047"}}, {"主题": "[次要告警]\"Monitor发生挂载双活卷单边故障告警\"", "告警ID": "1060049", "告警级别": "次要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.105.141", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.182.73)，对端地址=(other_monitor=10.200.19.50)", "可能原因": "未知", "metadata": {"filename": "*******.93 ALM-1060049 挂载双活卷单边故障告警.md", "section_number": "*******.93", "alarm_type": "ALM", "original_alarm_code": "ALM-1060049"}}, {"主题": "[重要告警]\"Storage发生后端存储证书异常\"", "告警ID": "1060050", "告警级别": "重要", "告警源": "Storage", "来源系统": "ServiceOM10.200.15.108", "定位信息": "区域=SH_CSVW，云服务=Storage，节点类型=mgt", "附加信息": "云服务=Storage，服务=mgt，本端地址=(10.200.94.47)，对端地址=(other_storage=10.200.44.202)", "可能原因": "未知", "metadata": {"filename": "*******.94 ALM-1060050 后端存储证书异常.md", "section_number": "*******.94", "alarm_type": "ALM", "original_alarm_code": "ALM-1060050"}}, {"主题": "[次要告警]\"Network发生主机组内本地直通盘使用率超过阈值\"", "告警ID": "1101315", "告警级别": "次要", "告警源": "Network", "来源系统": "ServiceOM10.200.255.17", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.12.168)，对端地址=(other_network=10.200.222.137)", "可能原因": "未知", "metadata": {"filename": "*******.95 ALM-1101315 主机组内本地直通盘使用率超过阈值.md", "section_number": "*******.95", "alarm_type": "ALM", "original_alarm_code": "ALM-1101315"}}, {"主题": "[重要告警]\"System发生NPU使用率超过阈值\"", "告警ID": "1101320", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.23.171", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.48.15)，对端地址=(other_system=10.200.159.2)", "可能原因": "未知", "metadata": {"filename": "*******.96 ALM-1101320 NPU使用率超过阈值.md", "section_number": "*******.96", "alarm_type": "ALM", "original_alarm_code": "ALM-1101320"}}, {"主题": "[严重告警]\"Storage发生虚拟机存储链路未完全恢复\"", "告警ID": "1101321", "告警级别": "严重", "告警源": "Storage", "来源系统": "ServiceOM10.200.9.240", "定位信息": "区域=SH_CSVW，云服务=Storage，节点类型=mgt", "附加信息": "云服务=Storage，服务=mgt，本端地址=(10.200.81.146)，对端地址=(other_storage=10.200.79.229)", "可能原因": "未知", "metadata": {"filename": "*******.97 ALM-1101321 虚拟机存储链路未完全恢复.md", "section_number": "*******.97", "alarm_type": "ALM", "original_alarm_code": "ALM-1101321"}}, {"主题": "[提示告警]\"Process发生裸金属服务器电源状态未获取到\"", "告警ID": "1126000", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.145.165", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.220.25)，对端地址=(other_process=10.200.234.146)", "可能原因": "未知", "metadata": {"filename": "*******.98 ALM-1126000 裸金属服务器电源状态未获取到.md", "section_number": "*******.98", "alarm_type": "ALM", "original_alarm_code": "ALM-1126000"}}, {"主题": "[严重告警]\"Process发生裸金属服务器管理状态不可用\"", "告警ID": "1126001", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.134.21", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.129.130)，对端地址=(other_process=10.200.50.194)", "可能原因": "未知", "metadata": {"filename": "*******.99 ALM-1126001 裸金属服务器管理状态不可用.md", "section_number": "*******.99", "alarm_type": "ALM", "original_alarm_code": "ALM-1126001"}}, {"主题": "[提示信息]\"Service OM告警参考\"", "告警ID": "", "告警级别": "提示", "告警源": "Process", "来源系统": "ManageOne10.200.224.166", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=management", "附加信息": "云服务=Process，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.3.2 Service OM告警参考.md", "section_number": "5.2.3.2", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[重要告警]\"Network发生Service OM与SNMP管理站连接异常\"", "告警ID": "9002", "告警级别": "重要", "告警源": "Network", "来源系统": "ServiceOM10.200.95.198", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.107.88)，对端地址=(other_network=10.200.127.4)", "可能原因": "未知", "metadata": {"filename": "5.2.3.2.1 ALM-9002 Service OM与SNMP管理站连接异常.md", "section_number": "5.2.3.2.1", "alarm_type": "ALM", "original_alarm_code": "ALM-9002"}}, {"主题": "[严重告警]\"Storage发生系统磁盘使用率过大\"", "告警ID": "9215", "告警级别": "严重", "告警源": "Storage", "来源系统": "ServiceOM10.200.51.103", "定位信息": "区域=SH_CSVW，云服务=Storage，节点类型=mgt", "附加信息": "云服务=Storage，服务=mgt，本端地址=(10.200.81.38)，对端地址=(other_storage=10.200.225.100)", "可能原因": "未知", "metadata": {"filename": "5.2.3.2.10 ALM-9215 系统磁盘使用率过大.md", "section_number": "5.2.3.2.10", "alarm_type": "ALM", "original_alarm_code": "ALM-9215"}}, {"主题": "[重要告警]\"System发生软件订阅与保障年费即将到期\"", "告警ID": "9216", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.67.30", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.48.140)，对端地址=(other_system=10.200.119.91)", "可能原因": "未知", "metadata": {"filename": "5.2.3.2.11 ALM-9216 软件订阅与保障年费即将到期.md", "section_number": "5.2.3.2.11", "alarm_type": "ALM", "original_alarm_code": "ALM-9216"}}, {"主题": "[重要告警]\"System发生软件订阅与保障年费已经过期\"", "告警ID": "9217", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.9.161", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.118.105)，对端地址=(other_system=10.200.238.197)", "可能原因": "未知", "metadata": {"filename": "5.2.3.2.12 ALM-9217 软件订阅与保障年费已经过期.md", "section_number": "5.2.3.2.12", "alarm_type": "ALM", "original_alarm_code": "ALM-9217"}}, {"主题": "[重要告警]\"Process发生Service OM主备倒换功能被禁用\"", "告警ID": "9226", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.64.75", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.150.48)，对端地址=(other_process=10.200.13.12)", "可能原因": "未知", "metadata": {"filename": "5.2.3.2.13 ALM-9226 Service OM主备倒换功能被禁用.md", "section_number": "5.2.3.2.13", "alarm_type": "ALM", "original_alarm_code": "ALM-9226"}}, {"主题": "[提示告警]\"Process发生Service OM资源异常\"", "告警ID": "9801", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.21.17", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.131.187)，对端地址=(other_process=10.200.241.112)", "可能原因": "未知", "metadata": {"filename": "5.2.3.2.14 ALM-9801 Service OM资源异常.md", "section_number": "5.2.3.2.14", "alarm_type": "ALM", "original_alarm_code": "ALM-9801"}}, {"主题": "[严重告警]\"Network发生Service OM与内部部件连接异常\"", "告警ID": "9803", "告警级别": "严重", "告警源": "Network", "来源系统": "ServiceOM10.200.53.38", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.147.53)，对端地址=(other_network=10.200.254.114)", "可能原因": "未知", "metadata": {"filename": "5.2.3.2.15 ALM-9803 Service OM与内部部件连接异常.md", "section_number": "5.2.3.2.15", "alarm_type": "ALM", "original_alarm_code": "ALM-9803"}}, {"主题": "[严重告警]\"Process发生Service OM双机倒换\"", "告警ID": "9901", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.196.32", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.179.95)，对端地址=(other_process=10.200.85.130)", "可能原因": "未知", "metadata": {"filename": "5.2.3.2.16 ALM-9901 Service OM双机倒换.md", "section_number": "5.2.3.2.16", "alarm_type": "ALM", "original_alarm_code": "ALM-9901"}}, {"主题": "[提示告警]\"Process发生Service OM双机心跳中断\"", "告警ID": "9902", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.59.188", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.248.61)，对端地址=(other_process=10.200.59.22)", "可能原因": "未知", "metadata": {"filename": "5.2.3.2.17 ALM-9902 Service OM双机心跳中断.md", "section_number": "5.2.3.2.17", "alarm_type": "ALM", "original_alarm_code": "ALM-9902"}}, {"主题": "[次要告警]\"Process发生Service OM双机文件同步失败\"", "告警ID": "9903", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.88.120", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.7.172)，对端地址=(other_process=10.200.178.22)", "可能原因": "未知", "metadata": {"filename": "5.2.3.2.18 ALM-9903 Service OM双机文件同步失败.md", "section_number": "5.2.3.2.18", "alarm_type": "ALM", "original_alarm_code": "ALM-9903"}}, {"主题": "[严重告警]\"System发生与License Server通信异常\"", "告警ID": "9911", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.216.35", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.122.97)，对端地址=(other_system=10.200.21.214)", "可能原因": "未知", "metadata": {"filename": "5.2.3.2.19 ALM-9911 与License Server通信异常.md", "section_number": "5.2.3.2.19", "alarm_type": "ALM", "original_alarm_code": "ALM-9911"}}, {"主题": "[严重告警]\"Process发生Service OM与上级时间服务器同步时间失败\"", "告警ID": "9201", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.124.233", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.148.145)，对端地址=(other_process=10.200.22.150)", "可能原因": "未知", "metadata": {"filename": "5.2.3.2.2 ALM-9201 Service OM与上级时间服务器同步时间失败.md", "section_number": "5.2.3.2.2", "alarm_type": "ALM", "original_alarm_code": "ALM-9201"}}, {"主题": "[严重告警]\"Monitor发生未配置OpenStack告警上报\"", "告警ID": "9912", "告警级别": "严重", "告警源": "Monitor", "来源系统": "ServiceOM10.200.62.114", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.216.129)，对端地址=(other_monitor=10.200.66.187)", "可能原因": "未知", "metadata": {"filename": "5.2.3.2.20 ALM-9912 未配置OpenStack告警上报.md", "section_number": "5.2.3.2.20", "alarm_type": "ALM", "original_alarm_code": "ALM-9912"}}, {"主题": "[严重告警]\"System发生系统默认证书使用提醒\"", "告警ID": "9913", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.105.131", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.176.8)，对端地址=(other_system=10.200.229.148)", "可能原因": "未知", "metadata": {"filename": "5.2.3.2.21 ALM-9913 系统默认证书使用提醒.md", "section_number": "5.2.3.2.21", "alarm_type": "ALM", "original_alarm_code": "ALM-9913"}}, {"主题": "[严重告警]\"System发生证书过期预警\"", "告警ID": "9915", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.190.237", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.75.146)，对端地址=(other_system=10.200.81.147)", "可能原因": "未知", "metadata": {"filename": "5.2.3.2.22 ALM-9915 证书过期预警.md", "section_number": "5.2.3.2.22", "alarm_type": "ALM", "original_alarm_code": "ALM-9915"}}, {"主题": "[重要告警]\"Process发生OMM-Server服务异常\"", "告警ID": "9916", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.94.218", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.226.199)，对端地址=(other_process=10.200.73.183)", "可能原因": "未知", "metadata": {"filename": "5.2.3.2.23 ALM-9916 OMM-Server服务异常.md", "section_number": "5.2.3.2.23", "alarm_type": "ALM", "original_alarm_code": "ALM-9916"}}, {"主题": "[严重告警]\"Process发生Service OM虚拟机CPU占用率超过阈值\"", "告警ID": "9917", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.249.228", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.49.19)，对端地址=(other_process=10.200.250.37)", "可能原因": "未知", "metadata": {"filename": "5.2.3.2.24 ALM-9917 Service OM虚拟机CPU占用率超过阈值.md", "section_number": "5.2.3.2.24", "alarm_type": "ALM", "original_alarm_code": "ALM-9917"}}, {"主题": "[次要告警]\"Process发生Service OM虚拟机内存占用率超过阈值\"", "告警ID": "9918", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.44.137", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.28.11)，对端地址=(other_process=10.200.112.92)", "可能原因": "未知", "metadata": {"filename": "5.2.3.2.25 ALM-9918 Service OM虚拟机内存占用率超过阈值.md", "section_number": "5.2.3.2.25", "alarm_type": "ALM", "original_alarm_code": "ALM-9918"}}, {"主题": "[严重告警]\"Process发生Service OM服务器时间被修改\"", "告警ID": "9203", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.198.78", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.165.149)，对端地址=(other_process=10.200.202.25)", "可能原因": "未知", "metadata": {"filename": "5.2.3.2.3 ALM-9203 Service OM服务器时间被修改.md", "section_number": "5.2.3.2.3", "alarm_type": "ALM", "original_alarm_code": "ALM-9203"}}, {"主题": "[重要告警]\"Process发生Service OM与上级时间服务器时间差异过大\"", "告警ID": "9204", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.87.36", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.185.193)，对端地址=(other_process=10.200.129.20)", "可能原因": "未知", "metadata": {"filename": "5.2.3.2.4 ALM-9204 Service OM与上级时间服务器时间差异过大.md", "section_number": "5.2.3.2.4", "alarm_type": "ALM", "original_alarm_code": "ALM-9204"}}, {"主题": "[提示告警]\"Process发生Service OM数据备份失败\"", "告警ID": "9206", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.159.133", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.203.17)，对端地址=(other_process=10.200.224.157)", "可能原因": "未知", "metadata": {"filename": "5.2.3.2.5 ALM-9206 Service OM数据备份失败.md", "section_number": "5.2.3.2.5", "alarm_type": "ALM", "original_alarm_code": "ALM-9206"}}, {"主题": "[提示告警]\"System发生License即将过期\"", "告警ID": "9207", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.39.152", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.50.179)，对端地址=(other_system=10.200.58.122)", "可能原因": "未知", "metadata": {"filename": "5.2.3.2.6 ALM-9207 License即将过期.md", "section_number": "5.2.3.2.6", "alarm_type": "ALM", "original_alarm_code": "ALM-9207"}}, {"主题": "[严重告警]\"System发生License已经过期\"", "告警ID": "9208", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.199.204", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.200.244)，对端地址=(other_system=10.200.120.17)", "可能原因": "未知", "metadata": {"filename": "5.2.3.2.7 ALM-9208 License已经过期.md", "section_number": "5.2.3.2.7", "alarm_type": "ALM", "original_alarm_code": "ALM-9208"}}, {"主题": "[提示告警]\"System发生当前资源数量大于License许可上限\"", "告警ID": "9209", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.144.169", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.2.12)，对端地址=(other_system=10.200.180.162)", "可能原因": "未知", "metadata": {"filename": "5.2.3.2.8 ALM-9209 当前资源数量大于License许可上限.md", "section_number": "5.2.3.2.8", "alarm_type": "ALM", "original_alarm_code": "ALM-9209"}}, {"主题": "[严重告警]\"System发生当前License已失效\"", "告警ID": "9210", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.146.224", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.75.195)，对端地址=(other_system=10.200.59.187)", "可能原因": "未知", "metadata": {"filename": "5.2.3.2.9 ALM-9210 当前License已失效.md", "section_number": "5.2.3.2.9", "alarm_type": "ALM", "original_alarm_code": "ALM-9210"}}, {"主题": "[提示信息]\"云管理\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.35.16", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.4 云管理.md", "section_number": "5.2.4", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示信息]\"性能监控\"", "告警ID": "", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ManageOne10.200.248.247", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=management", "附加信息": "云服务=Monitor，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "******* 性能监控.md", "section_number": "*******", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[严重告警]\"Network发生【自定义监控】网络设备的设备平均CPU利用率阈值\"", "告警ID": "0001000100010001", "告警级别": "严重", "告警源": "Network", "来源系统": "ServiceOM10.200.31.52", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.15.190)，对端地址=(other_network=10.200.49.69)", "可能原因": "未知", "metadata": {"filename": "*******.1 ALM-0001000100010001 【自定义监控】网络设备的设备平均CPU利用率阈值.md", "section_number": "*******.1", "alarm_type": "ALM", "original_alarm_code": "ALM-0001000100010001"}}, {"主题": "[重要告警]\"Monitor发生【自定义监控】接口流入带宽利用率阈值\"", "告警ID": "0001000200010003", "告警级别": "重要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.36.100", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.69.205)，对端地址=(other_monitor=10.200.13.119)", "可能原因": "未知", "metadata": {"filename": "*******.10 ALM-0001000200010003 【自定义监控】接口流入带宽利用率阈值.md", "section_number": "*******.10", "alarm_type": "ALM", "original_alarm_code": "ALM-0001000200010003"}}, {"主题": "[提示告警]\"Monitor发生【自定义监控】接口流出带宽利用率阈值\"", "告警ID": "0001000200010004", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.145.17", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.142.138)，对端地址=(other_monitor=10.200.27.253)", "可能原因": "未知", "metadata": {"filename": "*******.11 ALM-0001000200010004 【自定义监控】接口流出带宽利用率阈值.md", "section_number": "*******.11", "alarm_type": "ALM", "original_alarm_code": "ALM-0001000200010004"}}, {"主题": "[严重告警]\"Monitor发生【自定义监控】接口接收包丢弃率阈值\"", "告警ID": "0001000200020001", "告警级别": "严重", "告警源": "Monitor", "来源系统": "ServiceOM10.200.100.96", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.21.112)，对端地址=(other_monitor=10.200.251.111)", "可能原因": "未知", "metadata": {"filename": "*******.12 ALM-0001000200020001 【自定义监控】接口接收包丢弃率阈值.md", "section_number": "*******.12", "alarm_type": "ALM", "original_alarm_code": "ALM-0001000200020001"}}, {"主题": "[重要告警]\"Monitor发生【自定义监控】接口发送包丢弃率阈值\"", "告警ID": "0001000200020002", "告警级别": "重要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.240.243", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.64.61)，对端地址=(other_monitor=10.200.72.76)", "可能原因": "未知", "metadata": {"filename": "*******.13 ALM-0001000200020002 【自定义监控】接口发送包丢弃率阈值.md", "section_number": "*******.13", "alarm_type": "ALM", "original_alarm_code": "ALM-0001000200020002"}}, {"主题": "[提示告警]\"Monitor发生【自定义监控】接口丢弃发送包数阈值\"", "告警ID": "0001000200020003", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.37.237", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.97.32)，对端地址=(other_monitor=10.200.50.69)", "可能原因": "未知", "metadata": {"filename": "*******.14 ALM-0001000200020003 【自定义监控】接口丢弃发送包数阈值.md", "section_number": "*******.14", "alarm_type": "ALM", "original_alarm_code": "ALM-0001000200020003"}}, {"主题": "[次要告警]\"Monitor发生【自定义监控】接口丢弃接收包数阈值\"", "告警ID": "0001000200020004", "告警级别": "次要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.109.242", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.128.250)，对端地址=(other_monitor=10.200.21.181)", "可能原因": "未知", "metadata": {"filename": "*******.15 ALM-0001000200020004 【自定义监控】接口丢弃接收包数阈值.md", "section_number": "*******.15", "alarm_type": "ALM", "original_alarm_code": "ALM-0001000200020004"}}, {"主题": "[次要告警]\"Monitor发生【自定义监控】接口发送包错误率阈值\"", "告警ID": "0001000200020005", "告警级别": "次要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.218.159", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.14.22)，对端地址=(other_monitor=10.200.148.109)", "可能原因": "未知", "metadata": {"filename": "*******.16 ALM-0001000200020005 【自定义监控】接口发送包错误率阈值.md", "section_number": "*******.16", "alarm_type": "ALM", "original_alarm_code": "ALM-0001000200020005"}}, {"主题": "[严重告警]\"Monitor发生【自定义监控】接口接收包错误率阈值\"", "告警ID": "0001000200020006", "告警级别": "严重", "告警源": "Monitor", "来源系统": "ServiceOM10.200.111.130", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.175.116)，对端地址=(other_monitor=10.200.105.116)", "可能原因": "未知", "metadata": {"filename": "*******.17 ALM-0001000200020006 【自定义监控】接口接收包错误率阈值.md", "section_number": "*******.17", "alarm_type": "ALM", "original_alarm_code": "ALM-0001000200020006"}}, {"主题": "[严重告警]\"Monitor发生【自定义监控】接口发送包数阈值\"", "告警ID": "0001000200020007", "告警级别": "严重", "告警源": "Monitor", "来源系统": "ServiceOM10.200.158.102", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.99.231)，对端地址=(other_monitor=10.200.95.88)", "可能原因": "未知", "metadata": {"filename": "*******.18 ALM-0001000200020007 【自定义监控】接口发送包数阈值.md", "section_number": "*******.18", "alarm_type": "ALM", "original_alarm_code": "ALM-0001000200020007"}}, {"主题": "[重要告警]\"Monitor发生【自定义监控】接口接收包数阈值\"", "告警ID": "0001000200020008", "告警级别": "重要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.52.86", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.159.207)，对端地址=(other_monitor=10.200.173.47)", "可能原因": "未知", "metadata": {"filename": "*******.19 ALM-0001000200020008 【自定义监控】接口接收包数阈值.md", "section_number": "*******.19", "alarm_type": "ALM", "original_alarm_code": "ALM-0001000200020008"}}, {"主题": "[重要告警]\"Network发生【自定义监控】网络设备的设备平均内存利用率阈值\"", "告警ID": "0001000100010002", "告警级别": "重要", "告警源": "Network", "来源系统": "ServiceOM10.200.14.174", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.18.47)，对端地址=(other_network=10.200.211.68)", "可能原因": "未知", "metadata": {"filename": "*******.2 ALM-0001000100010002 【自定义监控】网络设备的设备平均内存利用率阈值.md", "section_number": "*******.2", "alarm_type": "ALM", "original_alarm_code": "ALM-0001000100010002"}}, {"主题": "[严重告警]\"Monitor发生【自定义监控】接口接收包速率阈值\"", "告警ID": "0001000200020009", "告警级别": "严重", "告警源": "Monitor", "来源系统": "ServiceOM10.200.115.6", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.221.97)，对端地址=(other_monitor=10.200.53.250)", "可能原因": "未知", "metadata": {"filename": "*******.20 ALM-0001000200020009 【自定义监控】接口接收包速率阈值.md", "section_number": "*******.20", "alarm_type": "ALM", "original_alarm_code": "ALM-0001000200020009"}}, {"主题": "[提示告警]\"Monitor发生【自定义监控】接口发送包速率阈值\"", "告警ID": "000100020002000A", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.54.141", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.244.184)，对端地址=(other_monitor=10.200.222.108)", "可能原因": "未知", "metadata": {"filename": "*******.21 ALM-000100020002000A 【自定义监控】接口发送包速率阈值.md", "section_number": "*******.21", "alarm_type": "ALM", "original_alarm_code": "ALM-000100020002000A"}}, {"主题": "[提示告警]\"Monitor发生【自定义监控】接口发送错误包数阈值\"", "告警ID": "000100020002000B", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.60.45", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.190.13)，对端地址=(other_monitor=10.200.232.176)", "可能原因": "未知", "metadata": {"filename": "*******.22 ALM-000100020002000B 【自定义监控】接口发送错误包数阈值.md", "section_number": "*******.22", "alarm_type": "ALM", "original_alarm_code": "ALM-000100020002000B"}}, {"主题": "[次要告警]\"Monitor发生【自定义监控】接口接收错误包数阈值\"", "告警ID": "000100020002000C", "告警级别": "次要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.119.7", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.121.24)，对端地址=(other_monitor=10.200.63.34)", "可能原因": "未知", "metadata": {"filename": "*******.23 ALM-000100020002000C 【自定义监控】接口接收错误包数阈值.md", "section_number": "*******.23", "alarm_type": "ALM", "original_alarm_code": "ALM-000100020002000C"}}, {"主题": "[提示告警]\"Monitor发生【自定义监控】接口发送字节数阈值\"", "告警ID": "000100020002000D", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.162.207", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.80.75)，对端地址=(other_monitor=10.200.245.155)", "可能原因": "未知", "metadata": {"filename": "*******.24 ALM-000100020002000D 【自定义监控】接口发送字节数阈值.md", "section_number": "*******.24", "alarm_type": "ALM", "original_alarm_code": "ALM-000100020002000D"}}, {"主题": "[提示告警]\"Monitor发生【自定义监控】接口接收字节数阈值\"", "告警ID": "000100020002000E", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.89.136", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.229.179)，对端地址=(other_monitor=10.200.22.117)", "可能原因": "未知", "metadata": {"filename": "*******.25 ALM-000100020002000E 【自定义监控】接口接收字节数阈值.md", "section_number": "*******.25", "alarm_type": "ALM", "original_alarm_code": "ALM-000100020002000E"}}, {"主题": "[重要告警]\"ELB发生【自定义监控】弹性负载均衡的并发连接数阈值\"", "告警ID": "0001000300010001", "告警级别": "重要", "告警源": "ELB", "来源系统": "ServiceOM10.200.87.138", "定位信息": "区域=SH_CSVW，云服务=ELB，节点类型=mgt", "附加信息": "云服务=ELB，服务=mgt，本端地址=(10.200.179.252)，对端地址=(other_elb=10.200.162.30)", "可能原因": "未知", "metadata": {"filename": "*******.26 ALM-0001000300010001 【自定义监控】弹性负载均衡的并发连接数阈值.md", "section_number": "*******.26", "alarm_type": "ALM", "original_alarm_code": "ALM-0001000300010001"}}, {"主题": "[提示告警]\"ELB发生【自定义监控】弹性负载均衡的网络流入流速阈值\"", "告警ID": "0001000300020003", "告警级别": "提示", "告警源": "ELB", "来源系统": "ServiceOM10.200.117.208", "定位信息": "区域=SH_CSVW，云服务=ELB，节点类型=mgt", "附加信息": "云服务=ELB，服务=mgt，本端地址=(10.200.247.101)，对端地址=(other_elb=10.200.211.7)", "可能原因": "未知", "metadata": {"filename": "*******.27 ALM-0001000300020003 【自定义监控】弹性负载均衡的网络流入流速阈值.md", "section_number": "*******.27", "alarm_type": "ALM", "original_alarm_code": "ALM-0001000300020003"}}, {"主题": "[次要告警]\"ELB发生【自定义监控】弹性负载均衡的网络流出流速阈值\"", "告警ID": "0001000300020004", "告警级别": "次要", "告警源": "ELB", "来源系统": "ServiceOM10.200.44.20", "定位信息": "区域=SH_CSVW，云服务=ELB，节点类型=mgt", "附加信息": "云服务=ELB，服务=mgt，本端地址=(10.200.30.163)，对端地址=(other_elb=10.200.238.13)", "可能原因": "未知", "metadata": {"filename": "*******.28 ALM-0001000300020004 【自定义监控】弹性负载均衡的网络流出流速阈值.md", "section_number": "*******.28", "alarm_type": "ALM", "original_alarm_code": "ALM-0001000300020004"}}, {"主题": "[重要告警]\"Monitor发生【自定义监控】单板CPU利用率阈值\"", "告警ID": "0001000400010001", "告警级别": "重要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.78.87", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.56.36)，对端地址=(other_monitor=10.200.22.199)", "可能原因": "未知", "metadata": {"filename": "*******.29 ALM-0001000400010001 【自定义监控】单板CPU利用率阈值.md", "section_number": "*******.29", "alarm_type": "ALM", "original_alarm_code": "ALM-0001000400010001"}}, {"主题": "[严重告警]\"Network发生【自定义监控】网络设备的响应时间阈值\"", "告警ID": "0001000100010003", "告警级别": "严重", "告警源": "Network", "来源系统": "ServiceOM10.200.17.34", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.142.179)，对端地址=(other_network=10.200.2.200)", "可能原因": "未知", "metadata": {"filename": "*******.3 ALM-0001000100010003 【自定义监控】网络设备的响应时间阈值.md", "section_number": "*******.3", "alarm_type": "ALM", "original_alarm_code": "ALM-0001000100010003"}}, {"主题": "[严重告警]\"Monitor发生【自定义监控】单板内存利用率阈值\"", "告警ID": "0001000400010002", "告警级别": "严重", "告警源": "Monitor", "来源系统": "ServiceOM10.200.235.1", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.245.196)，对端地址=(other_monitor=10.200.174.50)", "可能原因": "未知", "metadata": {"filename": "*******.30 ALM-0001000400010002 【自定义监控】单板内存利用率阈值.md", "section_number": "*******.30", "alarm_type": "ALM", "original_alarm_code": "ALM-0001000400010002"}}, {"主题": "[严重告警]\"Monitor发生【自定义监控】光口的光模块接收功率阈值\"", "告警ID": "0001000500010001", "告警级别": "严重", "告警源": "Monitor", "来源系统": "ServiceOM10.200.56.137", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.221.6)，对端地址=(other_monitor=10.200.4.243)", "可能原因": "未知", "metadata": {"filename": "*******.31 ALM-0001000500010001 【自定义监控】光口的光模块接收功率阈值.md", "section_number": "*******.31", "alarm_type": "ALM", "original_alarm_code": "ALM-0001000500010001"}}, {"主题": "[重要告警]\"Monitor发生【自定义监控】光口的光模块发送功率阈值\"", "告警ID": "0001000500010002", "告警级别": "重要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.206.32", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.12.211)，对端地址=(other_monitor=10.200.150.211)", "可能原因": "未知", "metadata": {"filename": "*******.32 ALM-0001000500010002 【自定义监控】光口的光模块发送功率阈值.md", "section_number": "*******.32", "alarm_type": "ALM", "original_alarm_code": "ALM-0001000500010002"}}, {"主题": "[提示告警]\"Monitor发生【自定义监控】电源的电源功率阈值\"", "告警ID": "0003000200010001", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.203.173", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.206.197)，对端地址=(other_monitor=10.200.143.92)", "可能原因": "未知", "metadata": {"filename": "*******.33 ALM-0003000200010001 【自定义监控】电源的电源功率阈值.md", "section_number": "*******.33", "alarm_type": "ALM", "original_alarm_code": "ALM-0003000200010001"}}, {"主题": "[次要告警]\"Network发生【自定义监控】宿主机的CPU使用率阈值\"", "告警ID": "0005000100010001", "告警级别": "次要", "告警源": "Network", "来源系统": "ServiceOM10.200.234.86", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.83.226)，对端地址=(other_network=10.200.183.84)", "可能原因": "未知", "metadata": {"filename": "*******.34 ALM-0005000100010001 【自定义监控】宿主机的CPU使用率阈值.md", "section_number": "*******.34", "alarm_type": "ALM", "original_alarm_code": "ALM-0005000100010001"}}, {"主题": "[重要告警]\"Network发生【自定义监控】宿主机的内存使用率阈值\"", "告警ID": "0005000100020008", "告警级别": "重要", "告警源": "Network", "来源系统": "ServiceOM10.200.233.219", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.137.117)，对端地址=(other_network=10.200.232.229)", "可能原因": "未知", "metadata": {"filename": "*******.35 ALM-0005000100020008 【自定义监控】宿主机的内存使用率阈值.md", "section_number": "*******.35", "alarm_type": "ALM", "original_alarm_code": "ALM-0005000100020008"}}, {"主题": "[提示告警]\"Network发生【自定义监控】宿主机的网络流入速率阈\"", "告警ID": "000500010003000B", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.6.129", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.123.173)，对端地址=(other_network=10.200.9.221)", "可能原因": "未知", "metadata": {"filename": "*******.36 ALM-000500010003000B 【自定义监控】宿主机的网络流入速率阈.md", "section_number": "*******.36", "alarm_type": "ALM", "original_alarm_code": "ALM-000500010003000B"}}, {"主题": "[提示告警]\"Network发生【自定义监控】宿主机的网络流出速率阈\"", "告警ID": "000500010003000C", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.92.71", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.251.75)，对端地址=(other_network=10.200.167.208)", "可能原因": "未知", "metadata": {"filename": "*******.37 ALM-000500010003000C 【自定义监控】宿主机的网络流出速率阈.md", "section_number": "*******.37", "alarm_type": "ALM", "original_alarm_code": "ALM-000500010003000C"}}, {"主题": "[次要告警]\"Network发生【自定义监控】宿主机的磁盘使用率阈值\"", "告警ID": "0005000100040014", "告警级别": "次要", "告警源": "Network", "来源系统": "ServiceOM10.200.134.208", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.104.239)，对端地址=(other_network=10.200.253.111)", "可能原因": "未知", "metadata": {"filename": "*******.38 ALM-0005000100040014 【自定义监控】宿主机的磁盘使用率阈值.md", "section_number": "*******.38", "alarm_type": "ALM", "original_alarm_code": "ALM-0005000100040014"}}, {"主题": "[次要告警]\"Process发生【自定义监控】弹性云服务器的CPU使用率阈值\"", "告警ID": "0002000200010001", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.141.163", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.163.96)，对端地址=(other_process=10.200.232.110)", "可能原因": "未知", "metadata": {"filename": "*******.39 ALM-0002000200010001 【自定义监控】弹性云服务器的CPU使用率阈值.md", "section_number": "*******.39", "alarm_type": "ALM", "original_alarm_code": "ALM-0002000200010001"}}, {"主题": "[提示告警]\"Network发生【自定义监控】网络设备的当日不可达比率阈值\"", "告警ID": "0001000100010004", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.100.182", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.251.165)，对端地址=(other_network=10.200.170.132)", "可能原因": "未知", "metadata": {"filename": "*******.4 ALM-0001000100010004 【自定义监控】网络设备的当日不可达比率阈值.md", "section_number": "*******.4", "alarm_type": "ALM", "original_alarm_code": "ALM-0001000100010004"}}, {"主题": "[次要告警]\"Process发生【自定义监控】弹性云服务器的内存使用率阈值\"", "告警ID": "0002000200020003", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.198.201", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.36.51)，对端地址=(other_process=10.200.64.44)", "可能原因": "未知", "metadata": {"filename": "*******.40 ALM-0002000200020003 【自定义监控】弹性云服务器的内存使用率阈值.md", "section_number": "*******.40", "alarm_type": "ALM", "original_alarm_code": "ALM-0002000200020003"}}, {"主题": "[次要告警]\"Process发生【自定义监控】弹性云服务器的云硬盘使用率阈值\"", "告警ID": "0002000200040004", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.170.244", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.229.58)，对端地址=(other_process=10.200.222.188)", "可能原因": "未知", "metadata": {"filename": "*******.41 ALM-0002000200040004 【自定义监控】弹性云服务器的云硬盘使用率阈值.md", "section_number": "*******.41", "alarm_type": "ALM", "original_alarm_code": "ALM-0002000200040004"}}, {"主题": "[严重告警]\"Compute发生【自定义监控】虚拟机的CPU使用率阈值\"", "告警ID": "0005000200010001", "告警级别": "严重", "告警源": "Compute", "来源系统": "ServiceOM10.200.50.55", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.79.180)，对端地址=(other_compute=10.200.24.153)", "可能原因": "未知", "metadata": {"filename": "*******.42 ALM-0005000200010001 【自定义监控】虚拟机的CPU使用率阈值.md", "section_number": "*******.42", "alarm_type": "ALM", "original_alarm_code": "ALM-0005000200010001"}}, {"主题": "[严重告警]\"Compute发生【自定义监控】虚拟机的内存使用率阈值\"", "告警ID": "0005000200020003", "告警级别": "严重", "告警源": "Compute", "来源系统": "ServiceOM10.200.109.233", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.208.73)，对端地址=(other_compute=10.200.76.92)", "可能原因": "未知", "metadata": {"filename": "*******.43 ALM-0005000200020003 【自定义监控】虚拟机的内存使用率阈值.md", "section_number": "*******.43", "alarm_type": "ALM", "original_alarm_code": "ALM-0005000200020003"}}, {"主题": "[严重告警]\"Compute发生【自定义监控】虚拟机的云硬盘使用率阈值\"", "告警ID": "0005000200040004", "告警级别": "严重", "告警源": "Compute", "来源系统": "ServiceOM10.200.32.237", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.164.75)，对端地址=(other_compute=10.200.251.95)", "可能原因": "未知", "metadata": {"filename": "*******.44 ALM-0005000200040004 【自定义监控】虚拟机的云硬盘使用率阈值.md", "section_number": "*******.44", "alarm_type": "ALM", "original_alarm_code": "ALM-0005000200040004"}}, {"主题": "[严重告警]\"Network发生【自定义监控】主机组的CPU使用率阈值\"", "告警ID": "0005000300010001", "告警级别": "严重", "告警源": "Network", "来源系统": "ServiceOM10.200.148.138", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.145.125)，对端地址=(other_network=10.200.44.61)", "可能原因": "未知", "metadata": {"filename": "*******.45 ALM-0005000300010001 【自定义监控】主机组的CPU使用率阈值.md", "section_number": "*******.45", "alarm_type": "ALM", "original_alarm_code": "ALM-0005000300010001"}}, {"主题": "[重要告警]\"Network发生【自定义监控】主机组的内存使用率阈值\"", "告警ID": "0005000300020007", "告警级别": "重要", "告警源": "Network", "来源系统": "ServiceOM10.200.71.85", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.194.42)，对端地址=(other_network=10.200.177.127)", "可能原因": "未知", "metadata": {"filename": "*******.46 ALM-0005000300020007 【自定义监控】主机组的内存使用率阈值.md", "section_number": "*******.46", "alarm_type": "ALM", "original_alarm_code": "ALM-0005000300020007"}}, {"主题": "[提示告警]\"Network发生【自定义监控】主机组的网络流入速率阈值\"", "告警ID": "0005000300030010", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.168.149", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.239.73)，对端地址=(other_network=10.200.27.79)", "可能原因": "未知", "metadata": {"filename": "*******.47 ALM-0005000300030010 【自定义监控】主机组的网络流入速率阈值.md", "section_number": "*******.47", "alarm_type": "ALM", "original_alarm_code": "ALM-0005000300030010"}}, {"主题": "[次要告警]\"Network发生【自定义监控】主机组的网络流出速率阈值\"", "告警ID": "0005000300030011", "告警级别": "次要", "告警源": "Network", "来源系统": "ServiceOM10.200.100.100", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.187.85)，对端地址=(other_network=10.200.77.25)", "可能原因": "未知", "metadata": {"filename": "*******.48 ALM-0005000300030011 【自定义监控】主机组的网络流出速率阈值.md", "section_number": "*******.48", "alarm_type": "ALM", "original_alarm_code": "ALM-0005000300030011"}}, {"主题": "[重要告警]\"Monitor发生【自定义监控】端口性能的速率阈值\"", "告警ID": "0007000100010001", "告警级别": "重要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.128.106", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.250.186)，对端地址=(other_monitor=10.200.152.216)", "可能原因": "未知", "metadata": {"filename": "*******.49 ALM-0007000100010001 【自定义监控】端口性能的速率阈值.md", "section_number": "*******.49", "alarm_type": "ALM", "original_alarm_code": "ALM-0007000100010001"}}, {"主题": "[重要告警]\"Network发生【自定义监控】网络设备的当前会话新建速率阈值\"", "告警ID": "0001000100020003", "告警级别": "重要", "告警源": "Network", "来源系统": "ServiceOM10.200.92.220", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.214.72)，对端地址=(other_network=10.200.175.96)", "可能原因": "未知", "metadata": {"filename": "*******.5 ALM-0001000100020003 【自定义监控】网络设备的当前会话新建速率阈值.md", "section_number": "*******.5", "alarm_type": "ALM", "original_alarm_code": "ALM-0001000100020003"}}, {"主题": "[提示告警]\"Monitor发生【自定义监控】端口性能的CRC错误阈值\"", "告警ID": "0007000100010002", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.170.121", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.101.103)，对端地址=(other_monitor=10.200.60.158)", "可能原因": "未知", "metadata": {"filename": "*******.50 ALM-0007000100010002 【自定义监控】端口性能的CRC错误阈值.md", "section_number": "*******.50", "alarm_type": "ALM", "original_alarm_code": "ALM-0007000100010002"}}, {"主题": "[提示告警]\"Monitor发生【自定义监控】端口性能的发送link reset错误阈值\"", "告警ID": "0007000100010003", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.131.90", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.168.107)，对端地址=(other_monitor=10.200.148.85)", "可能原因": "未知", "metadata": {"filename": "*******.51 ALM-0007000100010003 【自定义监控】端口性能的发送link reset错误阈值.md", "section_number": "*******.51", "alarm_type": "ALM", "original_alarm_code": "ALM-0007000100010003"}}, {"主题": "[次要告警]\"Monitor发生【自定义监控】端口性能的接收link reset错误阈值\"", "告警ID": "0007000100010004", "告警级别": "次要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.156.135", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.68.69)，对端地址=(other_monitor=10.200.219.25)", "可能原因": "未知", "metadata": {"filename": "*******.52 ALM-0007000100010004 【自定义监控】端口性能的接收link reset错误阈值.md", "section_number": "*******.52", "alarm_type": "ALM", "original_alarm_code": "ALM-0007000100010004"}}, {"主题": "[严重告警]\"Monitor发生【自定义监控】端口性能的link reset错误总数阈值\"", "告警ID": "0007000100010005", "告警级别": "严重", "告警源": "Monitor", "来源系统": "ServiceOM10.200.200.6", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.48.23)，对端地址=(other_monitor=10.200.250.111)", "可能原因": "未知", "metadata": {"filename": "*******.53 ALM-0007000100010005 【自定义监控】端口性能的link reset错误总数阈值.md", "section_number": "*******.53", "alarm_type": "ALM", "original_alarm_code": "ALM-0007000100010005"}}, {"主题": "[严重告警]\"Monitor发生【自定义监控】端口性能的class 3 discard错误阈值\"", "告警ID": "0007000100010006", "告警级别": "严重", "告警源": "Monitor", "来源系统": "ServiceOM10.200.50.6", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.157.180)，对端地址=(other_monitor=10.200.181.3)", "可能原因": "未知", "metadata": {"filename": "*******.54 ALM-0007000100010006 【自定义监控】端口性能的class 3 discard错误阈值.md", "section_number": "*******.54", "alarm_type": "ALM", "original_alarm_code": "ALM-0007000100010006"}}, {"主题": "[重要告警]\"Monitor发生【自定义监控】端口性能的sync loss错误阈值\"", "告警ID": "0007000100010007", "告警级别": "重要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.253.170", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.183.205)，对端地址=(other_monitor=10.200.65.209)", "可能原因": "未知", "metadata": {"filename": "*******.55 ALM-0007000100010007 【自定义监控】端口性能的sync loss错误阈值.md", "section_number": "*******.55", "alarm_type": "ALM", "original_alarm_code": "ALM-0007000100010007"}}, {"主题": "[严重告警]\"Monitor发生【自定义监控】端口性能的接收利用率阈值\"", "告警ID": "0007000100010008", "告警级别": "严重", "告警源": "Monitor", "来源系统": "ServiceOM10.200.125.177", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.117.2)，对端地址=(other_monitor=10.200.54.215)", "可能原因": "未知", "metadata": {"filename": "*******.56 ALM-0007000100010008 【自定义监控】端口性能的接收利用率阈值.md", "section_number": "*******.56", "alarm_type": "ALM", "original_alarm_code": "ALM-0007000100010008"}}, {"主题": "[提示告警]\"Monitor发生【自定义监控】端口性能的缓冲信用量阈值\"", "告警ID": "0007000100010009", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.146.122", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.28.74)，对端地址=(other_monitor=10.200.204.116)", "可能原因": "未知", "metadata": {"filename": "*******.57 ALM-0007000100010009 【自定义监控】端口性能的缓冲信用量阈值.md", "section_number": "*******.57", "alarm_type": "ALM", "original_alarm_code": "ALM-0007000100010009"}}, {"主题": "[重要告警]\"Monitor发生【自定义监控】端口性能的接收速率阈值\"", "告警ID": "000700010001000A", "告警级别": "重要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.255.159", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.18.111)，对端地址=(other_monitor=10.200.29.169)", "可能原因": "未知", "metadata": {"filename": "*******.58 ALM-000700010001000A 【自定义监控】端口性能的接收速率阈值.md", "section_number": "*******.58", "alarm_type": "ALM", "original_alarm_code": "ALM-000700010001000A"}}, {"主题": "[次要告警]\"Monitor发生【自定义监控】端口性能的发送利用率阈\"", "告警ID": "000700010001000B", "告警级别": "次要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.227.202", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.98.15)，对端地址=(other_monitor=10.200.154.172)", "可能原因": "未知", "metadata": {"filename": "*******.59 ALM-000700010001000B 【自定义监控】端口性能的发送利用率阈.md", "section_number": "*******.59", "alarm_type": "ALM", "original_alarm_code": "ALM-000700010001000B"}}, {"主题": "[重要告警]\"Network发生【自定义监控】网络设备的当前会话总数阈值\"", "告警ID": "0001000100020004", "告警级别": "重要", "告警源": "Network", "来源系统": "ServiceOM10.200.250.176", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.194.218)，对端地址=(other_network=10.200.192.205)", "可能原因": "未知", "metadata": {"filename": "*******.6 ALM-0001000100020004 【自定义监控】网络设备的当前会话总数阈值.md", "section_number": "*******.6", "alarm_type": "ALM", "original_alarm_code": "ALM-0001000100020004"}}, {"主题": "[严重告警]\"Monitor发生【自定义监控】端口性能的带宽利用率阈\"", "告警ID": "000700010001000C", "告警级别": "严重", "告警源": "Monitor", "来源系统": "ServiceOM10.200.191.39", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.4.90)，对端地址=(other_monitor=10.200.65.32)", "可能原因": "未知", "metadata": {"filename": "*******.60 ALM-000700010001000C 【自定义监控】端口性能的带宽利用率阈.md", "section_number": "*******.60", "alarm_type": "ALM", "original_alarm_code": "ALM-000700010001000C"}}, {"主题": "[次要告警]\"Monitor发生【自定义监控】端口性能的link f\"", "告警ID": "000700010001000D", "告警级别": "次要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.183.92", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.111.170)，对端地址=(other_monitor=10.200.3.126)", "可能原因": "未知", "metadata": {"filename": "*******.61 ALM-000700010001000D 【自定义监控】端口性能的link f.md", "section_number": "*******.61", "alarm_type": "ALM", "original_alarm_code": "ALM-000700010001000D"}}, {"主题": "[重要告警]\"Monitor发生【自定义监控】端口性能的signal\"", "告警ID": "000700010001000E", "告警级别": "重要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.204.113", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.139.168)，对端地址=(other_monitor=10.200.255.242)", "可能原因": "未知", "metadata": {"filename": "*******.62 ALM-000700010001000E 【自定义监控】端口性能的signal.md", "section_number": "*******.62", "alarm_type": "ALM", "original_alarm_code": "ALM-000700010001000E"}}, {"主题": "[提示告警]\"Monitor发生【自定义监控】端口性能的总error\"", "告警ID": "000700010001000F", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.255.77", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.214.22)，对端地址=(other_monitor=10.200.133.111)", "可能原因": "未知", "metadata": {"filename": "*******.63 ALM-000700010001000F 【自定义监控】端口性能的总error.md", "section_number": "*******.63", "alarm_type": "ALM", "original_alarm_code": "ALM-000700010001000F"}}, {"主题": "[重要告警]\"Monitor发生【自定义监控】端口性能的发送速率阈值\"", "告警ID": "0007000100010010", "告警级别": "重要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.97.182", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.66.8)，对端地址=(other_monitor=10.200.196.81)", "可能原因": "未知", "metadata": {"filename": "*******.64 ALM-0007000100010010 【自定义监控】端口性能的发送速率阈值.md", "section_number": "*******.64", "alarm_type": "ALM", "original_alarm_code": "ALM-0007000100010010"}}, {"主题": "[严重告警]\"Network发生【自定义监控】大数据主机组CPU使用率阈值\"", "告警ID": "0008000100010001", "告警级别": "严重", "告警源": "Network", "来源系统": "ServiceOM10.200.245.162", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.181.7)，对端地址=(other_network=10.200.143.131)", "可能原因": "未知", "metadata": {"filename": "*******.65 ALM-0008000100010001 【自定义监控】大数据主机组CPU使用率阈值.md", "section_number": "*******.65", "alarm_type": "ALM", "original_alarm_code": "ALM-0008000100010001"}}, {"主题": "[提示告警]\"Network发生【自定义监控】大数据主机组的内存使用率阈\"", "告警ID": "0008000100010002", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.89.161", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.152.56)，对端地址=(other_network=10.200.14.226)", "可能原因": "未知", "metadata": {"filename": "*******.66 ALM-0008000100010002 【自定义监控】大数据主机组的内存使用率阈.md", "section_number": "*******.66", "alarm_type": "ALM", "original_alarm_code": "ALM-0008000100010002"}}, {"主题": "[次要告警]\"Network发生【自定义监控】大数据主机组的磁盘使用率阈值\"", "告警ID": "0008000100010003", "告警级别": "次要", "告警源": "Network", "来源系统": "ServiceOM10.200.59.202", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(************)，对端地址=(other_network=*************)", "可能原因": "未知", "metadata": {"filename": "*******.67 ALM-0008000100010003 【自定义监控】大数据主机组的磁盘使用率阈值.md", "section_number": "*******.67", "alarm_type": "ALM", "original_alarm_code": "ALM-0008000100010003"}}, {"主题": "[严重告警]\"Monitor发生【自定义监控】弹性IP的上行流量阈值\"", "告警ID": "0002000500000001", "告警级别": "严重", "告警源": "Monitor", "来源系统": "ServiceOM10.200.174.175", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(*************)，对端地址=(other_monitor=*************)", "可能原因": "未知", "metadata": {"filename": "*******.68 ALM-0002000500000001 【自定义监控】弹性IP的上行流量阈值.md", "section_number": "*******.68", "alarm_type": "ALM", "original_alarm_code": "ALM-0002000500000001"}}, {"主题": "[次要告警]\"Monitor发生【自定义监控】弹性IP的下行流量阈值\"", "告警ID": "0002000500000003", "告警级别": "次要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.136.16", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(*************)，对端地址=(other_monitor=*************)", "可能原因": "未知", "metadata": {"filename": "*******.69 ALM-0002000500000003 【自定义监控】弹性IP的下行流量阈值.md", "section_number": "*******.69", "alarm_type": "ALM", "original_alarm_code": "ALM-0002000500000003"}}, {"主题": "[次要告警]\"Network发生【自定义监控】网络设备的网络流量值阈值\"", "告警ID": "0001000100020005", "告警级别": "次要", "告警源": "Network", "来源系统": "ServiceOM*************", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(**************)，对端地址=(other_network=*************)", "可能原因": "未知", "metadata": {"filename": "*******.7 ALM-0001000100020005 【自定义监控】网络设备的网络流量值阈值.md", "section_number": "*******.7", "alarm_type": "ALM", "original_alarm_code": "ALM-0001000100020005"}}, {"主题": "[提示告警]\"Process发生【自定义监控】裸金属服务器的用户空间\"", "告警ID": "0002000A00000001", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.193.195", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.145.211)，对端地址=(other_process=10.200.172.17)", "可能原因": "未知", "metadata": {"filename": "*******.70 ALM-0002000A00000001 【自定义监控】裸金属服务器的用户空间.md", "section_number": "*******.70", "alarm_type": "ALM", "original_alarm_code": "ALM-0002000A00000001"}}, {"主题": "[次要告警]\"Process发生【自定义监控】裸金属服务器的内存使用\"", "告警ID": "0002000A0000000D", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.243.248", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.209.207)，对端地址=(other_process=10.200.193.179)", "可能原因": "未知", "metadata": {"filename": "*******.71 ALM-0002000A0000000D 【自定义监控】裸金属服务器的内存使用.md", "section_number": "*******.71", "alarm_type": "ALM", "original_alarm_code": "ALM-0002000A0000000D"}}, {"主题": "[次要告警]\"Process发生【自定义监控】裸金属服务器的磁盘使用\"", "告警ID": "0002000A00000014", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.38.107", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.171.87)，对端地址=(other_process=10.200.162.153)", "可能原因": "未知", "metadata": {"filename": "*******.72 ALM-0002000A00000014 【自定义监控】裸金属服务器的磁盘使用.md", "section_number": "*******.72", "alarm_type": "ALM", "original_alarm_code": "ALM-0002000A00000014"}}, {"主题": "[提示告警]\"Database发生【自定义监控】关系数据库的CPU使用率阈值\"", "告警ID": "0002000700000001", "告警级别": "提示", "告警源": "Database", "来源系统": "ServiceOM10.200.241.200", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.71.215)，对端地址=(other_database=10.200.196.21)", "可能原因": "未知", "metadata": {"filename": "*******.73 ALM-0002000700000001 【自定义监控】关系数据库的CPU使用率阈值.md", "section_number": "*******.73", "alarm_type": "ALM", "original_alarm_code": "ALM-0002000700000001"}}, {"主题": "[严重告警]\"Database发生【自定义监控】关系数据库的内存使用率阈值\"", "告警ID": "0002000700000002", "告警级别": "严重", "告警源": "Database", "来源系统": "ServiceOM10.200.191.110", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.201.15)，对端地址=(other_database=10.200.102.244)", "可能原因": "未知", "metadata": {"filename": "*******.74 ALM-0002000700000002 【自定义监控】关系数据库的内存使用率阈值.md", "section_number": "*******.74", "alarm_type": "ALM", "original_alarm_code": "ALM-0002000700000002"}}, {"主题": "[提示告警]\"Database发生【自定义监控】关系数据库的IOPS阈值\"", "告警ID": "0002000700000003", "告警级别": "提示", "告警源": "Database", "来源系统": "ServiceOM10.200.19.159", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.168.116)，对端地址=(other_database=10.200.225.89)", "可能原因": "未知", "metadata": {"filename": "*******.75 ALM-0002000700000003 【自定义监控】关系数据库的IOPS阈值.md", "section_number": "*******.75", "alarm_type": "ALM", "original_alarm_code": "ALM-0002000700000003"}}, {"主题": "[提示告警]\"Database发生【自定义监控】关系数据库的数据库连接\"", "告警ID": "000200070000002A", "告警级别": "提示", "告警源": "Database", "来源系统": "ServiceOM10.200.194.140", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.210.190)，对端地址=(other_database=10.200.157.111)", "可能原因": "未知", "metadata": {"filename": "*******.76 ALM-000200070000002A 【自定义监控】关系数据库的数据库连接.md", "section_number": "*******.76", "alarm_type": "ALM", "original_alarm_code": "ALM-000200070000002A"}}, {"主题": "[次要告警]\"Database发生【自定义监控】关系数据库的使用中的数据库连接数阈值告警\"", "告警ID": "0002000700000036", "告警级别": "次要", "告警源": "Database", "来源系统": "ServiceOM10.200.143.249", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.166.65)，对端地址=(other_database=10.200.62.183)", "可能原因": "未知", "metadata": {"filename": "*******.77 ALM-0002000700000036 【自定义监控】关系数据库的使用中的数据库连接数阈值告警.md", "section_number": "*******.77", "alarm_type": "ALM", "original_alarm_code": "ALM-0002000700000036"}}, {"主题": "[重要告警]\"Database发生【自定义监控】Oracle CDB关系型数据库的主机内存使用率阈值\"", "告警ID": "0002000800000003", "告警级别": "重要", "告警源": "Database", "来源系统": "ServiceOM10.200.152.23", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.193.180)，对端地址=(other_database=10.200.41.7)", "可能原因": "未知", "metadata": {"filename": "*******.78 ALM-0002000800000003 【自定义监控】Oracle CDB关系型数据库的主机内存使用率阈值.md", "section_number": "*******.78", "alarm_type": "ALM", "original_alarm_code": "ALM-0002000800000003"}}, {"主题": "[重要告警]\"Database发生【自定义监控】Oracle CDB关系型数据库的主机CPU使用率阈值\"", "告警ID": "0002000800000004", "告警级别": "重要", "告警源": "Database", "来源系统": "ServiceOM10.200.67.229", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.35.218)，对端地址=(other_database=10.200.97.83)", "可能原因": "未知", "metadata": {"filename": "*******.79 ALM-0002000800000004 【自定义监控】Oracle CDB关系型数据库的主机CPU使用率阈值.md", "section_number": "*******.79", "alarm_type": "ALM", "original_alarm_code": "ALM-0002000800000004"}}, {"主题": "[次要告警]\"Monitor发生【自定义监控】接口接收速率阈值\"", "告警ID": "0001000200010001", "告警级别": "次要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.227.171", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.174.205)，对端地址=(other_monitor=10.200.70.101)", "可能原因": "未知", "metadata": {"filename": "*******.8 ALM-0001000200010001 【自定义监控】接口接收速率阈值.md", "section_number": "*******.8", "alarm_type": "ALM", "original_alarm_code": "ALM-0001000200010001"}}, {"主题": "[重要告警]\"Database发生【自定义监控】Oracle PDB关系型数据库的主机内存使用率阈值\"", "告警ID": "0002000900000003", "告警级别": "重要", "告警源": "Database", "来源系统": "ServiceOM10.200.2.45", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.27.210)，对端地址=(other_database=10.200.132.234)", "可能原因": "未知", "metadata": {"filename": "*******.80 ALM-0002000900000003 【自定义监控】Oracle PDB关系型数据库的主机内存使用率阈值.md", "section_number": "*******.80", "alarm_type": "ALM", "original_alarm_code": "ALM-0002000900000003"}}, {"主题": "[次要告警]\"Database发生【自定义监控】Oracle PDB关系型数据库的主机CPU使用率阈值\"", "告警ID": "0002000900000004", "告警级别": "次要", "告警源": "Database", "来源系统": "ServiceOM10.200.55.122", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.94.9)，对端地址=(other_database=10.200.171.79)", "可能原因": "未知", "metadata": {"filename": "*******.81 ALM-0002000900000004 【自定义监控】Oracle PDB关系型数据库的主机CPU使用率阈值.md", "section_number": "*******.81", "alarm_type": "ALM", "original_alarm_code": "ALM-0002000900000004"}}, {"主题": "[次要告警]\"Database发生【自定义监控】关系数据库的CPU使用率超过阈值\"", "告警ID": "0002000700000048", "告警级别": "次要", "告警源": "Database", "来源系统": "ServiceOM10.200.5.114", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.181.243)，对端地址=(other_database=10.200.1.108)", "可能原因": "未知", "metadata": {"filename": "*******.82 ALM-0002000700000048 【自定义监控】关系数据库的CPU使用率超过阈值.md", "section_number": "*******.82", "alarm_type": "ALM", "original_alarm_code": "ALM-0002000700000048"}}, {"主题": "[重要告警]\"Database发生【自定义监控】关系数据库的内存使用率超过阈值\"", "告警ID": "0002000700000049", "告警级别": "重要", "告警源": "Database", "来源系统": "ServiceOM10.200.148.249", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.155.137)，对端地址=(other_database=10.200.137.89)", "可能原因": "未知", "metadata": {"filename": "*******.83 ALM-0002000700000049 【自定义监控】关系数据库的内存使用率超过阈值.md", "section_number": "*******.83", "alarm_type": "ALM", "original_alarm_code": "ALM-0002000700000049"}}, {"主题": "[严重告警]\"Monitor发生【自定义监控】电源的电源功率超过阈值\"", "告警ID": "0003000200010005", "告警级别": "严重", "告警源": "Monitor", "来源系统": "ServiceOM10.200.92.240", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.195.151)，对端地址=(other_monitor=10.200.103.44)", "可能原因": "未知", "metadata": {"filename": "*******.84 ALM-0003000200010005 【自定义监控】电源的电源功率超过阈值.md", "section_number": "*******.84", "alarm_type": "ALM", "original_alarm_code": "ALM-0003000200010005"}}, {"主题": "[严重告警]\"Process发生【自定义监控】分布式缓存服务的每秒并\"", "告警ID": "000A000200000010", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.171.216", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.209.217)，对端地址=(other_process=10.200.10.108)", "可能原因": "未知", "metadata": {"filename": "*******.85 ALM-000A000200000010 【自定义监控】分布式缓存服务的每秒并.md", "section_number": "*******.85", "alarm_type": "ALM", "original_alarm_code": "ALM-000A000200000010"}}, {"主题": "[提示告警]\"Process发生【自定义监控】分布式缓存服务的缓存命\"", "告警ID": "000A000200000021", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.85.209", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.91.68)，对端地址=(other_process=10.200.164.92)", "可能原因": "未知", "metadata": {"filename": "*******.86 ALM-000A000200000021 【自定义监控】分布式缓存服务的缓存命.md", "section_number": "*******.86", "alarm_type": "ALM", "original_alarm_code": "ALM-000A000200000021"}}, {"主题": "[次要告警]\"Process发生【自定义监控】分布式缓存服务的每秒并\"", "告警ID": "000A00020000002D", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.179.245", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.145.178)，对端地址=(other_process=10.200.205.166)", "可能原因": "未知", "metadata": {"filename": "*******.87 ALM-000A00020000002D 【自定义监控】分布式缓存服务的每秒并.md", "section_number": "*******.87", "alarm_type": "ALM", "original_alarm_code": "ALM-000A00020000002D"}}, {"主题": "[次要告警]\"Process发生【自定义监控】分布式缓存服务的CPU\"", "告警ID": "000A00020000004D", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.212.30", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.207.191)，对端地址=(other_process=10.200.140.119)", "可能原因": "未知", "metadata": {"filename": "*******.88 ALM-000A00020000004D 【自定义监控】分布式缓存服务的CPU.md", "section_number": "*******.88", "alarm_type": "ALM", "original_alarm_code": "ALM-000A00020000004D"}}, {"主题": "[次要告警]\"Monitor发生【自定义监控】接口发送速率阈值\"", "告警ID": "0001000200010002", "告警级别": "次要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.137.130", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.60.81)，对端地址=(other_monitor=10.200.211.245)", "可能原因": "未知", "metadata": {"filename": "*******.9 ALM-0001000200010002 【自定义监控】接口发送速率阈值.md", "section_number": "*******.9", "alarm_type": "ALM", "original_alarm_code": "ALM-0001000200010002"}}, {"主题": "[提示信息]\"系统维护\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.53.98", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "*******0 系统维护.md", "section_number": "*******0", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示信息]\"告警参考\"", "告警ID": "", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ManageOne10.200.32.75", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=management", "附加信息": "云服务=Monitor，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "*******0.1 告警参考.md", "section_number": "*******0.1", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[严重告警]\"System发生操作系统帐\"", "告警ID": "MOMaintenanceService_100100", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.96.67", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.25.244)，对端地址=(other_system=10.200.134.142)", "可能原因": "未知", "metadata": {"filename": "*******0.1.1 ALM-MOMaintenanceService_100100 操作系统帐.md", "section_number": "*******0.1.1", "alarm_type": "ALM", "original_alarm_code": "ALM-MOMaintenanceService_100100"}}, {"主题": "[严重告警]\"System发生证书即将过\"", "告警ID": "MOMaintenanceService_100103", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.58.180", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.119.250)，对端地址=(other_system=10.200.161.177)", "可能原因": "未知", "metadata": {"filename": "*******0.1.2 ALM-MOMaintenanceService_100103 证书即将过.md", "section_number": "*******0.1.2", "alarm_type": "ALM", "original_alarm_code": "ALM-MOMaintenanceService_100103"}}, {"主题": "[次要告警]\"System发生证书即将过\"", "告警ID": "MOMaintenanceService_100106", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.57.156", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.107.10)，对端地址=(other_system=10.200.1.48)", "可能原因": "未知", "metadata": {"filename": "*******0.1.3 ALM-MOMaintenanceService_100106 证书即将过.md", "section_number": "*******0.1.3", "alarm_type": "ALM", "original_alarm_code": "ALM-MOMaintenanceService_100106"}}, {"主题": "[提示信息]\"服务监控\"", "告警ID": "", "告警级别": "提示", "告警源": "Process", "来源系统": "ManageOne10.200.136.135", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=management", "附加信息": "云服务=Process，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "*******1 服务监控.md", "section_number": "*******1", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示信息]\"告警参考\"", "告警ID": "", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ManageOne10.200.224.18", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=management", "附加信息": "云服务=Monitor，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "*******1.1 告警参考.md", "section_number": "*******1.1", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[次要告警]\"System发生节点\"", "告警ID": "servicemonitor_agent_heartbeat", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.48.39", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.2.4)，对端地址=(other_system=10.200.166.144)", "可能原因": "未知", "metadata": {"filename": "*******1.1.1 ALM-servicemonitor_agent_heartbeat 节点.md", "section_number": "*******1.1.1", "alarm_type": "ALM", "original_alarm_code": "ALM-servicemonitor_agent_heartbeat"}}, {"主题": "[次要告警]\"Unknown发生\"", "告警ID": "servicemonitor_os.disk.rd_rsp_ti", "告警级别": "次要", "告警源": "Unknown", "来源系统": "ServiceOM10.200.187.92", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.159.123)，对端地址=(other_unknown=10.200.17.200)", "可能原因": "未知", "metadata": {"filename": "*******1.1.10 ALM-servicemonitor_os.disk.rd_rsp_ti.md", "section_number": "*******1.1.10", "alarm_type": "ALM", "original_alarm_code": "ALM-servicemonitor_os.disk.rd_rsp_ti"}}, {"主题": "[严重告警]\"Unknown发生\"", "告警ID": "servicemonitor_os.disk.wt_rsp_ti", "告警级别": "严重", "告警源": "Unknown", "来源系统": "ServiceOM10.200.216.3", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.42.6)，对端地址=(other_unknown=10.200.216.35)", "可能原因": "未知", "metadata": {"filename": "*******1.1.11 ALM-servicemonitor_os.disk.wt_rsp_ti.md", "section_number": "*******1.1.11", "alarm_type": "ALM", "original_alarm_code": "ALM-servicemonitor_os.disk.wt_rsp_ti"}}, {"主题": "[提示告警]\"Unknown发生\"", "告警ID": "servicemonitor_os.fs.inode_free", "告警级别": "提示", "告警源": "Unknown", "来源系统": "ServiceOM10.200.154.129", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.20.16)，对端地址=(other_unknown=10.200.215.186)", "可能原因": "未知", "metadata": {"filename": "*******1.1.12 ALM-servicemonitor_os.fs.inode_free .md", "section_number": "*******1.1.12", "alarm_type": "ALM", "original_alarm_code": "ALM-servicemonitor_os.fs.inode_free"}}, {"主题": "[次要告警]\"System发生硬盘使\"", "告警ID": "servicemonitor_os.fs.percent", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.248.119", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.131.12)，对端地址=(other_system=10.200.52.137)", "可能原因": "未知", "metadata": {"filename": "*******1.1.13 ALM-servicemonitor_os.fs.percent 硬盘使.md", "section_number": "*******1.1.13", "alarm_type": "ALM", "original_alarm_code": "ALM-servicemonitor_os.fs.percent"}}, {"主题": "[重要告警]\"Unknown发生\"", "告警ID": "servicemonitor_redis.dbcopyStatu", "告警级别": "重要", "告警源": "Unknown", "来源系统": "ServiceOM10.200.249.92", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.210.99)，对端地址=(other_unknown=10.200.206.225)", "可能原因": "未知", "metadata": {"filename": "*******1.1.14 ALM-servicemonitor_redis.dbcopyStatu.md", "section_number": "*******1.1.14", "alarm_type": "ALM", "original_alarm_code": "ALM-servicemonitor_redis.dbcopyStatu"}}, {"主题": "[次要告警]\"Unknown发生\"", "告警ID": "servicemonitor_redis.dbsvrStatus", "告警级别": "次要", "告警源": "Unknown", "来源系统": "ServiceOM10.200.241.211", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.136.255)，对端地址=(other_unknown=10.200.204.145)", "可能原因": "未知", "metadata": {"filename": "*******1.1.15 ALM-servicemonitor_redis.dbsvrStatus.md", "section_number": "*******1.1.15", "alarm_type": "ALM", "original_alarm_code": "ALM-servicemonitor_redis.dbsvrStatus"}}, {"主题": "[次要告警]\"Unknown发生\"", "告警ID": "servicemonitor_redis.connectedCl", "告警级别": "次要", "告警源": "Unknown", "来源系统": "ServiceOM10.200.4.115", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.87.168)，对端地址=(other_unknown=10.200.149.241)", "可能原因": "未知", "metadata": {"filename": "*******1.1.16 ALM-servicemonitor_redis.connectedCl.md", "section_number": "*******1.1.16", "alarm_type": "ALM", "original_alarm_code": "ALM-servicemonitor_redis.connectedCl"}}, {"主题": "[提示告警]\"Process发生服务监控节点心跳\"", "告警ID": "servicemonitor_heartbeat", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.182.11", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.207.30)，对端地址=(other_process=10.200.181.220)", "可能原因": "未知", "metadata": {"filename": "*******1.1.2 ALM-servicemonitor_heartbeat 服务监控节点心跳.md", "section_number": "*******1.1.2", "alarm_type": "ALM", "original_alarm_code": "ALM-servicemonitor_heartbeat"}}, {"主题": "[严重告警]\"System发生CPU使用率\"", "告警ID": "servicemonitor_cpu.percent", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.37.86", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.225.83)，对端地址=(other_system=10.200.41.37)", "可能原因": "未知", "metadata": {"filename": "*******1.1.3 ALM-servicemonitor_cpu.percent CPU使用率.md", "section_number": "*******1.1.3", "alarm_type": "ALM", "original_alarm_code": "ALM-servicemonitor_cpu.percent"}}, {"主题": "[提示告警]\"System发生物理内\"", "告警ID": "servicemonitor_memory.percent", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.165.123", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.197.52)，对端地址=(other_system=10.200.107.40)", "可能原因": "未知", "metadata": {"filename": "*******1.1.4 ALM-servicemonitor_memory.percent 物理内.md", "section_number": "*******1.1.4", "alarm_type": "ALM", "original_alarm_code": "ALM-servicemonitor_memory.percent"}}, {"主题": "[重要告警]\"Unknown发生\"", "告警ID": "servicemonitor_os.nic.rx_dropped_", "告警级别": "重要", "告警源": "Unknown", "来源系统": "ServiceOM10.200.143.108", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.218.123)，对端地址=(other_unknown=10.200.189.82)", "可能原因": "未知", "metadata": {"filename": "*******1.1.5 ALM-servicemonitor_os.nic.rx_dropped_.md", "section_number": "*******1.1.5", "alarm_type": "ALM", "original_alarm_code": "ALM-servicemonitor_os.nic.rx_dropped_"}}, {"主题": "[提示告警]\"Unknown发生\"", "告警ID": "servicemonitor_os.nic.rx_errors_p", "告警级别": "提示", "告警源": "Unknown", "来源系统": "ServiceOM10.200.130.196", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.99.216)，对端地址=(other_unknown=10.200.120.2)", "可能原因": "未知", "metadata": {"filename": "*******1.1.6 ALM-servicemonitor_os.nic.rx_errors_p.md", "section_number": "*******1.1.6", "alarm_type": "ALM", "original_alarm_code": "ALM-servicemonitor_os.nic.rx_errors_p"}}, {"主题": "[重要告警]\"Unknown发生\"", "告警ID": "servicemonitor_os.nic.tx_dropped", "告警级别": "重要", "告警源": "Unknown", "来源系统": "ServiceOM10.200.79.130", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.228.161)，对端地址=(other_unknown=10.200.166.187)", "可能原因": "未知", "metadata": {"filename": "*******1.1.7 ALM-servicemonitor_os.nic.tx_dropped .md", "section_number": "*******1.1.7", "alarm_type": "ALM", "original_alarm_code": "ALM-servicemonitor_os.nic.tx_dropped"}}, {"主题": "[次要告警]\"System发生网\"", "告警ID": "servicemonitor_os.nic.tx_errors", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.184.95", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.170.27)，对端地址=(other_system=10.200.41.93)", "可能原因": "未知", "metadata": {"filename": "*******1.1.8 ALM-servicemonitor_os.nic.tx_errors 网.md", "section_number": "*******1.1.8", "alarm_type": "ALM", "original_alarm_code": "ALM-servicemonitor_os.nic.tx_errors"}}, {"主题": "[严重告警]\"System发生硬\"", "告警ID": "servicemonitor_os.disk.io_waite", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.103.138", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.173.104)，对端地址=(other_system=10.200.253.156)", "可能原因": "未知", "metadata": {"filename": "*******1.1.9 ALM-servicemonitor_os.disk.io_waite 硬.md", "section_number": "*******1.1.9", "alarm_type": "ALM", "original_alarm_code": "ALM-servicemonitor_os.disk.io_waite"}}, {"主题": "[提示信息]\"统一证书\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.23.154", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "*******2 统一证书.md", "section_number": "*******2", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示告警]\"Monitor发生系统存在即将过期证书告警\"", "告警ID": "MOCertMgmt_100101", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.249.231", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.246.94)，对端地址=(other_monitor=10.200.98.208)", "可能原因": "未知", "metadata": {"filename": "*******2.1 ALM-MOCertMgmt_100101 系统存在即将过期证书告警.md", "section_number": "*******2.1", "alarm_type": "ALM", "original_alarm_code": "ALM-MOCertMgmt_100101"}}, {"主题": "[提示信息]\"统一日志\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.139.104", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "*******3 统一日志.md", "section_number": "*******3", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[重要告警]\"System发生Elasticsearch集群心跳检测异常\"", "告警ID": "0001000300030001", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.187.144", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.56.201)，对端地址=(other_system=10.200.58.84)", "可能原因": "未知", "metadata": {"filename": "*******3.1 ALM-0001000300030001 Elasticsearch集群心跳检测异常.md", "section_number": "*******3.1", "alarm_type": "ALM", "original_alarm_code": "ALM-0001000300030001"}}, {"主题": "[提示信息]\"备份恢复\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.217.39", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "*******4 备份恢复.md", "section_number": "*******4", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示告警]\"System发生备份失败\"", "告警ID": "MOBackupService_100001", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.86.109", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.32.130)，对端地址=(other_system=10.200.99.96)", "可能原因": "未知", "metadata": {"filename": "*******4.1 ALM-MOBackupService_100001 备份失败.md", "section_number": "*******4.1", "alarm_type": "ALM", "original_alarm_code": "ALM-MOBackupService_100001"}}, {"主题": "[次要告警]\"Process发生未配置备份服务器\"", "告警ID": "MOBackupService_100002", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.115.174", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.17.196)，对端地址=(other_process=10.200.114.196)", "可能原因": "未知", "metadata": {"filename": "*******4.2 ALM-MOBackupService_100002 未配置备份服务器.md", "section_number": "*******4.2", "alarm_type": "ALM", "original_alarm_code": "ALM-MOBackupService_100002"}}, {"主题": "[提示信息]\"IES管理\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.226.169", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "*******5 IES管理.md", "section_number": "*******5", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[次要告警]\"System发生产品数据定时备份失败\"", "告警ID": "101205", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.161.193", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.155.82)，对端地址=(other_system=10.200.12.224)", "可能原因": "未知", "metadata": {"filename": "*******5.1 ALM-101205 产品数据定时备份失败.md", "section_number": "*******5.1", "alarm_type": "ALM", "original_alarm_code": "ALM-101205"}}, {"主题": "[提示信息]\"运维自动化\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.125.200", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "*******6 运维自动化.md", "section_number": "*******6", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[次要告警]\"System发生ManageOne管理资源超过部署规格限制\"", "告警ID": "10000011", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.80.143", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.158.223)，对端地址=(other_system=10.200.5.195)", "可能原因": "未知", "metadata": {"filename": "*******6.1 ALM-10000011 ManageOne管理资源超过部署规格限制.md", "section_number": "*******6.1", "alarm_type": "ALM", "original_alarm_code": "ALM-10000011"}}, {"主题": "[提示信息]\"华为虚拟化资源池\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.134.64", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "*******7 华为虚拟化资源池.md", "section_number": "*******7", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示告警]\"System发生话单文件发送到计量中心文件服\"", "告警ID": "MOVCDRService_100091", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.186.188", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.156.230)，对端地址=(other_system=10.200.42.186)", "可能原因": "未知", "metadata": {"filename": "*******7.1 ALM-MOVCDRService_100091 话单文件发送到计量中心文件服.md", "section_number": "*******7.1", "alarm_type": "ALM", "original_alarm_code": "ALM-MOVCDRService_100091"}}, {"主题": "[提示信息]\"部署面业务告警\"", "告警ID": "", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ManageOne10.200.245.115", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=management", "附加信息": "云服务=Monitor，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "*******8 部署面业务告警.md", "section_number": "*******8", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示信息]\"ManageOne管理\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.253.160", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "*******8.1 ManageOne管理.md", "section_number": "*******8.1", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[重要告警]\"System发生节点故障倒换\"", "告警ID": "101209", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.163.191", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.237.63)，对端地址=(other_system=10.200.43.213)", "可能原因": "未知", "metadata": {"filename": "*******8.1.1 ALM-101209 节点故障倒换.md", "section_number": "*******8.1.1", "alarm_type": "ALM", "original_alarm_code": "ALM-101209"}}, {"主题": "[严重告警]\"Process发生NTP服务异常\"", "告警ID": "51023", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.170.232", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.109.172)，对端地址=(other_process=10.200.44.141)", "可能原因": "未知", "metadata": {"filename": "*******8.1.2 ALM-51023 NTP服务异常.md", "section_number": "*******8.1.2", "alarm_type": "ALM", "original_alarm_code": "ALM-51023"}}, {"主题": "[提示告警]\"Database发生数据库实例故障倒换\"", "告警ID": "101211", "告警级别": "提示", "告警源": "Database", "来源系统": "ServiceOM10.200.8.16", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.42.95)，对端地址=(other_database=10.200.151.143)", "可能原因": "未知", "metadata": {"filename": "*******8.1.3 ALM-101211 数据库实例故障倒换.md", "section_number": "*******8.1.3", "alarm_type": "ALM", "original_alarm_code": "ALM-101211"}}, {"主题": "[重要告警]\"Network发生连接ZooKeeper失败\"", "告警ID": "101212", "告警级别": "重要", "告警源": "Network", "来源系统": "ServiceOM10.200.242.139", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.11.126)，对端地址=(other_network=10.200.206.143)", "可能原因": "未知", "metadata": {"filename": "*******8.1.4 ALM-101212 连接Zoo<PERSON>eeper失败.md", "section_number": "*******8.1.4", "alarm_type": "ALM", "original_alarm_code": "ALM-101212"}}, {"主题": "[提示信息]\"系统监控\"", "告警ID": "", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ManageOne10.200.71.118", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=management", "附加信息": "云服务=Monitor，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "*******8.2 系统监控.md", "section_number": "*******8.2", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[次要告警]\"Monitor发生CPU占用率过高告警\"", "告警ID": "151", "告警级别": "次要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.29.114", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.71.41)，对端地址=(other_monitor=10.200.240.246)", "可能原因": "未知", "metadata": {"filename": "*******8.2.1 ALM-151 CPU占用率过高告警.md", "section_number": "*******8.2.1", "alarm_type": "ALM", "original_alarm_code": "ALM-151"}}, {"主题": "[严重告警]\"Database发生数据库本地主备复制异常\"", "告警ID": "101210", "告警级别": "严重", "告警源": "Database", "来源系统": "ServiceOM10.200.29.125", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.159.31)，对端地址=(other_database=10.200.6.231)", "可能原因": "未知", "metadata": {"filename": "*******8.2.10 ALM-101210 数据库本地主备复制异常.md", "section_number": "*******8.2.10", "alarm_type": "ALM", "original_alarm_code": "ALM-101210"}}, {"主题": "[严重告警]\"Process发生网管服务异常退出告警\"", "告警ID": "152", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.80.180", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.190.204)，对端地址=(other_process=10.200.118.215)", "可能原因": "未知", "metadata": {"filename": "*******8.2.2 ALM-152 网管服务异常退出告警.md", "section_number": "*******8.2.2", "alarm_type": "ALM", "original_alarm_code": "ALM-152"}}, {"主题": "[严重告警]\"System发生节点状态异常\"", "告警ID": "101208", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.19.233", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.157.254)，对端地址=(other_system=10.200.2.253)", "可能原因": "未知", "metadata": {"filename": "*******8.2.3 ALM-101208 节点状态异常.md", "section_number": "*******8.2.3", "alarm_type": "ALM", "original_alarm_code": "ALM-101208"}}, {"主题": "[严重告警]\"Monitor发生内存占用率过高告警\"", "告警ID": "154", "告警级别": "严重", "告警源": "Monitor", "来源系统": "ServiceOM10.200.68.117", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.8.188)，对端地址=(other_monitor=10.200.49.39)", "可能原因": "未知", "metadata": {"filename": "*******8.2.4 ALM-154 内存占用率过高告警.md", "section_number": "*******8.2.4", "alarm_type": "ALM", "original_alarm_code": "ALM-154"}}, {"主题": "[严重告警]\"Storage发生磁盘占用率过高告警\"", "告警ID": "36", "告警级别": "严重", "告警源": "Storage", "来源系统": "ServiceOM10.200.57.33", "定位信息": "区域=SH_CSVW，云服务=Storage，节点类型=mgt", "附加信息": "云服务=Storage，服务=mgt，本端地址=(10.200.215.162)，对端地址=(other_storage=10.200.191.53)", "可能原因": "未知", "metadata": {"filename": "*******8.2.5 ALM-36 磁盘占用率过高告警.md", "section_number": "*******8.2.5", "alarm_type": "ALM", "original_alarm_code": "ALM-36"}}, {"主题": "[重要告警]\"Database发生数据库进程异常\"", "告警ID": "38", "告警级别": "重要", "告警源": "Database", "来源系统": "ServiceOM10.200.171.136", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.208.239)，对端地址=(other_database=10.200.193.217)", "可能原因": "未知", "metadata": {"filename": "*******8.2.6 ALM-38 数据库进程异常.md", "section_number": "*******8.2.6", "alarm_type": "ALM", "original_alarm_code": "ALM-38"}}, {"主题": "[次要告警]\"Process发生服务内存占用过高告警\"", "告警ID": "47", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.42.94", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.46.116)，对端地址=(other_process=10.200.142.233)", "可能原因": "未知", "metadata": {"filename": "*******8.2.7 ALM-47 服务内存占用过高告警.md", "section_number": "*******8.2.7", "alarm_type": "ALM", "original_alarm_code": "ALM-47"}}, {"主题": "[重要告警]\"System发生SSH管理通道故障\"", "告警ID": "101206", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.236.126", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.61.157)，对端地址=(other_system=10.200.247.93)", "可能原因": "未知", "metadata": {"filename": "*******8.2.8 ALM-101206 SSH管理通道故障.md", "section_number": "*******8.2.8", "alarm_type": "ALM", "original_alarm_code": "ALM-101206"}}, {"主题": "[次要告警]\"Process发生部署面服务进程资源占用异常\"", "告警ID": "53080", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.150.85", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.64.67)，对端地址=(other_process=10.200.180.215)", "可能原因": "未知", "metadata": {"filename": "*******8.2.9 ALM-53080 部署面服务进程资源占用异常.md", "section_number": "*******8.2.9", "alarm_type": "ALM", "original_alarm_code": "ALM-53080"}}, {"主题": "[提示信息]\"安全管理\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.138.27", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "*******8.3 安全管理.md", "section_number": "*******8.3", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示告警]\"System发生证书即将过期\"", "告警ID": "51020", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.16.102", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.179.245)，对端地址=(other_system=10.200.90.165)", "可能原因": "未知", "metadata": {"filename": "*******8.3.1 ALM-51020 证书即将过期.md", "section_number": "*******8.3.1", "alarm_type": "ALM", "original_alarm_code": "ALM-51020"}}, {"主题": "[重要告警]\"System发生证书已经过期\"", "告警ID": "51021", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.71.197", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.104.178)，对端地址=(other_system=10.200.195.139)", "可能原因": "未知", "metadata": {"filename": "*******8.3.2 ALM-51021 证书已经过期.md", "section_number": "*******8.3.2", "alarm_type": "ALM", "original_alarm_code": "ALM-51021"}}, {"主题": "[提示告警]\"System发生证书更新失败\"", "告警ID": "51022", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.34.154", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.13.255)，对端地址=(other_system=10.200.3.240)", "可能原因": "未知", "metadata": {"filename": "*******8.3.3 ALM-51022 证书更新失败.md", "section_number": "*******8.3.3", "alarm_type": "ALM", "original_alarm_code": "ALM-51022"}}, {"主题": "[提示信息]\"参考信息\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.33.6", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "*******8.4 参考信息.md", "section_number": "*******8.4", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示信息]\"如何查找节点对应的IP地址\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.30.211", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "*******8.4.1 如何查找节点对应的IP地址.md", "section_number": "*******8.4.1", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示信息]\"IAM 告警参考\"", "告警ID": "", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ManageOne10.200.118.237", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=management", "附加信息": "云服务=Monitor，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "*******9 IAM 告警参考.md", "section_number": "*******9", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[严重告警]\"Database发生IAM数据库资源使用异常或服务异常\"", "告警ID": "36064531474581504", "告警级别": "严重", "告警源": "Database", "来源系统": "ServiceOM10.200.123.189", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(*************)，对端地址=(other_database=*************)", "可能原因": "未知", "metadata": {"filename": "*******9.1 ALM-36064531474581504 IAM数据库资源使用异常或服务异常.md", "section_number": "*******9.1", "alarm_type": "ALM", "original_alarm_code": "ALM-36064531474581504"}}, {"主题": "[提示告警]\"System发生IAM鉴权失败\"", "告警ID": "0004000700010009", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.148.74", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.43.110)，对端地址=(other_system=10.200.42.159)", "可能原因": "未知", "metadata": {"filename": "*******9.2 ALM-0004000700010009 IAM鉴权失败.md", "section_number": "*******9.2", "alarm_type": "ALM", "original_alarm_code": "ALM-0004000700010009"}}, {"主题": "[提示信息]\"驱动管理\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.40.22", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.4.2 驱动管理.md", "section_number": "5.2.4.2", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示告警]\"Monitor发生系统连通性检测失败告警\"", "告警ID": "100502", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.82.171", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.165.59)，对端地址=(other_monitor=10.200.220.87)", "可能原因": "未知", "metadata": {"filename": "5.2.4.2.1 ALM-100502 系统连通性检测失败告警.md", "section_number": "5.2.4.2.1", "alarm_type": "ALM", "original_alarm_code": "ALM-100502"}}, {"主题": "[严重告警]\"Monitor发生SNMP连通性检测失败告警\"", "告警ID": "100553", "告警级别": "严重", "告警源": "Monitor", "来源系统": "ServiceOM10.200.69.9", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.11.58)，对端地址=(other_monitor=10.200.29.222)", "可能原因": "未知", "metadata": {"filename": "5.2.4.2.2 ALM-100553 SNMP连通性检测失败告警.md", "section_number": "5.2.4.2.2", "alarm_type": "ALM", "original_alarm_code": "ALM-100553"}}, {"主题": "[提示信息]\"驱动框架\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.254.255", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.4.3 驱动框架.md", "section_number": "5.2.4.3", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[重要告警]\"Monitor发生LVS配置失败告警\"", "告警ID": "100550", "告警级别": "重要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.251.2", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.52.241)，对端地址=(other_monitor=10.200.106.254)", "可能原因": "未知", "metadata": {"filename": "5.2.4.3.1 ALM-100550 LVS配置失败告警.md", "section_number": "5.2.4.3.1", "alarm_type": "ALM", "original_alarm_code": "ALM-100550"}}, {"主题": "[重要告警]\"Process发生LVS中断服务告警\"", "告警ID": "100551", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.238.232", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.198.19)，对端地址=(other_process=10.200.5.9)", "可能原因": "未知", "metadata": {"filename": "5.2.4.3.2 ALM-100551 LVS中断服务告警.md", "section_number": "5.2.4.3.2", "alarm_type": "ALM", "original_alarm_code": "ALM-100551"}}, {"主题": "[提示信息]\"容量管理\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.134.223", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "******* 容量管理.md", "section_number": "*******", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示告警]\"System发生vCPU分配率超\"", "告警ID": "CloudCapacityMgmt_Base_1001", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.16.13", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.167.196)，对端地址=(other_system=10.200.150.50)", "可能原因": "未知", "metadata": {"filename": "*******.1 ALM-CloudCapacityMgmt_Base_1001 vCPU分配率超.md", "section_number": "*******.1", "alarm_type": "ALM", "original_alarm_code": "ALM-CloudCapacityMgmt_Base_1001"}}, {"主题": "[严重告警]\"Compute发生vMemory分\"", "告警ID": "CloudCapacityMgmt_Base_1002", "告警级别": "严重", "告警源": "Compute", "来源系统": "ServiceOM10.200.93.187", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.180.125)，对端地址=(other_compute=10.200.91.156)", "可能原因": "未知", "metadata": {"filename": "*******.2 ALM-CloudCapacityMgmt_Base_1002 vMemory分.md", "section_number": "*******.2", "alarm_type": "ALM", "original_alarm_code": "ALM-CloudCapacityMgmt_Base_1002"}}, {"主题": "[严重告警]\"Storage发生存储使用率超过阈\"", "告警ID": "CloudCapacityMgmt_Base_1003", "告警级别": "严重", "告警源": "Storage", "来源系统": "ServiceOM10.200.61.48", "定位信息": "区域=SH_CSVW，云服务=Storage，节点类型=mgt", "附加信息": "云服务=Storage，服务=mgt，本端地址=(10.200.102.253)，对端地址=(other_storage=10.200.16.149)", "可能原因": "未知", "metadata": {"filename": "*******.3 ALM-CloudCapacityMgmt_Base_1003 存储使用率超过阈.md", "section_number": "*******.3", "alarm_type": "ALM", "original_alarm_code": "ALM-CloudCapacityMgmt_Base_1003"}}, {"主题": "[严重告警]\"Storage发生存储分配率超过阈\"", "告警ID": "CloudCapacityMgmt_Base_1004", "告警级别": "严重", "告警源": "Storage", "来源系统": "ServiceOM10.200.126.194", "定位信息": "区域=SH_CSVW，云服务=Storage，节点类型=mgt", "附加信息": "云服务=Storage，服务=mgt，本端地址=(************)，对端地址=(other_storage=************)", "可能原因": "未知", "metadata": {"filename": "*******.4 ALM-CloudCapacityMgmt_Base_1004 存储分配率超过阈.md", "section_number": "*******.4", "alarm_type": "ALM", "original_alarm_code": "ALM-CloudCapacityMgmt_Base_1004"}}, {"主题": "[严重告警]\"System发生弹性IP使用率超\"", "告警ID": "CloudCapacityMgmt_Base_1005", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.78.126", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(**************)，对端地址=(other_system=**************)", "可能原因": "未知", "metadata": {"filename": "*******.5 ALM-CloudCapacityMgmt_Base_1005 弹性IP使用率超.md", "section_number": "*******.5", "alarm_type": "ALM", "original_alarm_code": "ALM-CloudCapacityMgmt_Base_1005"}}, {"主题": "[次要告警]\"Storage发生数据存储使用率超\"", "告警ID": "CloudCapacityMgmt_Base_1006", "告警级别": "次要", "告警源": "Storage", "来源系统": "ServiceOM10.200.249.201", "定位信息": "区域=SH_CSVW，云服务=Storage，节点类型=mgt", "附加信息": "云服务=Storage，服务=mgt，本端地址=(*************)，对端地址=(other_storage=**************)", "可能原因": "未知", "metadata": {"filename": "*******.6 ALM-CloudCapacityMgmt_Base_1006 数据存储使用率超.md", "section_number": "*******.6", "alarm_type": "ALM", "original_alarm_code": "ALM-CloudCapacityMgmt_Base_1006"}}, {"主题": "[提示信息]\"告警管理\"", "告警ID": "", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ManageOne10.200.80.154", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=management", "附加信息": "云服务=Monitor，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.4.5 告警管理.md", "section_number": "5.2.4.5", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示告警]\"Monitor发生当前告警数量达到阈值\"", "告警ID": "157", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.150.178", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.111.197)，对端地址=(other_monitor=10.200.164.245)", "可能原因": "未知", "metadata": {"filename": "5.2.4.5.1 ALM-157 当前告警数量达到阈值.md", "section_number": "5.2.4.5.1", "alarm_type": "ALM", "original_alarm_code": "ALM-157"}}, {"主题": "[提示告警]\"Monitor发生同类告警数量超出门限\"", "告警ID": "832", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.137.13", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.197.71)，对端地址=(other_monitor=10.200.236.16)", "可能原因": "未知", "metadata": {"filename": "5.2.4.5.2 ALM-832 同类告警数量超出门限.md", "section_number": "5.2.4.5.2", "alarm_type": "ALM", "original_alarm_code": "ALM-832"}}, {"主题": "[提示信息]\"安全管理\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.149.101", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.4.6 安全管理.md", "section_number": "5.2.4.6", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示告警]\"Network发生远端认证主服务器连接失败告警\"", "告警ID": "128", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.125.13", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.192.106)，对端地址=(other_network=10.200.16.110)", "可能原因": "未知", "metadata": {"filename": "5.2.4.6.1 ALM-128 远端认证主服务器连接失败告警.md", "section_number": "5.2.4.6.1", "alarm_type": "ALM", "original_alarm_code": "ALM-128"}}, {"主题": "[严重告警]\"Network发生远端认证备服务器连接失败告警\"", "告警ID": "160", "告警级别": "严重", "告警源": "Network", "来源系统": "ServiceOM10.200.7.81", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.16.100)，对端地址=(other_network=10.200.106.252)", "可能原因": "未知", "metadata": {"filename": "5.2.4.6.2 ALM-160 远端认证备服务器连接失败告警.md", "section_number": "5.2.4.6.2", "alarm_type": "ALM", "original_alarm_code": "ALM-160"}}, {"主题": "[严重告警]\"System发生用户密码即将过期\"", "告警ID": "30004", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.202.79", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.247.45)，对端地址=(other_system=10.200.25.134)", "可能原因": "未知", "metadata": {"filename": "5.2.4.6.3 ALM-30004 用户密码即将过期.md", "section_number": "5.2.4.6.3", "alarm_type": "ALM", "original_alarm_code": "ALM-30004"}}, {"主题": "[重要告警]\"System发生用户密码已过期\"", "告警ID": "30005", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.243.195", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.106.153)，对端地址=(other_system=10.200.128.200)", "可能原因": "未知", "metadata": {"filename": "5.2.4.6.4 ALM-30005 用户密码已过期.md", "section_number": "5.2.4.6.4", "alarm_type": "ALM", "original_alarm_code": "ALM-30005"}}, {"主题": "[提示告警]\"System发生登录尝试次数达到最大值\"", "告警ID": "505001106", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.48.173", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.59.114)，对端地址=(other_system=10.200.221.44)", "可能原因": "未知", "metadata": {"filename": "5.2.4.6.5 ALM-505001106 登录尝试次数达到最大值.md", "section_number": "5.2.4.6.5", "alarm_type": "ALM", "original_alarm_code": "ALM-505001106"}}, {"主题": "[提示信息]\"日志转发\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.134.171", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.4.7 日志转发.md", "section_number": "5.2.4.7", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示告警]\"Process发生切换到备系统日志服务器告警\"", "告警ID": "126", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.147.11", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.34.238)，对端地址=(other_process=10.200.218.183)", "可能原因": "未知", "metadata": {"filename": "5.2.4.7.1 ALM-126 切换到备系统日志服务器告警.md", "section_number": "5.2.4.7.1", "alarm_type": "ALM", "original_alarm_code": "ALM-126"}}, {"主题": "[严重告警]\"Network发生连接主、备系统日志服务器均失败告警\"", "告警ID": "127", "告警级别": "严重", "告警源": "Network", "来源系统": "ServiceOM10.200.52.50", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.59.189)，对端地址=(other_network=10.200.237.214)", "可能原因": "未知", "metadata": {"filename": "5.2.4.7.2 ALM-127 连接主、备系统日志服务器均失败告警.md", "section_number": "5.2.4.7.2", "alarm_type": "ALM", "original_alarm_code": "ALM-127"}}, {"主题": "[提示信息]\"License管理\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.242.221", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.4.8 License管理.md", "section_number": "5.2.4.8", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[次要告警]\"System发生License即将过期\"", "告警ID": "999999992", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.254.251", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.213.10)，对端地址=(other_system=10.200.95.71)", "可能原因": "未知", "metadata": {"filename": "5.2.4.8.1 ALM-999999992 License即将过期.md", "section_number": "5.2.4.8.1", "alarm_type": "ALM", "original_alarm_code": "ALM-999999992"}}, {"主题": "[提示告警]\"System发生License控制项或销售项进入宽限期\"", "告警ID": "999999999", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.83.174", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.146.124)，对端地址=(other_system=10.200.126.121)", "可能原因": "未知", "metadata": {"filename": "5.2.4.8.10 ALM-999999999 License控制项或销售项进入宽限期.md", "section_number": "5.2.4.8.10", "alarm_type": "ALM", "original_alarm_code": "ALM-999999999"}}, {"主题": "[重要告警]\"System发生License已经过期\"", "告警ID": "999999993", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.242.193", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.192.38)，对端地址=(other_system=10.200.54.75)", "可能原因": "未知", "metadata": {"filename": "5.2.4.8.2 ALM-999999993 License已经过期.md", "section_number": "5.2.4.8.2", "alarm_type": "ALM", "original_alarm_code": "ALM-999999993"}}, {"主题": "[重要告警]\"System发生License不合法\"", "告警ID": "999999995", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.175.96", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.212.75)，对端地址=(other_system=10.200.82.111)", "可能原因": "未知", "metadata": {"filename": "5.2.4.8.3 ALM-999999995 License不合法.md", "section_number": "5.2.4.8.3", "alarm_type": "ALM", "original_alarm_code": "ALM-999999995"}}, {"主题": "[重要告警]\"Process发生软件服务年费即将过期\"", "告警ID": "999999989", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.66.96", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.13.46)，对端地址=(other_process=10.200.241.116)", "可能原因": "未知", "metadata": {"filename": "5.2.4.8.4 ALM-999999989 软件服务年费即将过期.md", "section_number": "5.2.4.8.4", "alarm_type": "ALM", "original_alarm_code": "ALM-999999989"}}, {"主题": "[次要告警]\"Process发生软件服务年费已经过期\"", "告警ID": "999999990", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.210.190", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.45.80)，对端地址=(other_process=10.200.243.59)", "可能原因": "未知", "metadata": {"filename": "5.2.4.8.5 ALM-999999990 软件服务年费已经过期.md", "section_number": "5.2.4.8.5", "alarm_type": "ALM", "original_alarm_code": "ALM-999999990"}}, {"主题": "[提示告警]\"System发生License资源达到或超过阈值\"", "告警ID": "999999994", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.80.55", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.224.75)，对端地址=(other_system=10.200.145.71)", "可能原因": "未知", "metadata": {"filename": "5.2.4.8.6 ALM-999999994 License资源达到或超过阈值.md", "section_number": "5.2.4.8.6", "alarm_type": "ALM", "original_alarm_code": "ALM-999999994"}}, {"主题": "[重要告警]\"System发生License销售项达到或超过阈值\"", "告警ID": "999999996", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.175.162", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.70.161)，对端地址=(other_system=10.200.191.149)", "可能原因": "未知", "metadata": {"filename": "5.2.4.8.7 ALM-999999996 License销售项达到或超过阈值.md", "section_number": "5.2.4.8.7", "alarm_type": "ALM", "original_alarm_code": "ALM-999999996"}}, {"主题": "[提示告警]\"System发生License资源达到或超过100%阈值\"", "告警ID": "999999997", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.201.152", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.166.44)，对端地址=(other_system=10.200.235.253)", "可能原因": "未知", "metadata": {"filename": "5.2.4.8.8 ALM-999999997 License资源达到或超过100%阈值.md", "section_number": "5.2.4.8.8", "alarm_type": "ALM", "original_alarm_code": "ALM-999999997"}}, {"主题": "[重要告警]\"System发生License销售项达到或超过100%阈值\"", "告警ID": "999999998", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.79.217", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.64.136)，对端地址=(other_system=10.200.204.199)", "可能原因": "未知", "metadata": {"filename": "5.2.4.8.9 ALM-999999998 License销售项达到或超过100%阈值.md", "section_number": "5.2.4.8.9", "alarm_type": "ALM", "original_alarm_code": "ALM-999999998"}}, {"主题": "[提示信息]\"远程通知管理\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.245.187", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.4.9 远程通知管理.md", "section_number": "5.2.4.9", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示告警]\"System发生远程通知发送失败\"", "告警ID": "505001111", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.172.178", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.91.205)，对端地址=(other_system=10.200.117.47)", "可能原因": "未知", "metadata": {"filename": "5.2.4.9.1 ALM-505001111 远程通知发送失败.md", "section_number": "5.2.4.9.1", "alarm_type": "ALM", "original_alarm_code": "ALM-505001111"}}, {"主题": "[提示信息]\"组合API\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.25.87", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.6 组合API.md", "section_number": "5.2.6", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示信息]\"组合API\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.244.71", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.6.1 组合API.md", "section_number": "5.2.6.1", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示告警]\"Process发生组合API节点NTP进程状态异常\"", "告警ID": "1150001", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.177.138", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.45.163)，对端地址=(other_process=10.200.234.136)", "可能原因": "未知", "metadata": {"filename": "5.2.6.1.1 ALM-1150001 组合API节点NTP进程状态异常.md", "section_number": "5.2.6.1.1", "alarm_type": "ALM", "original_alarm_code": "ALM-1150001"}}, {"主题": "[次要告警]\"System发生组合API节点CPU使用率过高\"", "告警ID": "1150002", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.121.186", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.113.145)，对端地址=(other_system=10.200.227.195)", "可能原因": "未知", "metadata": {"filename": "5.2.6.1.2 ALM-1150002 组合API节点CPU使用率过高.md", "section_number": "5.2.6.1.2", "alarm_type": "ALM", "original_alarm_code": "ALM-1150002"}}, {"主题": "[提示告警]\"System发生组合API节点内存使用率过高\"", "告警ID": "1150003", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.155.207", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.246.57)，对端地址=(other_system=10.200.93.211)", "可能原因": "未知", "metadata": {"filename": "5.2.6.1.3 ALM-1150003 组合API节点内存使用率过高.md", "section_number": "5.2.6.1.3", "alarm_type": "ALM", "original_alarm_code": "ALM-1150003"}}, {"主题": "[严重告警]\"Process发生组合API节点tomcat进程异常\"", "告警ID": "1150004", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.87.66", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.125.8)，对端地址=(other_process=10.200.21.10)", "可能原因": "未知", "metadata": {"filename": "5.2.6.1.4 ALM-1150004 组合API节点tomcat进程异常.md", "section_number": "5.2.6.1.4", "alarm_type": "ALM", "original_alarm_code": "ALM-1150004"}}, {"主题": "[重要告警]\"Process发生组合API节点tomcat进程不存在\"", "告警ID": "1150017", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.194.19", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.67.252)，对端地址=(other_process=10.200.240.200)", "可能原因": "未知", "metadata": {"filename": "5.2.6.1.5 ALM-1150017 组合API节点tomcat进程不存在.md", "section_number": "5.2.6.1.5", "alarm_type": "ALM", "original_alarm_code": "ALM-1150017"}}, {"主题": "[重要告警]\"Process发生组合API节点tomcat存在多进程\"", "告警ID": "1150018", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.66.38", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.56.132)，对端地址=(other_process=10.200.44.179)", "可能原因": "未知", "metadata": {"filename": "5.2.6.1.6 ALM-1150018 组合API节点tomcat存在多进程.md", "section_number": "5.2.6.1.6", "alarm_type": "ALM", "original_alarm_code": "ALM-1150018"}}, {"主题": "[提示信息]\"云硬盘\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.159.210", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.6.2 云硬盘.md", "section_number": "5.2.6.2", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[重要告警]\"System发生evs周期性检测cinder连通性失败\"", "告警ID": "1060036", "告警级别": "重要", "告警源": "System", "来源系统": "SystemOM10.200.189.225", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.38.190)，错误代码=1060036", "可能原因": "未知", "metadata": {"filename": "5.2.6.2.1 1060036 evs周期性检测cinder连通性失败.md", "section_number": "5.2.6.2.1", "alarm_type": "NUM", "original_alarm_code": "1060036"}}, {"主题": "[提示信息]\"修改配置文件中对接其他组件或服务的帐户密码\"", "告警ID": "", "告警级别": "提示", "告警源": "Process", "来源系统": "ManageOne10.200.173.175", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=management", "附加信息": "云服务=Process，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.6.2.2 修改配置文件中对接其他组件或服务的帐户密码.md", "section_number": "5.2.6.2.2", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示信息]\"典型Cinder问题定位指导\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.63.154", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.6.2.3 典型Cinder问题定位指导.md", "section_number": "5.2.6.2.3", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示信息]\"排查FusionSphere OpenStack\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.244.44", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.6.2.3.1 排查FusionSphere OpenStack.md", "section_number": "5.2.6.2.3.1", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示信息]\"查询对应卷信息\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.52.15", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.6.2.3.1.1 查询对应卷信息.md", "section_number": "5.2.6.2.3.1.1", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示信息]\"查看cinder-api日志\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.111.205", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.6.2.3.1.2 查看cinder-api日志.md", "section_number": "5.2.6.2.3.1.2", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示信息]\"查看cinder-scheduler日志\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.240.245", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.6.2.3.1.3 查看cinder-scheduler日志.md", "section_number": "5.2.6.2.3.1.3", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示信息]\"查询cinder-volume日志\"", "告警ID": "", "告警级别": "提示", "告警源": "Storage", "来源系统": "ManageOne10.200.87.241", "定位信息": "区域=SH_CSVW，云服务=Storage，节点类型=management", "附加信息": "云服务=Storage，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.6.2.3.1.4 查询cinder-volume日志.md", "section_number": "5.2.6.2.3.1.4", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示信息]\"排查被级联层OpenStack\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.90.13", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.6.2.3.2 排查被级联层OpenStack.md", "section_number": "5.2.6.2.3.2", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示信息]\"查看被级联层cinder-api日志\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.178.133", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.6.2.3.2.1 查看被级联层cinder-api日志.md", "section_number": "5.2.6.2.3.2.1", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示信息]\"查询被级联层cinder-scheduler日志\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.74.30", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.6.2.3.2.2 查询被级联层cinder-scheduler日志.md", "section_number": "5.2.6.2.3.2.2", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示信息]\"查询被级联层cinder-volume日志\"", "告警ID": "", "告警级别": "提示", "告警源": "Storage", "来源系统": "ManageOne10.200.195.243", "定位信息": "区域=SH_CSVW，云服务=Storage，节点类型=management", "附加信息": "云服务=Storage，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.6.2.3.2.3 查询被级联层cinder-volume日志.md", "section_number": "5.2.6.2.3.2.3", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示信息]\"镜像服务\"", "告警ID": "", "告警级别": "提示", "告警源": "Process", "来源系统": "ManageOne10.200.57.186", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=management", "附加信息": "云服务=Process，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.6.3 镜像服务.md", "section_number": "5.2.6.3", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示信息]\"告警参考\"", "告警ID": "", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ManageOne10.200.175.211", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=management", "附加信息": "云服务=Monitor，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.6.3.1 告警参考.md", "section_number": "5.2.6.3.1", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[严重告警]\"Process发生ntp进程不存在\"", "告警ID": "1131007", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.162.220", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.74.62)，对端地址=(other_process=10.200.25.107)", "可能原因": "未知", "metadata": {"filename": "5.2.6.3.1.1 ALM-1131007 ntp进程不存在.md", "section_number": "5.2.6.3.1.1", "alarm_type": "ALM", "original_alarm_code": "ALM-1131007"}}, {"主题": "[提示告警]\"Process发生tomcat多进程\"", "告警ID": "1131009", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.141.118", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.132.70)，对端地址=(other_process=10.200.29.108)", "可能原因": "未知", "metadata": {"filename": "5.2.6.3.1.2 ALM-1131009 tomcat多进程.md", "section_number": "5.2.6.3.1.2", "alarm_type": "ALM", "original_alarm_code": "ALM-1131009"}}, {"主题": "[重要告警]\"Process发生tomcat进程不存在\"", "告警ID": "1131011", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.120.141", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.143.225)，对端地址=(other_process=10.200.245.4)", "可能原因": "未知", "metadata": {"filename": "5.2.6.3.1.3 ALM-1131011 tomcat进程不存在.md", "section_number": "5.2.6.3.1.3", "alarm_type": "ALM", "original_alarm_code": "ALM-1131011"}}, {"主题": "[严重告警]\"Process发生tomcat进程down掉\"", "告警ID": "1131012", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.43.93", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.145.155)，对端地址=(other_process=10.200.185.176)", "可能原因": "未知", "metadata": {"filename": "5.2.6.3.1.4 ALM-1131012 tomcat进程down掉.md", "section_number": "5.2.6.3.1.4", "alarm_type": "ALM", "original_alarm_code": "ALM-1131012"}}, {"主题": "[提示信息]\"弹性云服务器\"", "告警ID": "", "告警级别": "提示", "告警源": "Process", "来源系统": "ManageOne10.200.68.10", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=management", "附加信息": "云服务=Process，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.6.4 弹性云服务器.md", "section_number": "5.2.6.4", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示告警]\"Database发生连接ECS数据库失败\"", "告警ID": "1101308", "告警级别": "提示", "告警源": "Database", "来源系统": "ServiceOM10.200.89.60", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.31.248)，对端地址=(other_database=10.200.213.243)", "可能原因": "未知", "metadata": {"filename": "5.2.6.4.1 ALM-1101308 连接ECS数据库失败.md", "section_number": "5.2.6.4.1", "alarm_type": "ALM", "original_alarm_code": "ALM-1101308"}}, {"主题": "[次要告警]\"Network发生ECS连接FSP失败\"", "告警ID": "1101312", "告警级别": "次要", "告警源": "Network", "来源系统": "ServiceOM10.200.160.5", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.9.241)，对端地址=(other_network=10.200.186.88)", "可能原因": "未知", "metadata": {"filename": "5.2.6.4.2 ALM-1101312 ECS连接FSP失败.md", "section_number": "5.2.6.4.2", "alarm_type": "ALM", "original_alarm_code": "ALM-1101312"}}, {"主题": "[提示信息]\"登录FusionSphere OpenStack后台\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.128.171", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.6.4.3 登录FusionSphere OpenStack后台.md", "section_number": "5.2.6.4.3", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示信息]\"导入环境变量\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.238.131", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.6.4.4 导入环境变量.md", "section_number": "5.2.6.4.4", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示信息]\"OBS Console\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.70.25", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.8.2.1 OBS Console.md", "section_number": "5.2.8.2.1", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[重要告警]\"Process发生OBS Console 的tomcat进程异常\"", "告警ID": "600000007", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.24.129", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.98.92)，对端地址=(other_process=10.200.147.194)", "可能原因": "未知", "metadata": {"filename": "5.2.8.2.1.1 ALM-600000007 OBS Console 的tomcat进程异常.md", "section_number": "5.2.8.2.1.1", "alarm_type": "ALM", "original_alarm_code": "ALM-600000007"}}, {"主题": "[提示告警]\"System发生OBS Console 的tomcat端口未监听\"", "告警ID": "600000008", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.127.138", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.231.157)，对端地址=(other_system=10.200.7.206)", "可能原因": "未知", "metadata": {"filename": "5.2.8.2.1.2 ALM-600000008 OBS Console 的tomcat端口未监听.md", "section_number": "5.2.8.2.1.2", "alarm_type": "ALM", "original_alarm_code": "ALM-600000008"}}, {"主题": "[次要告警]\"System发生OBS Console tomcat证书异常\"", "告警ID": "600000100", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.63.220", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.227.248)，对端地址=(other_system=10.200.213.131)", "可能原因": "未知", "metadata": {"filename": "5.2.8.2.1.3 ALM-600000100 OBS Console tomcat证书异常.md", "section_number": "5.2.8.2.1.3", "alarm_type": "ALM", "original_alarm_code": "ALM-600000100"}}, {"主题": "[提示信息]\"OBS LVS\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.62.111", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.8.2.3 OBS LVS.md", "section_number": "5.2.8.2.3", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[次要告警]\"System发生OBS LVS节点存在未连通网口\"", "告警ID": "600000201", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.52.81", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.151.230)，对端地址=(other_system=10.200.26.84)", "可能原因": "未知", "metadata": {"filename": "5.2.8.2.3.1 ALM-600000201 OBS LVS节点存在未连通网口.md", "section_number": "5.2.8.2.3.1", "alarm_type": "ALM", "original_alarm_code": "ALM-600000201"}}, {"主题": "[重要告警]\"Process发生keepalived进程未启动\"", "告警ID": "600000200", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.185.244", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.159.41)，对端地址=(other_process=10.200.232.248)", "可能原因": "未知", "metadata": {"filename": "5.2.8.2.3.2 ALM-600000200 keepalived进程未启动.md", "section_number": "5.2.8.2.3.2", "alarm_type": "ALM", "original_alarm_code": "ALM-600000200"}}, {"主题": "[提示信息]\"虚拟私有云\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.238.148", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "5.2.9 虚拟私有云.md", "section_number": "5.2.9", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[严重告警]\"Process发生ntp进程故障\"", "告警ID": "1200025", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.225.161", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(************)，对端地址=(other_process=**************)", "可能原因": "未知", "metadata": {"filename": "******* ALM-1200025 ntp进程故障.md", "section_number": "*******", "alarm_type": "ALM", "original_alarm_code": "ALM-1200025"}}, {"主题": "[次要告警]\"System发生公网IP地址不足\"", "告警ID": "1200054", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.65.14", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(*************)，对端地址=(other_system=*************)", "可能原因": "未知", "metadata": {"filename": "*******0 ALM-1200054 公网IP地址不足.md", "section_number": "*******0", "alarm_type": "ALM", "original_alarm_code": "ALM-1200054"}}, {"主题": "[次要告警]\"Database发生VPC数据库访问失败\"", "告警ID": "1200074", "告警级别": "次要", "告警源": "Database", "来源系统": "ServiceOM10.200.168.216", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(*************)，对端地址=(other_database=************)", "可能原因": "未知", "metadata": {"filename": "******** ALM-1200074 VPC数据库访问失败.md", "section_number": "********", "alarm_type": "ALM", "original_alarm_code": "ALM-1200074"}}, {"主题": "[提示信息]\"附录\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.77.40", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "*******2 附录.md", "section_number": "*******2", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[提示信息]\"导入环境变量\"", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.120.21", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知", "metadata": {"filename": "*******2.1 导入环境变量.md", "section_number": "*******2.1", "alarm_type": "OTHER", "original_alarm_code": ""}}, {"主题": "[严重告警]\"Process发生tomcat进程CPU占用率超过阈值\"", "告警ID": "1200027", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.155.127", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.234.127)，对端地址=(other_process=10.200.180.137)", "可能原因": "未知", "metadata": {"filename": "5.2.9.2 ALM-1200027 tomcat进程CPU占用率超过阈值.md", "section_number": "5.2.9.2", "alarm_type": "ALM", "original_alarm_code": "ALM-1200027"}}, {"主题": "[提示告警]\"Process发生tomcat进程内存占用率超过阈值\"", "告警ID": "1200028", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.138.21", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.193.193)，对端地址=(other_process=10.200.114.122)", "可能原因": "未知", "metadata": {"filename": "5.2.9.3 ALM-1200028 tomcat进程内存占用率超过阈值.md", "section_number": "5.2.9.3", "alarm_type": "ALM", "original_alarm_code": "ALM-1200028"}}, {"主题": "[提示告警]\"Process发生tomcat进程无响应\"", "告警ID": "1200030", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.35.12", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.247.54)，对端地址=(other_process=10.200.217.141)", "可能原因": "未知", "metadata": {"filename": "5.2.9.4 ALM-1200030 tomcat进程无响应.md", "section_number": "5.2.9.4", "alarm_type": "ALM", "original_alarm_code": "ALM-1200030"}}, {"主题": "[次要告警]\"Process发生zookeeper进程CPU占用率超过阈值\"", "告警ID": "1200032", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.89.143", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.68.171)，对端地址=(other_process=10.200.196.83)", "可能原因": "未知", "metadata": {"filename": "5.2.9.5 ALM-1200032 zookeeper进程CPU占用率超过阈值.md", "section_number": "5.2.9.5", "alarm_type": "ALM", "original_alarm_code": "ALM-1200032"}}, {"主题": "[重要告警]\"Process发生zookeeper进程内存占用率超过阈值\"", "告警ID": "1200033", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.216.177", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.21.131)，对端地址=(other_process=10.200.215.33)", "可能原因": "未知", "metadata": {"filename": "5.2.9.6 ALM-1200033 zookeeper进程内存占用率超过阈值.md", "section_number": "5.2.9.6", "alarm_type": "ALM", "original_alarm_code": "ALM-1200033"}}, {"主题": "[严重告警]\"Process发生zookeeper进程重复运行\"", "告警ID": "1200034", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.54.194", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.230.43)，对端地址=(other_process=10.200.204.86)", "可能原因": "未知", "metadata": {"filename": "5.2.9.7 ALM-1200034 zookeeper进程重复运行.md", "section_number": "5.2.9.7", "alarm_type": "ALM", "original_alarm_code": "ALM-1200034"}}, {"主题": "[重要告警]\"Process发生zookeeper进程无响应\"", "告警ID": "1200035", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.135.201", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.248.250)，对端地址=(other_process=10.200.8.143)", "可能原因": "未知", "metadata": {"filename": "5.2.9.8 ALM-1200035 zookeeper进程无响应.md", "section_number": "5.2.9.8", "alarm_type": "ALM", "original_alarm_code": "ALM-1200035"}}, {"主题": "[提示告警]\"System发生Neutron接口调用失败\"", "告警ID": "1200053", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.117.77", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.128.77)，对端地址=(other_system=10.200.84.20)", "可能原因": "未知", "metadata": {"filename": "5.2.9.9 ALM-1200053 Neutron接口调用失败.md", "section_number": "5.2.9.9", "alarm_type": "ALM", "original_alarm_code": "ALM-1200053"}}]